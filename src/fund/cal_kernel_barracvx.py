# -*- coding: utf-8 -*-
# 凸优化方法计算超额收益对于Barra因子的收益归因, 需要保证收益率计算频率一致

import pandas as pd
import numpy as np
from cvxpy import Problem, Variable, Minimize

def cal_kernel_barracvx(df_raw, df_barraret, len = 100):
    """
    凸优化方法计算超额收益对于Barra因子的收益归因

    Parameters
    ----------
    df_raw : DataFrame - 基金净值序列, index为str日期'YYYYMMDD', columns为净值序列
    df_barraret : DataFrame - Barra因子收益收益

    Returns
    -------
    DataFrame
    """
    if df_raw.shape[1] != 1:
        raise Exception('Check DataFrame, columns number must be 1.')

    df_nav = df_raw.sort_index()
    col = df_nav.columns[0]

    df_join = df_nav.join((df_barraret + 1).cumprod(), how = 'inner').pct_change().dropna().tail(len)
    df_join['CONST'] = 1
    df_y = df_join[[col]].copy()
    df_x = df_join[df_barraret.drop('COUNTRY', axis = 1).columns.tolist() + ['CONST']].copy()    

    try:
        para = Variable([df_x.shape[1], 1])
        my = df_y.values
        mx = df_x.values
        constraints = [
            para[0: 31] >= -1,
            para[0: 31] <= 1,
            para[31: -1] <= 5,
            para[31: -1] >= -5,
            sum(para[0: 31]) == 1.0]
        prob = Problem(Minimize(sum((my - mx@para) ** 2)), constraints)
        prob.solve()

        coef = para.value
        resid = my - mx.dot(coef)
        diag = np.linalg.inv(mx.T.dot(mx)).diagonal()
        
        coef = coef.reshape(coef.shape[0])
        coef_std = np.sqrt(((resid ** 2).sum() / (mx.shape[0] - mx.shape[1]) * diag))
        t_value = coef / coef_std
        adj_r2 = 1 - (resid ** 2).sum() / ((my - my.mean()) ** 2).sum() * (mx.shape[0] - 1) / (mx.shape[0] - mx.shape[1])
        r2 = 1 - (resid ** 2).sum() / ((my - my.mean()) ** 2).sum()

        df_style = pd.DataFrame({'ITEM': df_x.columns, 'COEF': coef, 'COEF_STD': coef_std, 'T_VALUE': t_value, 'R2': r2, 'ADJ_R2': adj_r2, 'NUM_SAMPLE': df_y.shape[0], 'LASTDATE': df_y.index.max()})
    except:
        df_style = pd.DataFrame({'ITEM': df_x.columns, 'COEF': None, 'COEF_STD': None, 'T_VALUE': None, 'R2': None, 'ADJ_R2': None, 'NUM_SAMPLE': df_y.shape[0], 'LASTDATE': df_y.index.max()})
        
    return df_style

if __name__ == '__main__':
    pass