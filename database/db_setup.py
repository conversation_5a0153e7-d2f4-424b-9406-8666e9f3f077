# -*- coding: utf-8 -*-
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))

from src.basic import db_vulpes
import pandas as pd

conn_to = db_vulpes(writer = True)

def db_init(conn_to):
    """
    数据库初始化

    Parameters
    ----------
    conn_to : dbconnector - 目标数据库链接

    Returns
    -------
    """
    raw_conn = conn_to.raw_connection()
    cursor = raw_conn.cursor()
    try:
        ls_tables = pd.read_sql_query('SHOW TABLES', con = conn_to)['name'].tolist()
    except:
        raise SystemError('Database access failed.')

    try:
        # >>>> [BASIC_TRADEDATE]
        if checktable('BASIC_TRADEDATE', ls_tables):
            cursor.execute('DROP TABLE IF EXISTS BASIC_TRADEDATE;')
        sql = '''
        CREATE TABLE IF NOT EXISTS BASIC_TRADEDATE(
            CAL_DATE VARCHAR NOT NULL COMMENT '日历日期',
            PRE_TDATE VARCHAR COMMENT '前交易日期',
            FRQ_D INTEGER COMMENT '日频',
            FRQ_W INTEGER COMMENT '周频',
            FRQ_M INTEGER COMMENT '月频',
            FRQ_S INTEGER COMMENT '季频',
            FRQ_Y INTEGER COMMENT '年频',
            W_TDATE VARCHAR COMMENT '周交易日期',
            M_TDATE VARCHAR COMMENT '月交易日期',
            S_TDATE VARCHAR COMMENT '季交易日期',
            Y_TDATE VARCHAR COMMENT '年交易日期',
            TDNUM INTEGER COMMENT '交易日计数',
            TWNUM INTEGER COMMENT '交易周计数',
            CREATE_TIME TIMESTAMP MATERIALIZED now() COMMENT '创建时间',
            PRIMARY KEY (CAL_DATE)
        )
        ENGINE = ReplacingMergeTree()
        ORDER BY (CAL_DATE);
        '''
        cursor.execute(sql)

        # >>>> [INDEX_DAILYMARKET]
        if checktable('INDEX_DAILYMARKET', ls_tables):
            cursor.execute('DROP TABLE IF EXISTS INDEX_DAILYMARKET;')
        sql = '''
        CREATE TABLE IF NOT EXISTS INDEX_DAILYMARKET(
            INDEX_CODE VARCHAR NOT NULL COMMENT '指数代码',
            TRADEDATE VARCHAR NOT NULL COMMENT '交易日期',
            PRE_CLOSE DOUBLE NULL COMMENT '前收盘价',
            OPEN DOUBLE NULL COMMENT '开盘价',
            HIGH DOUBLE NULL COMMENT '最高价',
            LOW DOUBLE NULL COMMENT '最低价',
            CLOSE DOUBLE NULL COMMENT '收盘价',
            RETURN DOUBLE NULL COMMENT '涨跌幅',
            VOL DOUBLE NULL COMMENT '成交量(手)',
            AMOUNT DOUBLE NULL COMMENT '成交额(千元)',
            CREATE_TIME TIMESTAMP MATERIALIZED now() COMMENT '创建时间',
            PRIMARY KEY (INDEX_CODE, TRADEDATE)
        )
        ENGINE = ReplacingMergeTree()
        ORDER BY (INDEX_CODE, TRADEDATE);
        '''
        cursor.execute(sql)

        # >>>> [INDEX_CONSWEIGHT]
        if checktable('INDEX_CONSWEIGHT', ls_tables):
            cursor.execute('DROP TABLE IF EXISTS INDEX_CONSWEIGHT;')
        sql = '''
        CREATE TABLE IF NOT EXISTS INDEX_CONSWEIGHT(
            INDEX_CODE VARCHAR NOT NULL COMMENT '行业代码',
            STOCK_CODE VARCHAR NOT NULL COMMENT '股票代码',
            TRADEDATE VARCHAR NOT NULL COMMENT '交易日期',
            WEIGHT DOUBLE NOT NULL COMMENT '权重',
            CREATE_TIME TIMESTAMP MATERIALIZED now() COMMENT '创建时间',
            PRIMARY KEY (INDEX_CODE, STOCK_CODE, TRADEDATE)
        )
        ENGINE = ReplacingMergeTree()
        ORDER BY (INDEX_CODE, STOCK_CODE, TRADEDATE);
        '''
        cursor.execute(sql)

        # >>>> [INDEX_SWMEMBER]
        if checktable('INDEX_SWMEMBER', ls_tables):
            cursor.execute('DROP TABLE IF EXISTS INDEX_SWMEMBER;')
        sql = '''
        CREATE TABLE IF NOT EXISTS INDEX_SWMEMBER(
            INDEX_CODE VARCHAR NOT NULL COMMENT '行业代码',
            INDEX_NAME VARCHAR NOT NULL COMMENT '行业名称',
            STOCK_CODE VARCHAR NOT NULL COMMENT '股票代码',
            IN_DATE VARCHAR COMMENT '调入日期',
            OUT_DATE VARCHAR NULL COMMENT '调出日期',
            CREATE_TIME TIMESTAMP MATERIALIZED now() COMMENT '创建时间',
            PRIMARY KEY (INDEX_CODE, STOCK_CODE, IN_DATE)
        )
        ENGINE = ReplacingMergeTree()
        ORDER BY (INDEX_CODE, STOCK_CODE, IN_DATE);
        '''
        cursor.execute(sql)

        # >>>> [STOCK_DAILYMARKET]
        if checktable('STOCK_DAILYMARKET', ls_tables):
            cursor.execute('DROP TABLE IF EXISTS STOCK_DAILYMARKET;')
        sql = '''
        CREATE TABLE IF NOT EXISTS STOCK_DAILYMARKET(
            STOCK_CODE VARCHAR NOT NULL COMMENT '股票代码',
            TRADEDATE VARCHAR NOT NULL COMMENT '交易日期',
            OPEN DOUBLE NULL COMMENT '开盘价',
            HIGH DOUBLE NULL COMMENT '最高价',
            LOW DOUBLE NULL COMMENT '最低价',
            CLOSE DOUBLE NULL COMMENT '收盘价',
            PRE_CLOSE DOUBLE NULL COMMENT '前收盘价',
            CHANGE DOUBLE NULL COMMENT '涨跌额',
            RETURN DOUBLE NULL COMMENT '涨跌幅',
            VOL DOUBLE NULL COMMENT '成交量(手)',
            AMOUNT DOUBLE NULL COMMENT '成交额(千元)',
            ADJ_FACTOR DOUBLE NULL COMMENT '复权因子',
            CREATE_TIME TIMESTAMP MATERIALIZED now() COMMENT '创建时间',
            PRIMARY KEY (STOCK_CODE, TRADEDATE)
        )
        ENGINE = ReplacingMergeTree()
        ORDER BY (STOCK_CODE, TRADEDATE);
        '''
        cursor.execute(sql)

        # >>>> [STOCK_DAILYRETURN]
        if checktable('STOCK_DAILYRETURN', ls_tables):
            cursor.execute('DROP TABLE IF EXISTS STOCK_DAILYRETURN;')
        sql = '''
        CREATE TABLE IF NOT EXISTS STOCK_DAILYRETURN(
            STOCK_CODE VARCHAR NOT NULL COMMENT '股票代码',
            TRADEDATE VARCHAR NOT NULL COMMENT '交易日期',
            CLOSE DOUBLE NULL COMMENT '后复权收盘价',
            PRE_CLOSE DOUBLE NULL COMMENT '后复权前收盘价',
            RETURN DOUBLE NULL COMMENT '后复权涨跌幅',
            ADJ_FACTOR DOUBLE NULL COMMENT '复权因子',
            CREATE_TIME TIMESTAMP MATERIALIZED now() COMMENT '创建时间',
            PRIMARY KEY (STOCK_CODE, TRADEDATE)
        )
        ENGINE = ReplacingMergeTree()
        ORDER BY (STOCK_CODE, TRADEDATE);
        '''
        cursor.execute(sql)

        # >>>> [STOCK_DAILYWINRATIO]
        if checktable('STOCK_DAILYWINRATIO', ls_tables):
            cursor.execute('DROP TABLE IF EXISTS STOCK_DAILYWINRATIO;')
        sql = '''
        CREATE TABLE IF NOT EXISTS STOCK_DAILYWINRATIO(
            TRADEDATE VARCHAR NOT NULL COMMENT '交易日期',
            BENCHMARK VARCHAR NOT NULL COMMENT '对标基准指数',
            WINRATIO DOUBLE NOT NULL COMMENT '相对基准指数截面胜率',
            IN_WINRATIO DOUBLE NOT NULL COMMENT '相对基准指数成分内胜率',
            OUT_WINRATIO DOUBLE NOT NULL COMMENT '相对基准指数成分外胜率',
            IN_STD DOUBLE NOT NULL COMMENT '指数成分内截面波动率',
            OUT_STD DOUBLE NOT NULL COMMENT '指数成分外截面波动率',
            CREATE_TIME TIMESTAMP MATERIALIZED now() COMMENT '创建时间',
            PRIMARY KEY (TRADEDATE, BENCHMARK)
        )
        ENGINE = ReplacingMergeTree()
        ORDER BY (TRADEDATE, BENCHMARK);
        '''
        cursor.execute(sql)

        # >>>> [STOCK_DAILYINDICATOR]
        if checktable('STOCK_DAILYINDICATOR', ls_tables):
            cursor.execute('DROP TABLE IF EXISTS STOCK_DAILYINDICATOR;')
        sql = '''
        CREATE TABLE IF NOT EXISTS STOCK_DAILYINDICATOR(
            STOCK_CODE VARCHAR NOT NULL COMMENT '股票代码',
            TRADEDATE VARCHAR NOT NULL COMMENT '交易日期',
            MV DOUBLE NULL COMMENT '总市值(万元)',
            LMV DOUBLE NULL COMMENT '流通市值(万元)',
            PB DOUBLE NULL COMMENT '市净率',
            PE DOUBLE NULL COMMENT '市盈率, 亏损的PE为空',
            PE_TTM DOUBLE NULL COMMENT '市盈率, 亏损的PE为空(TTM)',
            PS DOUBLE NULL COMMENT '市销率',
            PS_TTM DOUBLE NULL COMMENT '市销率(TTM)',
            DV DOUBLE NULL COMMENT '股息率',
            DV_TTM DOUBLE NULL COMMENT '股息率(TTM)',
            TURNOVER DOUBLE NULL COMMENT '换手率',
            FTURNOVER DOUBLE NULL COMMENT '自由流通股换手率',
            SHARENUM DOUBLE NULL COMMENT '总股数(万股)',
            LSHARENUM DOUBLE NULL COMMENT '流通股数(万股)',
            FSHARENUM DOUBLE NULL COMMENT '自由流通股数(万股)',
            CREATE_TIME TIMESTAMP MATERIALIZED now() COMMENT '创建时间',
            PRIMARY KEY (STOCK_CODE, TRADEDATE)
        )
        ENGINE = ReplacingMergeTree()
        ORDER BY (STOCK_CODE, TRADEDATE);
        '''
        cursor.execute(sql)

        # >>>> [STOCK_YEARLYREPORT]
        if checktable('STOCK_YEARLYREPORT', ls_tables):
            cursor.execute('DROP TABLE IF EXISTS STOCK_YEARLYREPORT;')
        sql = '''
        CREATE TABLE IF NOT EXISTS STOCK_YEARLYREPORT(
            STOCK_CODE VARCHAR NOT NULL COMMENT '股票代码',
            REPORT_DATE VARCHAR NOT NULL COMMENT '财年日期',
            DISCLOSE_DATE VARCHAR NULL COMMENT '发布日期',
            TA DOUBLE NULL COMMENT '总资产',
            MC DOUBLE NULL COMMENT '现金资产',
            CR DOUBLE NULL COMMENT '现金及存放中央银行款项',
            TL DOUBLE NULL COMMENT '总负债',
            TLL DOUBLE NULL COMMENT '非流动性负债',
            TE DOUBLE NULL COMMENT '总权益',
            TS DOUBLE NULL COMMENT '总股本',
            PS DOUBLE NULL COMMENT '优先股',
            NP DOUBLE NULL COMMENT '净利润',
            NCO DOUBLE NULL COMMENT '经营活动净现金流',
            NCI DOUBLE NULL COMMENT '投资活动净现金流',
            NCE DOUBLE NULL COMMENT '现金及现金等价物净增加额',
            ECE DOUBLE NULL COMMENT '期末现金及现金等价物余额',
            TOR DOUBLE NULL COMMENT '营业总收入',
            TOC DOUBLE NULL COMMENT '营业总成本',
            TP DOUBLE NULL COMMENT '总利润',
            NI DOUBLE NULL COMMENT '净利润',
            EBIT DOUBLE NULL COMMENT '息税前利润',
            CREATE_TIME TIMESTAMP MATERIALIZED now() COMMENT '创建时间',
            PRIMARY KEY (STOCK_CODE, REPORT_DATE)
        )
        ENGINE = ReplacingMergeTree()
        ORDER BY (STOCK_CODE, REPORT_DATE);
        '''
        cursor.execute(sql)
        
        # >>>> [CFUTURE_DAILYMARKET]
        if checktable('CFUTURE_DAILYMARKET', ls_tables):
            cursor.execute('DROP TABLE IF EXISTS CFUTURE_DAILYMARKET;')
        sql = '''
        CREATE TABLE IF NOT EXISTS CFUTURE_DAILYMARKET(
            FUT_CODE VARCHAR NOT NULL COMMENT '期货合约代码',
            TRADEDATE VARCHAR NOT NULL COMMENT '交易日期',
            STDCODE VARCHAR NOT NULL COMMENT '合约品种代码',
            EXCHANGE VARCHAR NOT NULL COMMENT '交易所',
            PRE_CLOSE DOUBLE NULL COMMENT '前收盘价',
            PRE_SETTLE DOUBLE NULL COMMENT '前结算价',
            OPEN DOUBLE NULL COMMENT '开盘价',
            HIGH DOUBLE NULL COMMENT '最高价',
            LOW DOUBLE NULL COMMENT '最低价',
            CLOSE DOUBLE NULL COMMENT '收盘价',
            SETTLE DOUBLE NULL COMMENT '结算价',
            VOL DOUBLE NULL COMMENT '成交量(手)',
            AMOUNT DOUBLE NULL COMMENT '成交额(万元)',
            OI DOUBLE NULL COMMENT '持仓量(手)',
            OI_CHG DOUBLE NULL COMMENT '持仓变化量(手)',
            LISTDATE VARCHAR COMMENT '上市交易日期',
            DLISTDATE VARCHAR COMMENT '最后交易日期',
            CREATE_TIME TIMESTAMP MATERIALIZED now() COMMENT '创建时间',
            PRIMARY KEY (FUT_CODE, TRADEDATE)
        )
        ENGINE = ReplacingMergeTree()
        ORDER BY (FUT_CODE, TRADEDATE);
        '''
        cursor.execute(sql)

        # >>>> [CFUTURE_INFO]
        if checktable('CFUTURE_INFO', ls_tables):
            cursor.execute('DROP TABLE IF EXISTS CFUTURE_INFO;')
        sql = '''
        CREATE TABLE IF NOT EXISTS CFUTURE_INFO(
            STDCODE VARCHAR NOT NULL COMMENT '合约品种代码',
            STDNAME VARCHAR NOT NULL COMMENT '合约品种名称',
            UNIT FLOAT NOT NULL COMMENT '每手交易单位',
            EXCHANGE_NAME VARCHAR NOT NULL COMMENT '交易所名称',
            TYPE_L1 VARCHAR NOT NULL COMMENT '品种分类L1',
            TYPE_L2 VARCHAR NOT NULL COMMENT '品种分类L2',
            CREATE_TIME TIMESTAMP MATERIALIZED now() COMMENT '创建时间',
            PRIMARY KEY (STDCODE)
        )
        ENGINE = ReplacingMergeTree()
        ORDER BY (STDCODE);
        '''
        cursor.execute(sql)

        # >>>> [CFUTURE_MAINCODE]
        if checktable('CFUTURE_MAINCODE', ls_tables):
            cursor.execute('DROP TABLE IF EXISTS CFUTURE_MAINCODE;')
        sql = '''
        CREATE TABLE IF NOT EXISTS CFUTURE_MAINCODE(
            FUT_CODE VARCHAR NOT NULL COMMENT '期货合约代码',
            TRADEDATE VARCHAR NOT NULL COMMENT '交易日期',
            STDCODE VARCHAR NOT NULL COMMENT '合约品种代码',
            MTYPE FLOAT NOT NULL COMMENT '主力合约类型: 1-主力合约, 2-次主力合约',
            CREATE_TIME TIMESTAMP MATERIALIZED now() COMMENT '创建时间',
            PRIMARY KEY (FUT_CODE, TRADEDATE)
        )
        ENGINE = ReplacingMergeTree()
        ORDER BY (FUT_CODE, TRADEDATE);
        '''
        cursor.execute(sql)

        # >>>> [FFUTURE_INFO]
        if checktable('FFUTURE_INFO', ls_tables):
            cursor.execute('DROP TABLE IF EXISTS FFUTURE_INFO;')
        sql = '''
        CREATE TABLE IF NOT EXISTS FFUTURE_INFO(
            STDCODE VARCHAR NOT NULL COMMENT '合约品种代码',
            TARGET_CODE VARCHAR NULL COMMENT '标的资产代码',
            STDNAME VARCHAR NOT NULL COMMENT '合约品种名称',
            UNIT FLOAT NOT NULL COMMENT '每点对应单位',
            EXCHANGE_NAME VARCHAR NOT NULL COMMENT '交易所名称',
            TYPE_L1 VARCHAR NOT NULL COMMENT '品种分类L1',
            TYPE_L2 VARCHAR NOT NULL COMMENT '品种分类L2',
            CREATE_TIME TIMESTAMP MATERIALIZED now() COMMENT '创建时间',
            PRIMARY KEY (STDCODE)
        )
        ENGINE = ReplacingMergeTree()
        ORDER BY (STDCODE);
        '''
        cursor.execute(sql)

        # >>>> [FFUTURE_DAILYMARKET]
        if checktable('FFUTURE_DAILYMARKET', ls_tables):
            cursor.execute('DROP TABLE IF EXISTS FFUTURE_DAILYMARKET;')
        sql = '''
        CREATE TABLE IF NOT EXISTS FFUTURE_DAILYMARKET(
            FUT_CODE VARCHAR NOT NULL COMMENT '期货合约代码',
            TRADEDATE VARCHAR NOT NULL COMMENT '交易日期',
            STDCODE VARCHAR NOT NULL COMMENT '合约品种代码',
            PRE_CLOSE DOUBLE NULL COMMENT '前收盘价',
            PRE_SETTLE DOUBLE NULL COMMENT '前结算价',
            OPEN DOUBLE NULL COMMENT '开盘价',
            HIGH DOUBLE NULL COMMENT '最高价',
            LOW DOUBLE NULL COMMENT '最低价',
            CLOSE DOUBLE NULL COMMENT '收盘价',
            SETTLE DOUBLE NULL COMMENT '结算价',
            VOL DOUBLE NULL COMMENT '成交量(手)',
            AMOUNT DOUBLE NULL COMMENT '成交额(万元)',
            OI DOUBLE NULL COMMENT '持仓量(手)',
            OI_CHG DOUBLE NULL COMMENT '持仓变化量(手)',
            MTYPE FLOAT COMMENT '合约类型',
            LISTDATE VARCHAR COMMENT '上市交易日期',
            DLISTDATE VARCHAR COMMENT '最后交易日期',
            CREATE_TIME TIMESTAMP MATERIALIZED now() COMMENT '创建时间',
            PRIMARY KEY (FUT_CODE, TRADEDATE)
        )
        ENGINE = ReplacingMergeTree()
        ORDER BY (FUT_CODE, TRADEDATE);
        '''
        cursor.execute(sql)

        # >>>> [CBOND_DAILYMARKET]
        if checktable('CBOND_DAILYMARKET', ls_tables):
            cursor.execute('DROP TABLE IF EXISTS CBOND_DAILYMARKET;')
        sql = '''
        CREATE TABLE IF NOT EXISTS CBOND_DAILYMARKET(
            CBOND_CODE VARCHAR NOT NULL COMMENT '转债代码',
            TRADEDATE VARCHAR NOT NULL COMMENT '交易日期',
            PRE_CLOSE DOUBLE NULL COMMENT '前收盘价',
            CLOSE DOUBLE NULL COMMENT '收盘价',
            OPEN DOUBLE NULL COMMENT '开盘价',
            HIGH DOUBLE NULL COMMENT '最高价',
            LOW DOUBLE NULL COMMENT '最低价',
            RETURN DOUBLE NULL COMMENT '涨跌幅',
            VOL DOUBLE NULL COMMENT '成交量(手,交易所1手=10张,银行间1手=100张)',
            AMOUNT DOUBLE NULL COMMENT '成交额(千元)',
            BD_VALUE DOUBLE NULL COMMENT '纯债价值',
            BD_PRATE DOUBLE NULL COMMENT '纯债溢价率 (转债价格-纯债价值)/纯债价值*100%',
            CB_VALUE DOUBLE NULL COMMENT '转股价值',
            CB_PRATE DOUBLE NULL COMMENT '转股溢价率 (转债价格-转股价值)/转股价值',
            CREATE_TIME TIMESTAMP MATERIALIZED now() COMMENT '创建时间',
            PRIMARY KEY (CBOND_CODE, TRADEDATE)
        )
        ENGINE = ReplacingMergeTree()
        ORDER BY (CBOND_CODE, TRADEDATE);
        '''
        cursor.execute(sql)

        # >>>> [CBOND_INFO]
        if checktable('CBOND_INFO', ls_tables):
            cursor.execute('DROP TABLE IF EXISTS CBOND_INFO;')
        sql = '''
        CREATE TABLE IF NOT EXISTS CBOND_INFO(
            CBOND_CODE VARCHAR NOT NULL COMMENT '转债代码',
            CBOND_NAME VARCHAR NOT NULL COMMENT '转债简称',
            STOCK_CODE VARCHAR NOT NULL COMMENT '正股代码',
            ISSUE_SIZE DOUBLE NOT NULL COMMENT '发行总额(元)',
            LISTDATE VARCHAR NULL COMMENT '上市日期',
            MATURITY DOUBLE NOT NULL COMMENT '发行期限(年)',
            MATURITY_DATE VARCHAR NULL COMMENT '到期日期',
            ISSUE_RATING VARCHAR NULL COMMENT '发行评级',
            CREATE_TIME TIMESTAMP MATERIALIZED now() COMMENT '创建时间',
            PRIMARY KEY (CBOND_CODE)
        )
        ENGINE = ReplacingMergeTree()
        ORDER BY (CBOND_CODE);
        '''
        cursor.execute(sql)

        # >>>> [FOPTION_ETFMARKET]
        if checktable('FOPTION_ETFMARKET', ls_tables):
            cursor.execute('DROP TABLE IF EXISTS FOPTION_ETFMARKET;')
        sql = '''
        CREATE TABLE IF NOT EXISTS FOPTION_ETFMARKET(
            FUND_CODE VARCHAR NOT NULL COMMENT 'ETF场内代码',
            FUND_NAME VARCHAR NOT NULL COMMENT 'ETF基金简称',
            EXCHANGE VARCHAR NOT NULL COMMENT '交易所',
            TRADEDATE VARCHAR NOT NULL COMMENT '交易日期',
            PRE_CLOSE DOUBLE NULL COMMENT '前收盘价',
            CLOSE DOUBLE NULL COMMENT '收盘价',
            OPEN DOUBLE NULL COMMENT '开盘价',
            HIGH DOUBLE NULL COMMENT '最高价',
            LOW DOUBLE NULL COMMENT '最低价',
            RETURN DOUBLE NULL COMMENT '涨跌幅',
            VOL DOUBLE NULL COMMENT '成交量(手)',
            AMOUNT DOUBLE NULL COMMENT '成交额(千元)',
            CREATE_TIME TIMESTAMP MATERIALIZED now() COMMENT '创建时间',
            PRIMARY KEY (FUND_CODE, TRADEDATE)
        )
        ENGINE = ReplacingMergeTree()
        ORDER BY (FUND_CODE, TRADEDATE);
        '''
        cursor.execute(sql)

        # >>>> [FOPTION_DAILYMARKET]
        if checktable('FOPTION_DAILYMARKET', ls_tables):
            cursor.execute('DROP TABLE IF EXISTS FOPTION_DAILYMARKET;')
        sql = '''
        CREATE TABLE IF NOT EXISTS FOPTION_DAILYMARKET(
            OPT_CODE VARCHAR NOT NULL COMMENT '期权合约代码',
            TRADEDATE VARCHAR NOT NULL COMMENT '交易日期',
            OPT_NAME VARCHAR NOT NULL COMMENT '期权合约名称',
            EXCHANGE VARCHAR NOT NULL COMMENT '交易所',
            TARGET_CODE VARCHAR NOT NULL COMMENT '标的资产代码',
            FLAG VARCHAR NOT NULL COMMENT '期权合约方向',
            STRIKE_PRICE DOUBLE NOT NULL COMMENT '行权价格',
            PRE_CLOSE DOUBLE NULL COMMENT '前收盘价',
            PRE_SETTLE DOUBLE NULL COMMENT '前结算价',
            OPEN DOUBLE NULL COMMENT '开盘价',
            HIGH DOUBLE NULL COMMENT '最高价',
            LOW DOUBLE NULL COMMENT '最低价',
            CLOSE DOUBLE NULL COMMENT '收盘价',
            SETTLE DOUBLE NULL COMMENT '结算价',
            VOL DOUBLE NULL COMMENT '成交量(手)',
            AMOUNT DOUBLE NULL COMMENT '成交额(万元)',
            OI DOUBLE NULL COMMENT '持仓量(手)',
            LISTDATE VARCHAR COMMENT '上市交易日期',
            DLISTDATE VARCHAR COMMENT '最后交易日期',
            TMONTH VARCHAR COMMENT '合约月份',
            OP_TIME DOUBLE NULL COMMENT '合约剩余时间(年)',
            CREATE_TIME TIMESTAMP MATERIALIZED now() COMMENT '创建时间',
            PRIMARY KEY (OPT_CODE, TRADEDATE)
        )
        ENGINE = ReplacingMergeTree()
        ORDER BY (OPT_CODE, TRADEDATE);
        '''
        cursor.execute(sql)

        # >>>> [FOPTION_DAILYGREEKS]
        if checktable('FOPTION_DAILYGREEKS', ls_tables):
            cursor.execute('DROP TABLE IF EXISTS FOPTION_DAILYGREEKS;')
        sql = '''
        CREATE TABLE IF NOT EXISTS FOPTION_DAILYGREEKS(
            OPT_CODE VARCHAR NOT NULL COMMENT '期权合约代码',
            TRADEDATE VARCHAR NOT NULL COMMENT '交易日期',
            EXCHANGE VARCHAR NOT NULL COMMENT '交易所',
            TARGET_CODE VARCHAR NOT NULL COMMENT '标的资产代码',
            FLAG VARCHAR NOT NULL COMMENT '期权合约方向',
            STRIKE_PRICE DOUBLE NOT NULL COMMENT '行权价格',
            TARGET_PRICE DOUBLE NULL COMMENT '标的资产价格',
            OPTION_PRICE DOUBLE NULL COMMENT '期权价格',
            HFUT_PRICE DOUBLE NULL COMMENT '合成期货价格',
            MHFUT_PRICE DOUBLE NULL COMMENT '平均合成期货价格',
            TMONTH VARCHAR COMMENT '合约月份',
            OP_TIME DOUBLE NULL COMMENT '合约剩余时间(年)',
            IV DOUBLE NULL COMMENT '隐含波动率(基于平均合成期货价格)',
            DELTA DOUBLE NULL COMMENT '标的资产价格对期权价格的影响(基于平均合成期货价格)',
            GAMMA DOUBLE NULL COMMENT '标的资产价格对期权DELTA的影响(基于平均合成期货价格)',
            VEGA DOUBLE NULL COMMENT '标的波动率对期权价格的影响(基于平均合成期货价格)',
            VANNA DOUBLE NULL COMMENT '标的资产价格对VEGA的影响(基于平均合成期货价格)',
            CREATE_TIME TIMESTAMP MATERIALIZED now() COMMENT '创建时间',
            PRIMARY KEY (OPT_CODE, TRADEDATE)
        )
        ENGINE = ReplacingMergeTree()
        ORDER BY (OPT_CODE, TRADEDATE);
        '''
        cursor.execute(sql)

        # >>>> [FOPTION_DAILYSKEW]
        if checktable('FOPTION_DAILYSKEW', ls_tables):
            cursor.execute('DROP TABLE IF EXISTS FOPTION_DAILYSKEW;')
        sql = '''
        CREATE TABLE IF NOT EXISTS FOPTION_DAILYSKEW(
            TRADEDATE VARCHAR NOT NULL COMMENT '交易日期',
            TARGET_CODE VARCHAR NOT NULL COMMENT '标的资产代码',
            TMONTH VARCHAR COMMENT '合约月份',
            MIV DOUBLE NULL COMMENT '平值期权隐含波动率',
            CIV DOUBLE NULL COMMENT '0.25_DELTA期权隐含波动率',
            PIV DOUBLE NULL COMMENT '-0.25_DELTA期权隐含波动率',
            SKEW DOUBLE NULL COMMENT '市场偏度',
            CSKEW DOUBLE NULL COMMENT 'CALL偏度',
            PSKEW DOUBLE NULL COMMENT 'PUT偏度',
            MTYPE DOUBLE NOT NULL COMMENT '合约类型',
            CREATE_TIME TIMESTAMP MATERIALIZED now() COMMENT '创建时间',
            PRIMARY KEY (TARGET_CODE, TRADEDATE, TMONTH)
        )
        ENGINE = ReplacingMergeTree()
        ORDER BY (TARGET_CODE, TRADEDATE, TMONTH);
        '''
        cursor.execute(sql)

        # >>>> [FOPTION_VOLATILITYCONE]
        if checktable('FOPTION_VOLATILITYCONE', ls_tables):
            cursor.execute('DROP TABLE IF EXISTS FOPTION_VOLATILITYCONE;')
        sql = '''
        CREATE TABLE IF NOT EXISTS FOPTION_VOLATILITYCONE(
            TARGET_CODE VARCHAR NOT NULL COMMENT '标的资产代码',
            QUANTILE DOUBLE NOT NULL COMMENT '截面分位数',
            TERM DOUBLE NOT NULL COMMENT '波动率计算期限',
            HVOL DOUBLE NOT NULL COMMENT '标的资产历史波动率',
            CREATE_TIME TIMESTAMP MATERIALIZED now() COMMENT '创建时间',
            PRIMARY KEY (TARGET_CODE, QUANTILE, TERM)
        )
        ENGINE = ReplacingMergeTree()
        ORDER BY (TARGET_CODE, QUANTILE, TERM);
        '''
        cursor.execute(sql)

        # >>>> [FACTOR_STGRETURN]
        if checktable('FACTOR_STGRETURN', ls_tables):
            cursor.execute('DROP TABLE IF EXISTS FACTOR_STGRETURN;')
        sql = '''
        CREATE TABLE IF NOT EXISTS FACTOR_STGRETURN(
            FACTOR_ID VARCHAR NOT NULL COMMENT '因子ID',
            TRADEDATE VARCHAR NOT NULL COMMENT '交易日期',
            RETURN DOUBLE NOT NULL COMMENT '因子收益率',
            CREATE_TIME TIMESTAMP MATERIALIZED now() COMMENT '创建时间',
            PRIMARY KEY (FACTOR_ID, TRADEDATE)
        )
        ENGINE = ReplacingMergeTree()
        ORDER BY (FACTOR_ID, TRADEDATE);
        '''
        cursor.execute(sql)

        # >>>> [FACTOR_STGHOLD]
        if checktable('FACTOR_STGHOLD', ls_tables):
            cursor.execute('DROP TABLE IF EXISTS FACTOR_STGHOLD;')
        sql = '''
        CREATE TABLE IF NOT EXISTS FACTOR_STGHOLD(
            FACTOR_ID VARCHAR NOT NULL COMMENT '因子ID',
            HOLD_CODE VARCHAR NOT NULL COMMENT '持仓标的代码',
            STDCODE VARCHAR NOT NULL COMMENT '持仓标的类型代码',
            TRADEDATE VARCHAR NOT NULL COMMENT '交易日期',
            RESETDATE VARCHAR NOT NULL COMMENT '换仓日期',
            HOLD_WEIGHT DOUBLE NOT NULL COMMENT '持仓标的权重',
            HOLD_RETURN DOUBLE NOT NULL COMMENT '持仓标的贡献收益率',
            CREATE_TIME TIMESTAMP MATERIALIZED now() COMMENT '创建时间',
            PRIMARY KEY (FACTOR_ID, HOLD_CODE, TRADEDATE)
        )
        ENGINE = ReplacingMergeTree()
        ORDER BY (FACTOR_ID, HOLD_CODE, TRADEDATE);
        '''
        cursor.execute(sql)
       
        # >>>> [BARRA_FACTORLOAD_L1]
        if checktable('BARRA_FACTORLOAD_L1', ls_tables):
            cursor.execute('DROP TABLE IF EXISTS BARRA_FACTORLOAD_L1;')
        sql = '''
        CREATE TABLE IF NOT EXISTS BARRA_FACTORLOAD_L1(
            STOCK_CODE VARCHAR NOT NULL COMMENT '股票代码',
            TRADEDATE VARCHAR NOT NULL COMMENT '交易日期',
            FACTOR_ID VARCHAR NOT NULL COMMENT '因子名称',
            LOAD DOUBLE NOT NULL COMMENT '因子载荷',
            ISRAW INTEGER NOT NULL COMMENT '是否为原始因子, 0为子因子包括行业中位数填充',
            CREATE_TIME TIMESTAMP MATERIALIZED now() COMMENT '创建时间',
            PRIMARY KEY (STOCK_CODE, TRADEDATE, FACTOR_ID)
        )
        ENGINE = ReplacingMergeTree()
        ORDER BY (STOCK_CODE, TRADEDATE, FACTOR_ID);
        '''
        cursor.execute(sql)

        # >>>> [BARRA_FACTORLOAD_L2]
        if checktable('BARRA_FACTORLOAD_L2', ls_tables):
            cursor.execute('DROP TABLE IF EXISTS BARRA_FACTORLOAD_L2;')
        sql = '''
        CREATE TABLE IF NOT EXISTS BARRA_FACTORLOAD_L2(
            STOCK_CODE VARCHAR NOT NULL COMMENT '股票代码',
            TRADEDATE VARCHAR NOT NULL COMMENT '交易日期',
            FACTOR_ID VARCHAR NOT NULL COMMENT '因子名称',
            LOAD DOUBLE NOT NULL COMMENT '因子载荷',
            ISRAW INTEGER NOT NULL COMMENT '是否为原始因子, 0为行业中位数填充',
            CREATE_TIME TIMESTAMP MATERIALIZED now() COMMENT '创建时间',
            PRIMARY KEY (STOCK_CODE, TRADEDATE, FACTOR_ID)
        )
        ENGINE = ReplacingMergeTree()
        ORDER BY (STOCK_CODE, TRADEDATE, FACTOR_ID);
        '''
        cursor.execute(sql)

        # >>>> [BARRA_DAILYRETURN]
        if checktable('BARRA_DAILYRETURN', ls_tables):
            cursor.execute('DROP TABLE IF EXISTS BARRA_DAILYRETURN;')
        sql = '''
        CREATE TABLE IF NOT EXISTS BARRA_DAILYRETURN(
            TRADEDATE VARCHAR NOT NULL COMMENT '交易日期',
            FACTOR_ID VARCHAR NOT NULL COMMENT '因子名称',
            RETURN DOUBLE NOT NULL COMMENT '因子收益率',
            MODEL VARCHAR NOT NULL COMMENT '计算模型CNE5/CNE6',
            CREATE_TIME TIMESTAMP MATERIALIZED now() COMMENT '创建时间',
            PRIMARY KEY (TRADEDATE, FACTOR_ID, MODEL)
        )
        ENGINE = ReplacingMergeTree()
        ORDER BY (TRADEDATE, FACTOR_ID, MODEL);
        '''
        cursor.execute(sql)

        # >>>> [FUND_RAWNAV]
        if checktable('FUND_RAWNAV', ls_tables):
            cursor.execute('DROP TABLE IF EXISTS FUND_RAWNAV;')
        sql = '''
        CREATE TABLE IF NOT EXISTS FUND_RAWNAV(
            TRADEDATE VARCHAR NOT NULL COMMENT '净值日期',
            FUND_NAME VARCHAR NOT NULL COMMENT '基金简称',
            FUND_NAV DOUBLE NOT NULL COMMENT '基金复权净值',
            COMPANY_NAME VARCHAR NOT NULL COMMENT '管理人简称',
            COMPANY_SIZE VARCHAR NULL COMMENT '管理人规模',
            STRATEGY VARCHAR NOT NULL COMMENT '基金策略',
            BENCHMARK_CODE VARCHAR NOT NULL COMMENT '业绩基准指数',
            CREATE_TIME TIMESTAMP MATERIALIZED now() COMMENT '创建时间',
            PRIMARY KEY (TRADEDATE, FUND_NAME)
        )
        ENGINE = ReplacingMergeTree()
        ORDER BY (TRADEDATE, FUND_NAME);
        '''
        cursor.execute(sql)

        # >>>> [FUND_WEEKLYMARKET]
        if checktable('FUND_WEEKLYMARKET', ls_tables):
            cursor.execute('DROP TABLE IF EXISTS FUND_WEEKLYMARKET;')
        sql = '''
        CREATE TABLE IF NOT EXISTS FUND_WEEKLYMARKET(
            TRADEDATE VARCHAR NOT NULL COMMENT '净值日期',
            FUND_NAME VARCHAR NOT NULL COMMENT '基金简称',
            STRATEGY VARCHAR NOT NULL COMMENT '基金策略',
            BENCHMARK_CODE VARCHAR NOT NULL COMMENT '业绩基准指数',
            COMPANY_NAME VARCHAR NOT NULL COMMENT '管理人简称',
            COMPANY_SIZE VARCHAR NULL COMMENT '管理人规模',
            FUND_NAV DOUBLE NOT NULL COMMENT '基金复权净值',
            FUND_RETURN DOUBLE NULL COMMENT '基金收益率',
            BENCHMARK_NAV DOUBLE NOT NULL COMMENT '业绩基准净值',
            BENCHMARK_RETURN DOUBLE NULL COMMENT '业绩基准收益率',
            EXCESSIVE_NAV DOUBLE NOT NULL COMMENT '超额收益净值',
            EXCESSIVE_RETURN DOUBLE NULL COMMENT '超额收益率',
            CREATE_TIME TIMESTAMP MATERIALIZED now() COMMENT '创建时间',
            PRIMARY KEY (TRADEDATE, FUND_NAME)
        )
        ENGINE = ReplacingMergeTree()
        ORDER BY (TRADEDATE, FUND_NAME);
        '''
        cursor.execute(sql)

        # >>>> [FUND_STKFUNDSCORE]
        if checktable('FUND_STKFUNDSCORE', ls_tables):
            cursor.execute('DROP TABLE IF EXISTS FUND_STKFUNDSCORE;')
        sql = '''
        CREATE TABLE IF NOT EXISTS FUND_STKFUNDSCORE(
            FUND_NAME VARCHAR NOT NULL COMMENT '基金简称',
            STRATEGY VARCHAR NOT NULL COMMENT '基金策略',
            COMPANY_SIZE VARCHAR NULL COMMENT '管理人规模',
            FIRSTDATE VARCHAR NOT NULL COMMENT '净值起始日期',
            LASTDATE VARCHAR NOT NULL COMMENT '净值截至日期',
            RAWWEEKRET DOUBLE NOT NULL COMMENT '基金近周收益率',
            RAWYTDRET DOUBLE NOT NULL COMMENT '基金本年收益率',
            RAWYEARRET DOUBLE NOT NULL COMMENT '基金近年收益率',
            RAWANNRET DOUBLE NOT NULL COMMENT '基金年化收益率',
            WEEKRET DOUBLE NOT NULL COMMENT '基金近周超额收益率',
            MONTHRET DOUBLE NOT NULL COMMENT '基金近月/4周超额收益率',
            QUARTYRET DOUBLE NOT NULL COMMENT '基金近季/12周超额收益率',
            HALFYRET DOUBLE NOT NULL COMMENT '基金半年/25周超额收益率',
            YTDRET DOUBLE NOT NULL COMMENT '基金本年超额收益率',
            YEARRET DOUBLE NOT NULL COMMENT '基金近年超额收益率',
            TWOYRET DOUBLE NOT NULL COMMENT '基金近2年超额收益率',
            YEARSTD DOUBLE NOT NULL COMMENT '基金近年超额波动率',
            YEARMDD DOUBLE NOT NULL COMMENT '基金近年超额最大回撤',
            YEARSKEW DOUBLE NOT NULL COMMENT '基金近年超额收益率偏度',
            YEARSHARPE DOUBLE NULL COMMENT '基金近年超额夏普率',
            YEARCALMAR DOUBLE NULL COMMENT '基金近年超额卡玛比',
            YEARWINRATIO DOUBLE NOT NULL COMMENT '基金近年超额胜率',
            YEARPLRATIO DOUBLE NULL COMMENT '基金近年超额盈亏比',
            ANNRET DOUBLE NOT NULL COMMENT '基金历史年化超额收益率',
            CUMRET DOUBLE NOT NULL COMMENT '基金历史累计超额收益率',
            ANNSTD DOUBLE NOT NULL COMMENT '基金历史超额收益波动率',
            HISTSKEW DOUBLE NOT NULL COMMENT '基金历史超额收益偏度',
            MAXDD DOUBLE NOT NULL COMMENT '基金历史超额收益最大回撤',
            MAXDDSTART VARCHAR NOT NULL COMMENT '超额收益最大回撤起始日期',
            MAXDDEND VARCHAR NOT NULL COMMENT '超额收益最大回撤结束日期',
            MAXDDFIX VARCHAR NULL COMMENT '超额收益最大回撤修复日期',
            TERM_MDD DOUBLE NULL COMMENT '超额收益最大回撤持续周数',
            TERM_FIX DOUBLE NULL COMMENT '超额收益最大回撤修复周数',
            ANNSHARPE DOUBLE NULL COMMENT '基金历史年化超额收益夏普率',
            ANNCALMAR DOUBLE NULL COMMENT '基金历史年化超额收益卡玛比',
            CUMCALMAR DOUBLE NULL COMMENT '基金历史累计超额收益卡玛比',
            CUMWINRATIO DOUBLE NOT NULL COMMENT '基金历史超额收益胜率',
            CUMPLRATIO DOUBLE NULL COMMENT '基金历史超额收益盈亏比',
            ST_PERFORM DOUBLE NOT NULL COMMENT '短期表现评分',
            LT_PERFORM DOUBLE NOT NULL COMMENT '长期表现评分',
            RISK_ADJUSTED DOUBLE NOT NULL COMMENT '风险调整收益评分',
            ALPHABLITY DOUBLE NOT NULL COMMENT 'ALPHA能力评分',
            RETURN_CORR DOUBLE NOT NULL COMMENT '超额相关性评分',
            STYLE_CTRL DOUBLE NOT NULL COMMENT '风格控制评分',
            SUMMARY DOUBLE NOT NULL COMMENT '综合表现评分',
            CREATE_TIME TIMESTAMP MATERIALIZED now() COMMENT '创建时间',
            PRIMARY KEY (FUND_NAME)
        )
        ENGINE = ReplacingMergeTree()
        ORDER BY (FUND_NAME);
        '''
        cursor.execute(sql)

        # >>>> [PORTFOLIO_RAWNAV]
        if checktable('PORTFOLIO_RAWNAV', ls_tables):
            cursor.execute('DROP TABLE IF EXISTS PORTFOLIO_RAWNAV;')
        sql = '''
        CREATE TABLE IF NOT EXISTS PORTFOLIO_RAWNAV(
            FUND_NAME VARCHAR NOT NULL COMMENT '基金简称',
            FUND_CODE VARCHAR NOT NULL COMMENT '基金代码',
            TRADEDATE VARCHAR NOT NULL COMMENT '净值日期',
            FETCHDATE VARCHAR NOT NULL COMMENT '信息获取日期',
            FUND_UNITNAV FLOAT NOT NULL COMMENT '基金单位净值',
            FUND_CUMNAV FLOAT NOT NULL COMMENT '基金累计净值',
            HOLD_SHARES DOUBLE NOT NULL COMMENT '基金持仓份额',
            CREATE_TIME TIMESTAMP MATERIALIZED now() COMMENT '创建时间',
            PRIMARY KEY (TRADEDATE, FUND_CODE)
        )
        ENGINE = ReplacingMergeTree()
        ORDER BY (TRADEDATE, FUND_CODE);
        '''
        cursor.execute(sql)

        # >>>> [KMAX_RAWNAV]
        if checktable('KMAX_RAWNAV', ls_tables):
            cursor.execute('DROP TABLE IF EXISTS KMAX_RAWNAV;')
        sql = '''
        CREATE TABLE IF NOT EXISTS KMAX_RAWNAV(
            FUND_NAME VARCHAR NOT NULL COMMENT '基金简称',
            FUND_CODE VARCHAR NOT NULL COMMENT '基金代码',
            TRADEDATE VARCHAR NOT NULL COMMENT '净值日期',
            FETCHDATE VARCHAR NOT NULL COMMENT '信息获取日期',
            FUND_UNITNAV FLOAT NOT NULL COMMENT '基金单位净值',
            FUND_CUMNAV FLOAT NOT NULL COMMENT '基金累计净值',
            STRATEGY VARCHAR NOT NULL COMMENT '基金策略',
            BENCHMARK_CODE VARCHAR NULL COMMENT '业绩基准指数',
            SOURCE VARCHAR NOT NULL COMMENT '数据来源',
            CREATE_TIME TIMESTAMP MATERIALIZED now() COMMENT '创建时间',
            PRIMARY KEY (TRADEDATE, FUND_CODE)
        )
        ENGINE = ReplacingMergeTree()
        ORDER BY (TRADEDATE, FUND_CODE);
        '''
        cursor.execute(sql)

        # >>>> [KMAX_WEEKLYMARKET]
        if checktable('KMAX_WEEKLYMARKET', ls_tables):
            cursor.execute('DROP TABLE IF EXISTS KMAX_WEEKLYMARKET;')
        sql = '''
        CREATE TABLE IF NOT EXISTS KMAX_WEEKLYMARKET(
            FUND_NAME VARCHAR NOT NULL COMMENT '基金简称',
            FUND_CODE VARCHAR NOT NULL COMMENT '基金代码',
            FUND_NAV FLOAT NOT NULL COMMENT '基金复权净值',
            TRADEDATE VARCHAR NOT NULL COMMENT '净值日期',
            STRATEGY VARCHAR NOT NULL COMMENT '基金策略',
            BENCHMARK_CODE VARCHAR NULL COMMENT '业绩基准指数',
            FUND_ADJNAV FLOAT NOT NULL COMMENT '基金调整净值',
            FUND_RETURN DOUBLE NULL COMMENT '基金收益率',
            BENCHMARK_NAV FLOAT NOT NULL COMMENT '业绩基准净值',
            BENCHMARK_RETURN DOUBLE NULL COMMENT '业绩基准收益率',
            EXCESSIVE_NAV FLOAT NOT NULL COMMENT '超额收益净值',
            EXCESSIVE_RETURN DOUBLE NULL COMMENT '超额收益率',
            CREATE_TIME TIMESTAMP MATERIALIZED now() COMMENT '创建时间',
            PRIMARY KEY (TRADEDATE, FUND_CODE)
        )
        ENGINE = ReplacingMergeTree()
        ORDER BY (TRADEDATE, FUND_CODE);
        '''
        cursor.execute(sql)

        raw_conn.commit()
    except:
        raise SystemError('Database Setup failed.')

    print('Initialization Finished.')
    return

def checktable(table, ls_tables):
    if table in ls_tables:
        drop = ''
        while str(drop).capitalize() not in ['Y', 'N']:
            drop = input('[WARNING]: Table "{}" already exits, replace with initial table? [y/n]:'.format(table))
        if str(drop).capitalize() == 'Y':
            return True

if __name__ == '__main__':
    db_init(conn_to)
