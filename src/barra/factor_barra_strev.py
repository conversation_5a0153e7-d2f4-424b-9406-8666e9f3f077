# -*- coding: utf-8 -*-

from basic import get_tradedate
import numpy as np
import pandas as pd

def factor_barra_strev(df_dret, df_mktret, ls_tdate):
    """
    计算STREV因子, (CNTR)

    This descriptor is first computed as the exponentially-weighted sum of the log excess returns 
    of the stock relative to the market over a trailing 63-day window, with a 10-day half-life, 
    then is multiplied with -1 to reverse sign. Finally, it is computed as the equal-weighted moving 
    average over a three-day window lagged by one day.

    Parameters
    ----------
        df_dret : DataFrame - 日频股票收益数据
        df_mktret : DataFrame - 全市场收益率
        ls_tdate : List [str] - 交易日序列

    Returns
    -------
    DataFrame
    
    """
    # 计算对数相对收益
    ls_twindow = get_tradedate(enddate = ls_tdate[-1], n = len(ls_tdate) + 63 + 10)
    df_RET = df_dret.pivot(index = 'TRADEDATE', columns = 'STOCK_CODE', values = 'RETURN').loc[ls_twindow].copy()
    df_MRET = df_mktret.loc[ls_twindow][['RETURN']].copy()
    df_LRET = np.log(df_RET + 1)
    df_LMRET = np.log(df_MRET + 1)
    df_RS = df_LRET.sub(df_LMRET.values, axis = 1)

    wl = 63
    hl = 10
    a = 0.5 ** (1 / hl)
    eweight = [(1 - a) * (a ** i) for i in np.arange(wl, 0, -1) - 1]
    # eweight[0] = eweight[0] / (1 - a)
    eweight /= sum(eweight)

    # 利用对角阵生成加权乘数矩阵，矩阵算法较快但因为fillna为0，无法排除NaN数据
    x = 0
    for i in range(wl):
        x += np.eye(df_RS.shape[0] - wl + 1, df_RS.shape[0], k = i) * eweight[i]
    x = np.vstack((np.zeros([wl - 1, df_RS.shape[0]]), x))
    df_output = pd.DataFrame(np.dot(x, df_RS.fillna(0)))
    df_output.columns = df_RS.columns
    df_output.index = df_RS.index
    
    df_output = -df_output
    df_output = df_output.rolling(3, min_periods = 1).mean().shift(1).loc[ls_tdate].stack().reset_index().copy()

    df_output.columns = ['TRADEDATE', 'STOCK_CODE', 'VALUE']
    df_output = df_output[df_output['VALUE'] != 0].copy()
    df_output['FACTOR'] = 'STREV'

    return df_output

if __name__ == '__main__':
    pass