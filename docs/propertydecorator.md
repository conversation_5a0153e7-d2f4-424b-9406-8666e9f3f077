# Use property decorator in Python

## How it works

In Python, the `@property` decorator is used to define a read-only attribute of a class. It is typically used with classes that represent mutable data structures, such as lists or dictionaries, to provide a means of accessing the data without modifying it.

When a class uses the `@property` decorator to define an attribute, the attribute is read-only, meaning it cannot be modified directly. Instead, a setter method must be defined to modify the attribute's value. This helps to maintain the integrity of the data structure and prevent accidental modification.

## Example

The `@property` decorator is used in the following manner:

```python
class MyClass:
    def __init__(self, data):
        self._data = data

    @property
    def data(self):
        return self._data

    @data.setter
    def data(self, new_data):
        self._data = new_data
```

In this example, the `MyClass` class has a read-only `data` attribute. To modify the value of the `data` attribute, a setter method `@data.setter` is defined.

Here are some things to note about the use of the `@property` decorator:

1. The attribute must be a data descriptor (i.e., it must have a `__get__` and a `__set__` method). If the attribute is not a data descriptor, the `@property` decorator will raise an error.
2. The attribute must be defined before the `@property` decorator is applied to it. If the `@property` decorator is applied before the attribute is defined, an error will be raised.
3. The attribute's name should start with an underscore, as it is considered a private attribute.
4. The setter method (`@data.setter`) must be defined to allow the modification of the attribute. If the setter method is not defined, attempting to modify the attribute will raise an error.
5. The `@property` decorator can be used to define a read-only attribute, but it can also be used to define a write-only attribute (i.e., a setter method is required).

Using the `@property` decorator can make your code more readable and less error-prone, especially when dealing with mutable data structures.
