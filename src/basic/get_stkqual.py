# -*- coding: utf-8 -*-
# 提取有效指数成分股

import pandas as pd
import numpy as np
from basic import db_vulpes, get_tradedate

def get_stkqual(mktcode = '000985.CSI'):
    """
    提取有效指数成分股, 默认中证全指成分股剔除北交所
    
    Parameters
    ----------
    mktcode : str - ['000985.CSI'] 市场指数代码

    Returns
    -------
    DataFrame
    """
    
    conn = db_vulpes()
    sql = '''
    SELECT 
        STOCK_CODE,
        TRADEDATE,
        WEIGHT
    FROM
        INDEX_CONSWEIGHT
    WHERE
        INDEX_CODE = '{}' AND
        WEIGHT > 0 AND
        STOCK_CODE NOT LIKE '%%.BJ'
    ORDER BY TRADEDATE;
    '''.format(mktcode)
    
    df_date = pd.read_sql(sql, con = conn).drop_duplicates()
    df_qual = df_date.pivot(index = 'TRADEDATE', columns = 'STOCK_CODE', values = 'WEIGHT') * 0 + 1
    df_stkqual = pd.DataFrame(index = get_tradedate(startdate = df_qual.index[0])).join(df_qual.fillna(0), how = 'left').ffill().replace({0: np.nan})

    return df_stkqual

if __name__ == '__main__':
    pass