# -*- coding: utf-8 -*-

from basic import get_tradedate
import pandas as pd
import numpy as np

def factor_barra_atvr(df_dturnover, ls_tdate):
    """
    计算ATVR因子

    Computed as the exponentially-weighted sum of the percentage of
    shares traded daily over a trailing 252-day window with a 63-day half-life.

    Parameters
    ----------
        df_dturnover : DataFrame - 日频股票换手率数据
        ls_tdate : List [str] - 交易日序列

    Returns
    -------
    DataFrame
    
    """
    # 数据提取
    ls_twindow = get_tradedate(enddate = ls_tdate[-1], n = len(ls_tdate) + 252)
    df_TURN = df_dturnover.pivot(index = 'TRADEDATE', columns = 'STOCK_CODE', values = 'TURNOVER').loc[ls_twindow].copy()

    wl = 252
    hl = 63
    a = 0.5 ** (1 / hl)
    eweight = [(1 - a) * (a ** i) for i in np.arange(wl, 0, -1) - 1]
    # eweight[0] = eweight[0] / (1 - a)
    eweight /= sum(eweight)

    # EWA的矩阵加速算法，矩阵算法较快但因为fillna为0，无法排除NaN数据
    # 利用对角阵生成加权乘数矩阵
    x = 0
    for i in range(wl):
        x += np.eye(df_TURN.shape[0] - wl + 1, df_TURN.shape[0], k = i) * eweight[i]
    x = np.vstack((np.zeros([wl - 1, df_TURN.shape[0]]), x))
    df_ATVR = pd.DataFrame(np.dot(x, df_TURN.fillna(0)))
    df_ATVR.columns = df_TURN.columns
    df_ATVR.index = df_TURN.index

    df_output = df_ATVR.loc[ls_tdate].stack().reset_index()
    df_output.columns = ['TRADEDATE', 'STOCK_CODE', 'VALUE']
    df_output = df_output[df_output['VALUE'] != 0].copy()

    df_output['FACTOR'] = 'ATVR'

    return df_output

if __name__ == '__main__':
    pass