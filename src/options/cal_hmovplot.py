# -*- coding: utf-8 -*-
# 生成ETF期权标的资产绝对涨幅历史概率曲面图

import os
import pandas as pd
import numpy as np
from basic.db_conn import db_vulpes

import matplotlib.pyplot as plt
from matplotlib.ticker import MultipleLocator
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False


def cal_hmovplot(foldername = 'distfig', wl= 750, hl = 250):
    '''
    生成ETF期权标的资产绝对涨幅历史概率曲面图

    Parameters
    ----------
    foldername : str - 文件夹名称
    ls_pct : list - 变化百分比阈值
    wl : int - 历史数据回顾区间
    hl : int - 指数加权半衰期
    
    Returns
    -------
    DataFrame
    '''
    # 生成文件夹
    folder = os.path.exists(foldername)
    if not folder:
        os.makedirs(foldername)
    
    # 下载数据
    conn = db_vulpes()
    sql = '''
    SELECT
        FUND_CODE,
        TRADEDATE,
        RETURN,
        FUND_NAME,
        EXCHANGE
    FROM FOPTION_ETFMARKET
    '''
    df_etfmkt = pd.read_sql(sql, con = conn)
    df_return = df_etfmkt.pivot(index = 'TRADEDATE', columns = 'FUND_CODE', values = 'RETURN')

    a = 0.5 ** (1 / hl)
    eweight = [(1 - a) * (a ** i) for i in np.arange(wl, 0, -1) - 1]
    eweight /= sum(eweight)
    
    # 观察周期1-20天
    # 触发阈值1-10%
    ls_left = range(1, 21)
    ls_pct = range(1, 11)
    dic_output = {}
    ls_col = df_return.columns
    for col in ls_col:    
        dic_output[col] = pd.DataFrame(index = ls_left, columns = ls_pct)

    # 数据计算
    for n_left in ls_left:
        df_absvolus = (np.exp(np.log(df_return + 1).rolling(n_left).sum()) - 1).abs().tail(wl)
        df_tmp = pd.DataFrame(index = ls_col)
        for i_pct in ls_pct:
            df_tmp[i_pct] = ((df_absvolus >= (i_pct / 100)).T.multiply(eweight).T).sum()
            for col in ls_col:
                dic_output[col].loc[n_left, i_pct] = df_tmp[i_pct][col]

    df_etfinfo = df_etfmkt.drop(columns = ['TRADEDATE', 'RETURN']).drop_duplicates()

    df_etfinfo['NAME'] = df_etfinfo['EXCHANGE'] + '-' + df_etfinfo['FUND_NAME']

    writer = pd.ExcelWriter(path = './/{}//etfoption_dist.xlsx'.format(foldername))

    for col in dic_output.keys():
        str_name = df_etfinfo.query('FUND_CODE == "{}"'.format(col))['NAME'].values[0]
        df_plot = dic_output[col].copy()
        df_plot.to_excel(excel_writer = writer, sheet_name = str_name)

        fig = plt.figure(figsize = (10, 10))
        ax = fig.add_subplot(projection = '3d')
        ax.xaxis.set_major_locator(MultipleLocator(1))
        ax.yaxis.set_major_locator(MultipleLocator(1))
        ax.zaxis.set_major_locator(MultipleLocator(10))
        X, Y = np.meshgrid(df_plot.columns.values, df_plot.index.values)
        surf = ax.plot_surface(X, Y, df_plot.values * 100, cmap = plt.get_cmap('coolwarm'), alpha = 0.85)
        fig.colorbar(surf, shrink = 0.5, aspect = 5)
        ax.set_xlabel('%')
        ax.set_ylabel('Days')
        ax.set_zlabel('P')
        plt.title(str_name)
        plt.savefig('{}//{}.png'.format(foldername, str_name))
        # plt.show()
        plt.close()
    writer.close()

    return dic_output

if __name__ == '__main__':
    pass