# -*- coding: utf-8 -*-
# 计算时序展期因子持仓权重

import numpy as np
import pandas as pd

def factor_cta_tryd(df_mktdata, df_qualified, trg_value):
    '''
    计算时序展期因子持仓权重

    Parameters
    ----------
    df_mktdata : DataFrame - 日频行情数据
    df_qualified : DataFrame - 可以交易品种数据
    trg_value : float - 展期触发阈值

    Returns
    -------
    DataFrame
    '''
    df_mktdata['DLISTDATE'] = pd.to_datetime(df_mktdata['DLISTDATE'])
    # 分子为主力与次主力合约的对数收盘价差
    df_numerator = df_mktdata.query('MTYPE == 1').pivot(index = 'TRADEDATE', columns = 'STDCODE', values = 'CLOSE').apply(np.log)
    df_numerator -= df_mktdata.query('MTYPE == 2').pivot(index = 'TRADEDATE', columns = 'STDCODE', values = 'CLOSE').apply(np.log)
    # 分母为主力与次主力合约的到期日天数
    df_denominator = df_mktdata.query('MTYPE == 1').pivot(index = 'TRADEDATE', columns = 'STDCODE', values = 'DLISTDATE')
    df_denominator -= df_mktdata.query('MTYPE == 2').pivot(index = 'TRADEDATE', columns = 'STDCODE', values = 'DLISTDATE')

    for i in df_denominator.columns:
        df_denominator[i] = df_denominator[i].dt.days
    # 展期年化收益率
    df_rollyield = -(df_numerator / df_denominator.replace({0: np.nan}) * 365) * df_qualified

    df_signal = (df_rollyield > trg_value) * 1
    df_signal -= (-df_rollyield > trg_value) * 1
    df_signal.fillna(0, inplace = True)

    se_holdnum = df_signal.apply(np.abs).sum(axis = 1)
    df_holdpct = df_signal.divide(se_holdnum, axis = 0)

    return df_holdpct

if __name__ == '__main__':
    pass