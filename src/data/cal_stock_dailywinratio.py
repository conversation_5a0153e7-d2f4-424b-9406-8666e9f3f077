# -*- coding: utf-8 -*-
# 计算每日截面胜率
from basic import get_tradedate, get_indexmkt
import pandas as pd

def cal_stock_dailywinratio(startdate, enddate, df_stkdata, dic_stkqual):
    '''
    计算每日截面胜率

    Parameters
    ----------
    startdate : str - 'yyyymmdd' 时间窗口起点
    enddate : str - 'yyyymmdd' 时间窗口终点
    df_stkdata : DataFrame - 股票收益率数据
    dic_stkqual : DataFrame - 股票截面指数成分股字典

    Returns
    -------
    DataFrame
    '''
    ls_twindow = get_tradedate(startdate, enddate)
    ls_index = ['000300.SH', '000905.SH', '000852.SH', '000985.CSI']

    df_qual = dic_stkqual['000985.CSI'].loc[ls_twindow].stack().to_frame(name = 'QUAL').reset_index().rename(columns={'level_0': 'TRADEDATE', 'level_1': 'STOCK_CODE'})
    df_stkret = df_stkdata[df_stkdata['TRADEDATE'].isin(ls_twindow)].merge(df_qual, on = ['TRADEDATE', 'STOCK_CODE'], how = 'inner')

    ls_winratio = []
    for index in ls_index:
        df_index = get_indexmkt(index).reset_index().rename({'RETURN': index}, axis = 1)[['TRADEDATE', index]].copy()
        df_ismem = dic_stkqual[index].loc[ls_twindow].stack().to_frame(name = 'IS_' + index).reset_index().rename(columns={'level_0': 'TRADEDATE', 'level_1': 'STOCK_CODE'})
        df_sret = df_stkret.merge(df_index, on = 'TRADEDATE', how = 'left').merge(df_ismem, on = ['TRADEDATE', 'STOCK_CODE'], how = 'left').drop(columns = ['STOCK_CODE'])
        df_sret[index] = ((df_sret['RETURN'] - df_sret[index]) > 0) * 1.0

        df_allcount = df_sret.groupby(['TRADEDATE']).sum()
        df_incount = df_sret[df_sret[f'IS_{index}'] == 1].groupby(['TRADEDATE']).sum()
        df_outcount = df_sret[df_sret[f'IS_{index}'] != 1].groupby(['TRADEDATE']).sum()
        df_instd = df_sret[df_sret[f'IS_{index}'] == 1].groupby(['TRADEDATE']).std()[['RETURN']].rename(columns = {'RETURN': 'IN_STD'})
        df_outstd = df_sret[df_sret[f'IS_{index}'] != 1].groupby(['TRADEDATE']).std()[['RETURN']].rename(columns = {'RETURN': 'OUT_STD'})

        df_allwr = (df_allcount[index] / df_allcount['QUAL']).to_frame(name = 'WINRATIO')
        df_inwr = (df_incount[index] / df_incount['QUAL']).to_frame(name = 'IN_WINRATIO')
        df_outwr = (df_outcount[index] / df_outcount['QUAL']).to_frame(name = 'OUT_WINRATIO')
        df_swinratio = df_allwr.join(df_inwr).join(df_outwr).join(df_instd).join(df_outstd).fillna(0).reset_index()
        df_swinratio['BENCHMARK'] = index
        ls_winratio.append(df_swinratio.copy())

    df_winratio = pd.concat(ls_winratio).reset_index(drop = True)
    df_winratio['BENCHMARK'] = df_winratio['BENCHMARK'].replace({'000300.SH': 'INDEX300', '000905.SH': 'INDEX500', '000852.SH': 'INDEX1000', '000985.CSI': 'INDEXCHN'})
    
    return df_winratio

if __name__ == '__main__':
    pass