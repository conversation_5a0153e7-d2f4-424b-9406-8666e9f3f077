# -*- coding: utf-8 -*-
# 计算基金综合评分

import pandas as pd
from fund import cal_kernel_barracvx
from fund import cal_kernel_statistics
from basic import get_barraret

def cal_stkfundscore(df_funddata):
    """
    计算基金综合评分

    Parameters
    ----------
    df_funddata : DataFrame - 基金净值数据, 数据结构参考FUND_WEEKLYMARKET

    Returns
    -------
    tuple - DataFrame
    """

    df_barraret = get_barraret(startdate = '', enddate = df_funddata['TRADEDATE'].max())
    ls_sta = []
    ls_reg = []

    for fund in df_funddata['FUND_NAME'].unique():
        df_excenav = df_funddata[df_funddata['FUND_NAME'] == fund].set_index('TRADEDATE')[['EXCESSIVE_NAV']]
        df_rawnav = df_funddata[df_funddata['FUND_NAME'] == fund].set_index('TRADEDATE')[['FUND_NAV']]

        dic_data = {
            'FUND_NAME': fund, 
            'STRATEGY': df_funddata[df_funddata['FUND_NAME'] == fund]['STRATEGY'].values[0],
            'COMPANY_SIZE': df_funddata[df_funddata['FUND_NAME'] == fund]['COMPANY_SIZE'].values[0]
        }
        dic_exce = cal_kernel_statistics(df_excenav)
        dic_raw = cal_kernel_statistics(df_rawnav)

        dic_data['RAWWEEKRET'] = dic_raw['WEEKRET']
        dic_data['RAWYTDRET'] = dic_raw['YTDRET']
        dic_data['RAWYEARRET'] = dic_raw['YEARRET']
        dic_data['RAWANNRET'] = dic_raw['ANNRET']
        dic_data.update(dic_exce)
        ls_sta.append(pd.DataFrame(dic_data, index = [1]))

        df_style = cal_kernel_barracvx(df_excenav, df_barraret)
        df_style['FUND_NAME'] = fund
        ls_reg.append(df_style.copy())

    df_sta = pd.concat(ls_sta, ignore_index = True)
    df_pfm = df_sta[['FUND_NAME', 'STRATEGY', 'MONTHRET', 'HALFYRET', 'YEARRET', 'TWOYRET', 'YEARWINRATIO', 'YEARPLRATIO', 'YEARSHARPE', 'YEARCALMAR']].copy()
    df_reg = pd.concat(ls_reg, ignore_index = True)


    df_style_t = df_reg.pivot(index = 'FUND_NAME', columns = 'ITEM', values = 'T_VALUE')
    df_style_t = df_style_t[[i for i in df_style_t.columns[0: -31] if i != 'CONST']].copy()
    df_alpha_t = df_reg.pivot(index = 'FUND_NAME', columns = 'ITEM', values = 'T_VALUE')[['CONST']].copy()
    df_corr = df_funddata.pivot(index = 'TRADEDATE', columns = 'FUND_NAME', values = 'EXCESSIVE_RETURN').tail(100).corr()
    ls_stgcorr = []
    for stg in df_sta['STRATEGY'].unique().tolist():
        ls_stgfund = df_sta[df_sta['STRATEGY'] == stg]['FUND_NAME'].unique()
        ls_stgcorr.append((1 - (df_corr.loc[ls_stgfund][ls_stgfund].sum(axis = 1) - 1) / (len(ls_stgfund) - 1)).to_frame(name = 'CORR'))
    df_stgcorr = pd.concat(ls_stgcorr)

    # from scipy import stats
    # stats.t.isf(0.025, 100 - 37) = 1.9983
    # That's why the t-value compares to 2, about 5% on two tails.

    df_rank_pfm = df_pfm.fillna(value = {'YEARPLRATIO': 10000, 'YEARCALMAR': 10000}).set_index('FUND_NAME').join(df_alpha_t).join(df_stgcorr)\
        .join((df_style_t.abs() < 2).sum(axis = 1).to_frame(name = 'STYLE'))\
        .groupby('STRATEGY').rank(ascending = True, pct = True, method = 'dense').reset_index()

    df_rank_pfm['ST_PERFORM'] = (df_rank_pfm['MONTHRET'] + df_rank_pfm['HALFYRET']) / 2
    df_rank_pfm['LT_PERFORM'] = (df_rank_pfm['YEARRET'] + df_rank_pfm['TWOYRET']) / 2
    df_rank_pfm['ALPHABLITY'] = (df_rank_pfm['YEARWINRATIO'] + df_rank_pfm['YEARPLRATIO']) / 2
    df_rank_pfm['RISK_ADJUSTED'] = (df_rank_pfm['YEARSHARPE'] + df_rank_pfm['YEARCALMAR']) / 2
    df_rank_pfm['STYLE_CTRL'] = (df_rank_pfm['STYLE'] + df_rank_pfm['CONST']) / 2
    df_rank_pfm['RETURN_CORR'] = df_rank_pfm['CORR']

    df_score = df_pfm[['FUND_NAME', 'STRATEGY']]\
        .merge(df_rank_pfm[['FUND_NAME', 'ST_PERFORM', 'LT_PERFORM', 'RISK_ADJUSTED', 'ALPHABLITY', 'RETURN_CORR', 'STYLE_CTRL']], on = 'FUND_NAME', how = 'left')
    df_score = df_pfm[['FUND_NAME', 'STRATEGY']]\
        .merge(df_score.set_index('FUND_NAME').groupby('STRATEGY')\
        .rank(ascending = True, pct = True, method = 'dense').reset_index(), on = 'FUND_NAME', how = 'left')
    df_score['SUMMARY'] = df_score[['ST_PERFORM', 'LT_PERFORM', 'ALPHABLITY', 'RISK_ADJUSTED', 'STYLE_CTRL', 'RETURN_CORR']].mean(axis = 1)

    df_summary = df_sta.merge(df_score, on = ['FUND_NAME', 'STRATEGY'], how = 'left')

    return df_summary

if __name__ == '__main__':
    pass
