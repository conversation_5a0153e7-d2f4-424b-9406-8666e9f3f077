# -*- coding: utf-8 -*-

from openpyxl import load_workbook
import warnings
warnings.filterwarnings('ignore')

import pandas as pd
import numpy as np
import pyexcel
# pyexcel pyexcel-xls pyexcel-xlsx
import pdfplumber

import imaplib
imaplib.Commands['ID'] = ('AUTH')
import zipfile
# 需要源代码decode("cp437") 改为 GBK
from fnmatch import fnmatch
from email import message_from_bytes
from email.header import decode_header
import re
import os
import time

# import dataframe_image as dfi
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import matplotlib.ticker as ticker
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

from basic import db_vulpes
from portfolio.config import *

class PortfolioOnline():
    '''
    云端组合数据
    '''

    def __init__(self):
        '''
        构造函数
        '''
        # 邮箱信息
        self.username = USERNAME
        self.password = PASSWORD
        self.imap_url = IMAP_URL
        self.inbox = INBOX
        
        # 持仓信息
        self.dic_hold = HOLD
        self.dic_formtocode = {self.dic_hold[i]['Fundfile']: i for i in self.dic_hold.keys()}
        # 业绩基准数据库
        self.conn = db_vulpes(writer = True)
        
        # 本地文件路径
        self.path = os.path.dirname(os.path.realpath(__file__))
        self.path_rawfolder = os.path.join(self.path, 'mailextension')
        if not os.path.exists(self.path_rawfolder):
            os.makedirs(self.path_rawfolder)

        self.path_figfolder = os.path.join(self.path, 'fig')
        if not os.path.exists(self.path_figfolder):
            os.makedirs(self.path_figfolder)
        
        self.df_rawnav = pd.DataFrame()

    def decode_info(self, s, init = 0, mailaddr = False):
        '''
        解码函数
        '''
        try:
            if mailaddr:
                r_mail = r'[a-z0-9\.\-+_]+@[a-z0-9\.\-+_]+\.[a-z]+'
                value = None
                for info in decode_header(s):
                    if info[1]:
                        ls_match = re.findall(r_mail, info[0].decode(info[1]))
                    else:
                        try:
                            ls_match = re.findall(r_mail, info[0].decode())
                        except:
                            ls_match = re.findall(r_mail, info[0])
                    
                    if len(ls_match) > 0:
                        return ls_match[0]
            else:
                value, charset = decode_header(s)[init]
                if charset:
                    value = value.decode(charset)
        except:
            return None
        
        return value

    def download_extention(self, lastdate: str = ''):

        try:
            search_range = 'SINCE ' + pd.to_datetime(lastdate).strftime('%d-%b-%Y')
        except:
            search_range = 'ALL'

        emailserver = imaplib.IMAP4_SSL(self.imap_url)  # 邮箱服务器地址
        emailserver.login(self.username, self.password)
        if self.imap_url.split('.')[1] == '163':
            args = ("name", self.username, "version", "1.0.0", "vendor", "myclient")
            typ, dat = emailserver._simple_command('ID', '("' + '" "'.join(args) + '")')
        emailserver.select(self.inbox)
        _, message_ids = emailserver.search(None, search_range)

        for message_id in message_ids[0].split():
            # 获取邮件内容
            _, data = emailserver.fetch(message_id, '(RFC822)')
            raw_email = data[0][1]
            email_message = message_from_bytes(raw_email)

            subject = self.decode_info(email_message.get('SUBJECT'))
            from_addr = self.decode_info(email_message.get('FROM'), mailaddr = True)  
            
            ls_time = email_message.get('DATE').split(',')[-1].split(' ')
            while '' in ls_time: ls_time.remove('') 
            date = pd.to_datetime(' '.join(ls_time[0: 3])).date().strftime('%Y-%m-%d')

            # 遍历所有附件
            for part in email_message.walk():
                if part.get_content_maintype() == 'multipart':
                    continue
                if part.get('Content-Disposition') is None:
                    continue
                if not part.get_filename():
                    continue

                # 保存附件到本地
                filename = self.decode_info(part.get_filename()).lower()
                extension = filename.split('.')[-1]

                if filename:
                    if extension in ['xlsx', 'xls', 'zip']:
                        name = '_'.join([date, filename])
                        filepath = os.path.join(self.path_rawfolder, name)
                        with open(filepath, 'wb') as f:
                            f.write(part.get_payload(decode = True))

                        if extension == 'zip':
                            zf = zipfile.ZipFile(filepath)
                            zf.extractall(path = self.path_rawfolder)
                            zf.close()
                    else:
                        print('[DOWNLOAD SKIP] {} FROM {}'.format(subject, from_addr))
        # 关闭连接
        emailserver.logout()

    def cal_rawnav(self):
        """
        提取原始基金净值
        """
        ls_filename = os.listdir(self.path_rawfolder)
        for name in ls_filename:
            filemt = time.localtime(os.stat(os.path.join(self.path_rawfolder, name)).st_mtime)
            if time.strftime('%Y%m%d', filemt) < time.strftime('%Y%m%d', time.localtime()):
                continue
            if name.split('.')[-1] == 'xls':
                pyexcel.save_book_as(file_name = os.path.join(self.path_rawfolder, name), dest_file_name = os.path.join(self.path_rawfolder, name + 'x'))

        ls_fileformat = list(self.dic_formtocode.keys())
        ls_all = []
        ls_filename = os.listdir(self.path_rawfolder)
        for fform in ls_fileformat:
            ls_match = []
            for fname in ls_filename:
                if fnmatch(fname, fform.lower()):
                    ls_match.append(fname)
            if len(ls_match) > 0:
                fval_code = self.dic_hold[self.dic_formtocode[fform]]['Fundcode']
                fval_name = self.dic_hold[self.dic_formtocode[fform]]['Fundname']
                
                fref_date = self.dic_hold[self.dic_formtocode[fform]]['Elements']['date']
                fref_nav = self.dic_hold[self.dic_formtocode[fform]]['Elements']['nav']
                fref_cumnav = self.dic_hold[self.dic_formtocode[fform]]['Elements']['cumnav']
                fref_hshare = self.dic_hold[self.dic_formtocode[fform]]['Elements']['holdshares']
                ls_tmp = []
                for mfname in ls_match:
                    fval_fetchdate = pd.to_datetime(mfname.split('_')[0]).strftime('%Y%m%d')
                    wb = load_workbook(os.path.join(self.path_rawfolder, mfname))
                    ws = wb.active

                    fval_date = str(ws[fref_date].value).replace('年', '').replace('月', '').replace('日', '')
                    fval_nav = ws[fref_nav].value
                    fval_cumnav = np.nan if pd.isna(ws[fref_cumnav].value) else ws[fref_cumnav].value
                    fval_hshare = np.nan if fref_hshare == '' else ws[fref_hshare].value

                    df_tmp = pd.DataFrame({
                        'FUND_CODE': str(fval_code),
                        'FUND_NAME': str(fval_name),
                        'TRADEDATE' : pd.to_datetime(fval_date if type(fval_date) is str else fval_date).strftime('%Y%m%d'),
                        'FETCHDATE' : fval_fetchdate,
                        'FUND_UNITNAV': round(float(fval_nav.replace(',', '') if type(fval_nav) is str else fval_nav), 4),
                        'FUND_CUMNAV': round(float(fval_cumnav.replace(',', '') if type(fval_cumnav) is str else fval_cumnav), 4),
                        'HOLD_SHARES': float(fval_hshare.replace(',', '') if type(fval_hshare) is str else fval_hshare),
                        }, index = [0])
                    ls_tmp.append(df_tmp.copy())
                    wb.close()
                if len(ls_tmp) > 0:
                    ls_all.append(pd.concat(ls_tmp, ignore_index = True).drop_duplicates().sort_values('TRADEDATE'))

        if len(ls_all) > 0:
            self.df_rawnav = pd.concat(ls_all, ignore_index = True).drop_duplicates().sort_values(['FUND_CODE', 'TRADEDATE'])

    def cal_washnav(self, df_lastnav: pd.DataFrame = None):
        """
        清洗基金净值
        """
        if df_lastnav is None:
            df_allnav = self.df_rawnav.copy()
        else:
            df_allnav = pd.concat([df_lastnav, self.df_rawnav], ignore_index = True).drop_duplicates().sort_values(['FUND_CODE', 'TRADEDATE'])

        ls_washnav = []
        ls_fundcode = df_allnav['FUND_CODE'].unique().tolist()
        for fcode in ls_fundcode:
            df_fundnav = df_allnav[df_allnav['FUND_CODE'] == fcode].sort_values('TRADEDATE').copy()
            df_fundnav['HOLD_EQUITY'] = df_fundnav['FUND_UNITNAV'] * df_fundnav['HOLD_SHARES']
            df_fundnav['FUND_CUMDIV'] = df_fundnav['FUND_CUMNAV'] - df_fundnav['FUND_UNITNAV']
            df_fundnav['FUND_DIV'] = df_fundnav['FUND_CUMDIV'].diff().fillna(0).round(4)
            df_fundnav['FUND_ADJNAV'] = 0
            df_fundnav['FUND_RETURN'] = ((df_fundnav['FUND_CUMNAV'] - df_fundnav['FUND_CUMDIV'].shift(1)) / df_fundnav['FUND_UNITNAV'].shift(1) - 1).fillna(0)
            df_fundnav['FUND_CUMRETURN'] = (df_fundnav['FUND_RETURN'] + 1).cumprod() - 1
            df_fundnav['FUND_ADJNAV'] = (df_fundnav['FUND_CUMRETURN'] + 1) * df_fundnav['FUND_UNITNAV'].tolist()[0]
            df_fundnav['FUND_DRAWDOWN'] = df_fundnav['FUND_ADJNAV'] / df_fundnav['FUND_ADJNAV'].cummax() - 1
            ls_washnav.append(df_fundnav.copy())
        
        self.df_washnav = pd.concat(ls_washnav, ignore_index = True).drop_duplicates()

    def cal_statistics(self):
        """
        计算基金统计指标
        """
        ls_fundcode = self.df_washnav['FUND_CODE'].unique().tolist()
        ls_sta = []
        for fcode in ls_fundcode:
            df_fundnav = self.df_washnav[self.df_washnav['FUND_CODE'] == fcode].copy()
            df_fundsta = df_fundnav.tail(1)[['FUND_NAME', 'FUND_CODE', 'TRADEDATE', 'FUND_ADJNAV', 'FUND_CUMRETURN', 'FUND_DRAWDOWN', 'FUND_RETURN']].copy()

            df_fundnav['DATETIME'] = pd.to_datetime(df_fundnav['TRADEDATE'])
            hold_days = (df_fundnav['DATETIME'].tolist()[-1] - df_fundnav['DATETIME'].tolist()[0]).days + 1
            hold_annret = (df_fundnav['FUND_CUMRETURN'].tolist()[-1] + 1) ** (365 / hold_days) - 1
            hold_sharpe = hold_annret / (df_fundnav['FUND_RETURN'].std() * np.sqrt(252))
            df_fundsta['FUND_WEEKRETURN'] = (df_fundnav.tail(5)[['FUND_RETURN']] + 1).cumprod().values[-1][0] - 1
            df_fundsta['HOLD_ANNRETURN'] = hold_annret
            df_fundsta['HOLD_MAXDRAWDOWN'] = df_fundnav['FUND_DRAWDOWN'].min()
            df_fundsta['HOLD_SHARPE'] = hold_sharpe
            df_fundsta['STARTDATE'] = df_fundnav['TRADEDATE'].tolist()[0]

            mdd_hit = df_fundnav[df_fundnav['FUND_DRAWDOWN'] == df_fundnav['FUND_DRAWDOWN'].min()]['TRADEDATE'].tolist()[-1]
            mdd_start = df_fundnav[(df_fundnav['TRADEDATE'] <= mdd_hit) & (df_fundnav['FUND_DRAWDOWN'] == 0)]['TRADEDATE'].tolist()[-1]
            df_mddfix = df_fundnav[(df_fundnav['TRADEDATE'] > mdd_hit) & (df_fundnav['FUND_DRAWDOWN'] == 0)]
            mdd_fix = df_fundnav['TRADEDATE'].tolist()[-1] if df_mddfix.empty else df_mddfix['TRADEDATE'].tolist()[0]
            df_fundsta['MDD_ISFIX'] = 0 if df_mddfix.empty else 1
            df_fundsta['MDD_STRAT'] = mdd_start
            df_fundsta['MDD_HIT'] = mdd_hit
            df_fundsta['MDD_FIX'] = mdd_fix
            
            ls_sta.append(df_fundsta.copy())
        
        self.df_statistics = pd.concat(ls_sta, ignore_index = True).drop_duplicates()
    
    def cal_plotfig(self):
        """
        业绩作图
        """
        ls_fundcode = self.df_washnav['FUND_CODE'].unique().tolist()
        for fcode in ls_fundcode:
            df_fundnav = self.df_washnav[self.df_washnav['FUND_CODE'] == fcode].copy()
            df_fundsta = self.df_statistics[self.df_statistics['FUND_CODE'] == fcode].copy()

            df_fundnav['DATETIME'] = pd.to_datetime(df_fundnav['TRADEDATE'])
            df_fundnav['FUND_HOLDNAV'] = df_fundnav['FUND_CUMRETURN'] + 1
            dd_fix_start = pd.to_datetime(df_fundsta['MDD_STRAT']).values[0]
            dd_fix_date = pd.to_datetime(df_fundsta['MDD_HIT']).values[0]
            dd_fix_fixdate = pd.to_datetime(df_fundsta['MDD_FIX']).values[0]

            plt.figure(figsize=(12, 6))
            plt.subplots_adjust(hspace = 0.02)
            ax1 = plt.subplot(211)
            plt.grid(True, color = "#A5A5A5")
            plt.ylim([df_fundnav['FUND_HOLDNAV'].min() - 0.01, df_fundnav['FUND_HOLDNAV'].max() + 0.01])
            plt.gca().yaxis.set_major_formatter(ticker.FormatStrFormatter('%.2f'))
            plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))

            plt.plot(df_fundnav['DATETIME'], df_fundnav['FUND_HOLDNAV'], lw = 2, label = '持仓累计收益', color = '#D93F2F')

            if df_fundsta['HOLD_MAXDRAWDOWN'].values[0] < 0:
                plt.fill_between(
                    df_fundnav['DATETIME'], 
                    y1 = 0, 
                    y2 = df_fundnav['FUND_HOLDNAV'], 
                    where = (df_fundnav['DATETIME'] >= dd_fix_start) & (df_fundnav['DATETIME'] <= dd_fix_date), 
                    color = 'salmon', 
                    alpha = 0.4, 
                    label = '回撤期')
                if df_fundsta['MDD_ISFIX'].values[0] == 1:
                    plt.fill_between(
                        df_fundnav['DATETIME'], 
                        y1 = 0, 
                        y2 = df_fundnav['FUND_HOLDNAV'], 
                        where = (df_fundnav['DATETIME'] >= dd_fix_date) & (df_fundnav['DATETIME'] <= dd_fix_fixdate), 
                        color = 'green', 
                        alpha = 0.4, 
                        label = '修复期')
            plt.axhline(y = 1, color="black", linestyle="--", lw = 1)
            plt.xticks(rotation = 90, fontsize = 0)
            plt.yticks(fontsize = 10)
            plt.legend(loc = 2, fontsize = 10)
            plt.title(df_fundsta['FUND_NAME'].values[0], fontsize = 14)

            ax2 = plt.subplot(212, sharex = ax1)
            plt.grid(True, color = "#A5A5A5")
            plt.ylim([df_fundnav['FUND_DRAWDOWN'].min() * 100 - 1, 0])
            plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
            plt.gca().yaxis.set_major_formatter(ticker.FormatStrFormatter('%.2f%%'))

            plt.fill_between(df_fundnav['DATETIME'], y1 =0, y2 =  df_fundnav['FUND_DRAWDOWN'] * 100, color = 'gray', alpha = 0.4, label = '最大回撤')

            plt.xticks(rotation = 45, fontsize = 10)
            plt.yticks(fontsize = 10)
            plt.legend(loc = 3, fontsize = 10)

            plt.margins(0,0)
            # plt.subplots_adjust(hspace = 0)
            fig_path = os.path.join(self.path_figfolder, '{}.png'.format(df_fundsta['FUND_NAME'].values[0]))
            plt.savefig(fig_path, dpi = 100, bbox_inches = 'tight', transparent = False)
            plt.close()

    def clean_rawfolder(self):
        '''
        清理附件文件夹
        '''
        for root, dirs, files in os.walk(self.path_rawfolder):
            for name in files:
                os.remove(os.path.join(root, name))
    
    def run(self):
        '''
        运行完整刷新流程
        '''

        try:
            df_lastnav = pd.read_sql('SELECT * FROM PORTFOLIO_RAWNAV', con = self.conn)
            if df_lastnav.empty:
                lastdate = ''
            else:
                lastdate = df_lastnav['FETCHDATE'].max()
        except:
            df_lastnav = None
            lastdate = ''

        self.download_extention(lastdate = lastdate)
        self.cal_rawnav()
        self.cal_washnav(df_lastnav = df_lastnav)
        self.cal_statistics()
        self.cal_plotfig()
        self.clean_rawfolder()

        self.df_rawnav.to_sql('PORTFOLIO_RAWNAV', con = self.conn, index = False, if_exists = 'append', chunksize = 10000)
        raw_conn = self.conn.raw_connection()
        cursor = raw_conn.cursor()
        cursor.execute('OPTIMIZE TABLE PORTFOLIO_RAWNAV FINAL DEDUPLICATE')
        raw_conn.commit()

if __name__ == '__main__':
    pass