# -*- coding: utf-8 -*-
# 下载股票日频衍生指标数据

import pandas as pd
from basic import get_tradedate

def download_stock_dailyindicator(startdate, enddate, conn_ts):
    """
    下载股票日频衍生指标数据

    Parameters
    ----------
    startdate : str - 'yyyymmdd' 时间窗口起点
    enddate : str - 'yyyymmdd' 时间窗口终点
    conn_ts : tushare数据接口

    Returns
    -------
    DataFrame
    """
    ls_tdate = get_tradedate(startdate, enddate)
    if len(ls_tdate) == 0:
        return None

    ls_stkdate = []
    for tdate in ls_tdate:
        try:
            ls_stkdate.append(conn_ts.daily_basic(trade_date = tdate).copy())
        except:
            raise Exception('[Tushare]: Stock daily indicator data download failed.')

    ls_stkdate = [i for i in ls_stkdate if i.shape[0] > 0]
    if len(ls_stkdate) > 0:
        df_stkdate = pd.concat(ls_stkdate, ignore_index = True).drop_duplicates()
        df_stkdate.columns = [i.upper() for i in df_stkdate.columns]
        df_stkdate.rename({'TS_CODE': 'STOCK_CODE', 
                            'TRADE_DATE': 'TRADEDATE',
                            'TURNOVER_RATE': 'TURNOVER',
                            'TURNOVER_RATE_F': 'FTURNOVER',
                            'DV_RATIO': 'DV',
                            'TOTAL_SHARE': 'SHARENUM',
                            'FLOAT_SHARE': 'LSHARENUM',
                            'FREE_SHARE': 'FSHARENUM',
                            'TOTAL_MV': 'MV',
                            'CIRC_MV': 'LMV'}, axis = 1, inplace = True)
        df_stkdate.drop(columns = ['CLOSE', 'VOLUME_RATIO'], inplace = True)
        df_stkdate[['TURNOVER', 'FTURNOVER', 'DV', 'DV_TTM']] /= 100
    else:
        df_stkdate = pd.DataFrame()

    return df_stkdate

if __name__ == '__main__':
    pass