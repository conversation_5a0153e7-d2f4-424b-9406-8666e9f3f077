# -*- coding: utf-8 -*-
# 提取计算商品CTA因子所需要的行情数据

import pandas as pd
from basic import db_vulpes

def get_cfut_mktdata(startdate = ''):
    '''
    提取计算商品CTA因子所需要的行情数据

    Parameters
    ----------
    startdate : str - 行情起始日期
    
    Returns
    -------
    DataFrame
    '''

    conn = db_vulpes()
    sql = '''
    SELECT 
        A.FUT_CODE,
        A.TRADEDATE,
        A.STDCODE,
        A.PRE_CLOSE,
        A.PRE_SETTLE,
        A.OPEN,
        A.HIGH,
        A.LOW,
        A.CLOSE,
        A.SETTLE,
        A.AMOUNT,
        A.VOL,
        A.OI,
        A.DLISTDATE,
        B.MTYPE 
    FROM 
        CFUTURE_DAILYMARKET A
    INNER JOIN 
        CFUTURE_MAINCODE B 
    ON
        A.FUT_CODE = B.FUT_CODE AND
        A.TRADEDATE = B.TRADEDATE
    WHERE
        B.TRADEDATE >= '{}'
    '''.format(startdate)

    df_mktdata = pd.read_sql(sql, con = conn)
    df_mktdata['RETURN'] = df_mktdata['CLOSE'] / df_mktdata['PRE_SETTLE'] - 1
    df_mktdata['SETRET'] = df_mktdata['SETTLE'] / df_mktdata['PRE_SETTLE'] - 1
    df_mktdata['CLSRET'] = df_mktdata['CLOSE'] / df_mktdata['PRE_CLOSE'] - 1
    df_mktdata['STORET'] = df_mktdata['SETTLE'] / df_mktdata['OPEN'] - 1
    df_mktdata['CTORET'] = df_mktdata['CLOSE'] / df_mktdata['OPEN'] - 1

    return df_mktdata

if __name__ == '__main__':
    pass