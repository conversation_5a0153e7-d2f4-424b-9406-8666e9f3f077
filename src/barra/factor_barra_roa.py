# -*- coding: utf-8 -*-

from barra import mod_makeupann

def factor_barra_roa(df_yreport, ls_tdate):
    """
    计算ROA因子

    Computed as, ROA = Earnings / TA, where <PERSON>arnings is the trailing 
    12-month earnings, and TA is the most recently reported total assets.

    Parameters
    ----------
        df_yreport : DataFrame - 年度财务指标表数据
        ls_tdate : List [str] - 交易日序列

    Returns
    -------
    DataFrame
    
    """
    df_yreport['ROA'] = df_yreport['NI'] / df_yreport['TA']
    df_ROA = df_yreport.pivot(index = 'REPORT_DATE', columns = 'STOCK_CODE', values = 'ROA')
    df_ANN = df_yreport.pivot(index = 'REPORT_DATE', columns = 'STOCK_CODE', values = 'DISCLOSE_DATE')

    df_output = mod_makeupann(df_ROA, df_ANN, ls_tdate).stack().reset_index()
    df_output.columns = ['TRADEDATE', 'STOCK_CODE', 'VALUE']

    df_output['FACTOR'] = 'ROA'

    return df_output

if __name__ == '__main__':
    pass