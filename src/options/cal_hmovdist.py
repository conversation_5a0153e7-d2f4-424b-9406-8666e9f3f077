# -*- coding: utf-8 -*-
# 计算ETF期权标的资产绝对涨幅历史概率

import pandas as pd
import numpy as np
from basic.db_conn import db_vulpes

def cal_hmovdist(n_left, ls_pct = [1, 3, 5, 10], wl = 750, hl = 250):
    '''
    计算ETF期权标的资产绝对涨幅历史概率

    Parameters
    ----------
    n_left : int - 绝对涨幅计算天数
    ls_pct : list - 变化百分比阈值
    wl : int - 历史数据回顾区间
    hl : int - 指数加权半衰期
    
    Returns
    -------
    DataFrame
    '''

    conn = db_vulpes()
    sql = '''
    SELECT
        FUND_CODE,
        TRADEDATE,
        RETURN,
        FUND_NAME,
        EXCHANGE
    FROM FOPTION_ETFMARKET
    '''
    df_etfmkt = pd.read_sql(sql, con = conn)
    df_return = df_etfmkt.pivot(index = 'TRADEDATE', columns = 'FUND_CODE', values = 'RETURN')

    a = 0.5 ** (1 / hl)
    eweight = [(1 - a) * (a ** i) for i in np.arange(wl, 0, -1) - 1]
    eweight /= sum(eweight)

    df_absvol = (np.exp(np.log(df_return + 1).rolling(n_left).sum()) - 1).abs().tail(wl)

    df_output = pd.DataFrame(index = df_absvol.columns)

    for pct in ls_pct:
        df_output['{}%'.format(pct)] = ((df_absvol >= (pct / 100)).T.multiply(eweight).T).sum()

    df_output = (df_output * 100).round(2).astype(str) + '%'

    df_output = df_etfmkt.drop(columns = ['TRADEDATE', 'RETURN']).drop_duplicates().set_index('FUND_CODE').join(df_output).reset_index()
    df_output = df_output.sort_values('EXCHANGE', ascending = True).set_index(['EXCHANGE', 'FUND_CODE'])

    return df_output

if __name__ == '__main__':
    pass