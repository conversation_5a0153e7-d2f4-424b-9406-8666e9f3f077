# -*- coding: utf-8 -*-
# 计算时序Dummy动量因子持仓权重

import numpy as np

def factor_cta_tdmom(df_mktdata, value, df_qualified, n_momentum, n_dummy, trg_value = 0):
    '''
    计算时序Dummy动量因子持仓权重

    Parameters
    ----------
    df_mktdata : DataFrame - 日频行情数据
    value : str - 收益率测算目标
    df_qualified : DataFrame - 可以交易品种数据
    n_momentum : int - 动量测算时间[日]
    n_dummy : int - 判断动量的Dummy累计触发次数
    trg_value : float - 动量Dummy触发价格, 默认为0

    Returns
    -------
    DataFrame
    '''
    df_posdummy = (df_mktdata.query('MTYPE == 1').pivot(index = 'TRADEDATE', columns = 'STDCODE', values = value) > trg_value).rolling(n_momentum).sum()
    df_nagdummy = (df_mktdata.query('MTYPE == 1').pivot(index = 'TRADEDATE', columns = 'STDCODE', values = value) < -trg_value).rolling(n_momentum).sum()
    
    df_signal = ((df_posdummy * df_qualified) >= n_dummy) * 1
    df_signal -= ((df_nagdummy * df_qualified) >= n_dummy) * 1
    df_signal.fillna(0, inplace = True)

    se_holdnum = df_signal.apply(np.abs).sum(axis = 1)
    df_holdpct = df_signal.divide(se_holdnum, axis = 0)
    
    return df_holdpct

if __name__ == '__main__':
    pass