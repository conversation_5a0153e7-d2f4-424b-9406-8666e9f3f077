#!/usr/bin/env python
# -*- encoding:utf-8 -*-

from wework_msg import *
from wework_pfrep import *
from wework_pfhold import *
from wework_pfdata import *

def wework_react(msg, name):
    '''
    基于信息进行反应
    '''
    if (msg[0:6] == '<组合台账>') & (name == 'Vulpes'):
        sendmsg_text('收到！报告生成预计3分钟，请稍候……', name)
        if len(msg) > 6:
            todate = msg[6:].replace(' ', '')
        else:
            todate = ''
        send_pfreport(userid = name, partyid = '', todate = todate, current = True, savepng = True)
    elif (msg[0:6] == '<持仓监控>') & (name == 'Vulpes'):
        send_pfhold(userid = name)
    elif (msg[0:6] == '<数据更新>') & (name == 'Vulpes'):
        send_pfdata(userid = name)
        sendmsg_text('数据更新完成！')
    else:
        sendmsg_text('>_< 你们人类的语言太复杂了，我还没学会……', name)
    
if __name__ == '__main__':
    pass