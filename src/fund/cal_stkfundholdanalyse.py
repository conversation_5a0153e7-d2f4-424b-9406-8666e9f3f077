# -*- coding: utf-8 -*-
# 股票型基金四级估值表分析

import pandas as pd
import numpy as np
import os
from basic import get_tradedate, get_indexmkt, get_indexlabel, get_indexweight, db_vulpes

def cal_stkfundholdanalyse(df_rawhold, fund_name: str, benchmark_code: str, benchmark_date: str = None, save: bool = True, savepath: str = None):
    """
    股票型基金四级估值表分析

    Parameters
    ----------
    df_rawhold : DataFrame - 清洗后的四级估值表, 参考cal_stkfundrawhold函数输出
    fund_name : str - 基金名称
    benchmark_code : str - 业绩基准代码
    benchmark_date : str - 业绩基准日期
    save : bool - 是否保存分析结果
    savepath : str - 结果保存路径

    Returns
    -------
    dict - DataFrame
    """
    if not savepath:
        savepath = os.getcwd()

    conn_vulpes = db_vulpes()
    dic_bm = {'000300.SH': '沪深300', '000905.SH': '中证500', '000852.SH': '中证1000', '932000.CSI': '中证2000', '000985.CSI': '中证全指', '000510.CSI': '中证A500'}

    paneldate = df_rawhold['日期'].values[0]
    if paneldate == '':
        paneldate = (pd.Timestamp.now() - pd.Timedelta(days = 1)).strftime('%Y%m%d')

    if benchmark_date is None:
        benchmark_date = paneldate
    tdate_point = get_tradedate(enddate = paneldate, n = 1)[0]
    tdate_mpoint = get_tradedate(enddate = benchmark_date, n = 1, frq = 'M')[0]
    df_indexlabel = get_indexlabel(tdate_mpoint)

    sql = '''
        SELECT INDEX_NAME, STOCK_CODE, 1 AS DUMMY
        FROM INDEX_SWMEMBER
        WHERE
        IN_DATE <= '{}'
        AND (OUT_DATE > '{}' OR OUT_DATE IS NULL)
        '''.format(tdate_point, tdate_point)

    df_industry = pd.read_sql(sql, con = conn_vulpes)
    # df_industry_dummy = df_industry.pivot(index = 'STOCK_CODE', columns = 'INDEX_NAME', values = 'DUMMY').fillna(0).reset_index()
    # df_industry_dummy.columns.name = None
    # ls_industry = df_industry_dummy.columns.tolist()[1:]
    df_industry = df_industry.rename({'INDEX_NAME': '申万一级'}, axis = 1).drop(columns = ['DUMMY']).copy()

    sql = "SELECT * FROM BARRA_FACTORLOAD_L1 WHERE TRADEDATE = '{}'".format(tdate_point)
    df_load = pd.read_sql(sql, conn_vulpes)
    df_style = df_load.pivot(index = 'STOCK_CODE', columns = 'FACTOR_ID', values = 'LOAD').reset_index()
    df_style.columns.name = None
    ls_style = ['BETA', 'SIZE', 'BTOP', 'MOMENTUM', 'GROWTH', 'MIDCAP', 'EARNING', 'LIQUIDITY', 'VOLATILITY', 'LEVERAGE', 'DIVIDEND', 'STREVERSAL', 'EARNINGVAR', 'PROFITABLITY', 'INVENSTMENT']

    df_rawhold_stk = df_rawhold[df_rawhold['资产类型'] == '股票'].copy()
    df_fundhold = df_rawhold_stk.rename({'代码': 'STOCK_CODE'}, axis = 1)\
        .merge(df_indexlabel[['STOCK_CODE', 'INDEX_NAME']], on = 'STOCK_CODE', how = 'left')\
        .merge(df_industry, on = 'STOCK_CODE', how = 'left')\
        .merge(df_style, on = 'STOCK_CODE', how = 'left')\
        .rename({'STOCK_CODE': '代码', 'INDEX_NAME': '成分指数'}, axis = 1)
    df_fundhold['成分指数'] = df_fundhold['成分指数'].fillna('其他')

    hold_stnum = df_fundhold['名称'].str.upper().str.contains('ST').sum()
    hold_utnum = (df_fundhold['停牌信息'] != '正常交易').sum()
    
    dic_sta = {0: {}, 1: {}, -1: {}}
    dic_sta[0]['估值表日期'] = tdate_point
    dic_sta[0]['基准日期'] = tdate_mpoint
    dic_sta[0]['基准指数'] = dic_bm[benchmark_code] if benchmark_code in dic_bm else '无基准'
    dic_sta[0]['ST股数'] = hold_stnum
    dic_sta[0]['非交易股数'] = hold_utnum

    for direction in [1, -1]:
        df_tmp = df_fundhold[df_fundhold['多空方向'] == direction].copy()
        if df_tmp.empty:
            continue
        dic_sta[direction]['持股仓位'] = df_tmp['市值占比'].sum()
        dic_sta[direction]['持股数'] = df_tmp.shape[0]
        dic_sta[direction]['持股市值'] = df_tmp['市值'].sum()
        dic_sta[direction]['前10大持股比例'] = df_tmp['持股权重'].abs().nlargest(10).sum()
        dic_sta[direction]['前20大持股比例'] = df_tmp['持股权重'].abs().nlargest(20).sum()
        dic_sta[direction]['最大持股比例'] = df_tmp['持股权重'].abs().max()
        dic_sta[direction]['最小持股比例'] = df_tmp['持股权重'].abs().min()
        dic_sta[direction]['股数胜率'] = (df_tmp['持仓盈亏'] > 0).sum() / df_tmp.shape[0]
        dic_sta[direction]['权重胜率'] = (df_tmp['持仓盈亏'] > 0).dot(df_tmp['持股权重'] * direction)
        dic_sta[direction]['盈亏比'] = -df_tmp[df_tmp['持仓盈亏'] > 0]['持仓盈亏'].sum() / df_tmp[df_tmp['持仓盈亏'] <= 0]['持仓盈亏'].sum()

    df_info = pd.DataFrame(dic_sta[0], index = [fund_name])
    df_hold_sta = pd.concat([pd.DataFrame(dic_sta[1], index = ['多头持仓']), pd.DataFrame(dic_sta[-1], index = ['融券持仓'])]).dropna(how = 'all').fillna(0).T

    ls_weight = [0, 0.001, 0.0025, 0.005, 0.0075, 0.01, 0.02, 0.03, 0.05]
    df_hold_weight = pd.DataFrame({'权重区间': ['{}%以上'.format(w * 100) for w in ls_weight], 
                                '数量': [(df_fundhold['持股权重'].abs() >= i).sum() for i in ls_weight], 
                                '持股权重': [df_fundhold[(df_fundhold['持股权重'].abs() >= i)]['持股权重'].abs().sum() for i in ls_weight]})
    df_hold_weight['持股权重'] = df_hold_weight['持股权重'] / df_fundhold['持股权重'].abs().sum()
    
    df_hold_cumweight = df_hold_weight.copy()
    df_hold_cumweight['数量权重'] = df_hold_cumweight['数量'] / df_fundhold.shape[0]
    df_hold_weight['权重区间'] = ['{}%-{}%'.format(ls_weight[i] * 100, ls_weight[i + 1] * 100) for i in range(len(ls_weight) - 1)] + ['{}%以上'.format(ls_weight[-1] * 100)]
    df_hold_weight['数量'] = df_hold_weight['数量'].diff(-1).fillna(df_hold_weight['数量'].values[-1])
    df_hold_weight['持股权重'] = df_hold_weight['持股权重'].diff(-1).fillna(df_hold_weight['持股权重'].values[-1])
    df_hold_weight['数量权重'] = df_hold_weight['数量'] / df_hold_weight['数量'].sum()

    df_hold_indexnum = df_fundhold.groupby('成分指数')['持股权重'].count().to_frame('持股数量').reset_index()
    df_hold_indexnum['数量权重'] = df_hold_indexnum['持股数量'] / df_hold_indexnum['持股数量'].sum()
    df_hold_absindexweight = df_fundhold.groupby('成分指数')['持股权重'].sum().sort_values(ascending = False).reset_index().rename(columns = {'持股权重': '净持股权重'})
    df_hold_indexweight = df_fundhold.groupby('成分指数')['市值占比'].sum().sort_values(ascending = False).reset_index().rename(columns = {'市值占比': '持股权重'})
    df_hold_indexweight['持股权重'] /= df_hold_indexweight['持股权重'].sum()

    df_hold_index = df_hold_indexnum.merge(df_hold_absindexweight, on = '成分指数', how = 'left').merge(df_hold_indexweight, on = '成分指数', how = 'left')

    df_hold_industry = df_fundhold.groupby('申万一级')['持股权重'].sum().sort_values(ascending = False).reset_index()
    df_hold_style = df_fundhold['持股权重'].dot(df_fundhold[ls_style].fillna(0)).to_frame('风格敞口').rename_axis('风格因子').reset_index()

    ls_trange = get_tradedate(enddate = tdate_point, n = 21)[: -1] + get_tradedate(startdate = tdate_point, n = 21)
    sql = '''
        SELECT STOCK_CODE, TRADEDATE, RETURN
        FROM STOCK_DAILYMARKET 
        WHERE 
        TRADEDATE >= '{}' AND 
        TRADEDATE <= '{}'
        '''.format(ls_trange[0], ls_trange[-1])
    df_rawreturn = pd.read_sql(sql, conn_vulpes)
    df_stockreturn = df_rawreturn.pivot(index = 'TRADEDATE', columns = 'STOCK_CODE', values = 'RETURN').fillna(0)
    df_logreturn = np.log(df_stockreturn + 1)

    if benchmark_code in dic_bm.keys():
        df_bmhold = get_indexweight(tdate_mpoint, benchmark_code)
        df_indexmkt = get_indexmkt(benchmark_code)
        df_bmreturn = df_indexmkt[df_indexmkt.index.isin(df_stockreturn.index)][['RETURN']]
        df_info['基准占比'] = df_bmhold.merge(df_rawhold_stk.rename({'代码': 'STOCK_CODE'}, axis = 1), how = 'left')['持股权重'].sum()
    else:
        df_info['基准占比'] = 0
        benchmark_code = ''
        df_bmhold = pd.DataFrame({'STOCK_CODE': df_fundhold['代码'].unique(),
                                  'TRADEDATE': tdate_mpoint,
                                  'INDEX_CODE': benchmark_code,
                                  'WEIGHT': 0})
        df_bmreturn = pd.DataFrame({'RETURN': [0] * df_stockreturn.shape[0]}, index = df_stockreturn.index)
    
    df_benchmark = df_bmhold.merge(df_industry, on = 'STOCK_CODE', how = 'left').merge(df_style, on = 'STOCK_CODE', how = 'left')
    df_bm_industry = df_benchmark.groupby('申万一级')['WEIGHT'].sum().sort_values(ascending = False).reset_index().rename(columns = {'WEIGHT': '基准权重'})
    df_bm_style = df_benchmark['WEIGHT'].dot(df_benchmark[ls_style].fillna(0)).to_frame('基准敞口').rename_axis('风格因子').reset_index()
    df_hold_industry = df_hold_industry.merge(df_bm_industry, on = '申万一级', how = 'left').fillna(0)
    df_hold_industry['行业偏离'] = df_hold_industry['持股权重'] - df_hold_industry['基准权重']
    df_hold_style = df_hold_style.merge(df_bm_style, on = '风格因子', how = 'left')
    df_hold_style['风格偏离'] = df_hold_style['风格敞口'] - df_hold_style['基准敞口']

    df_logexec = df_logreturn.sub(np.log(df_bmreturn + 1).values)

    ls_twindow = [1, 3, 5, 10, 15, 20]
    df_rangereturn = pd.DataFrame({'STOCK_CODE': df_stockreturn.columns}).set_index('STOCK_CODE')
    df_rangeexec = df_rangereturn.copy()
    # ls_twindow.reverse()
    # for tw in ls_twindow:
    #     df_rangereturn['-{}D'.format(tw)] = np.exp(df_logreturn.rolling(tw).sum()).loc[tdate_point] - 1
    #     df_rangeexec['-{}D'.format(tw)] = np.exp(df_logexec.rolling(tw).sum()).loc[tdate_point] - 1
    # ls_twindow.reverse()
    for tw in ls_twindow:
        df_rangereturn['{}D'.format(tw)] = np.exp(df_logreturn.rolling(tw).sum().shift(-tw)).loc[tdate_point] - 1
        df_rangeexec['{}D'.format(tw)] = np.exp(df_logexec.rolling(tw).sum().shift(-tw)).loc[tdate_point] - 1

    df_rangereturn.reset_index(inplace = True)
    df_rangeexec.reset_index(inplace = True)

    df_holdreturn = df_rawhold_stk.merge(df_rangereturn.rename({'STOCK_CODE': '代码'}, axis = 1), on = '代码', how = 'left')
    df_holdexec = df_rawhold_stk.merge(df_rangeexec.rename({'STOCK_CODE': '代码'}, axis = 1), on = '代码', how = 'left')

    ls_twcol = ['{}D'.format(tw) for tw in ls_twindow]
    df_holdic = pd.DataFrame({'持仓时间':ls_twcol}).set_index('持仓时间')
    df_holdic['全持仓'] = df_holdexec[['持股权重'] + ls_twcol].corr().loc[ls_twcol]['持股权重']
    df_holdic['前50%'] = df_holdexec[df_holdexec['持股权重'] > df_holdexec['持股权重'].quantile(0.5)][['持股权重'] + ls_twcol].corr().loc[ls_twcol]['持股权重']
    df_holdic['后50%'] = df_holdexec[df_holdexec['持股权重'] < df_holdexec['持股权重'].quantile(0.5)][['持股权重'] + ls_twcol].corr().loc[ls_twcol]['持股权重']
    df_holdic['前25%'] = df_holdexec[df_holdexec['持股权重'] > df_holdexec['持股权重'].quantile(0.75)][['持股权重'] + ls_twcol].corr().loc[ls_twcol]['持股权重']
    df_holdic['前25-50%'] = df_holdexec[df_holdexec['持股权重'].between(df_holdexec['持股权重'].quantile(0.5), df_holdexec['持股权重'].quantile(0.75))][['持股权重'] + ls_twcol].corr().loc[ls_twcol]['持股权重']
    df_holdic['后25-50%'] = df_holdexec[df_holdexec['持股权重'].between(df_holdexec['持股权重'].quantile(0.25), df_holdexec['持股权重'].quantile(0.5))][['持股权重'] + ls_twcol].corr().loc[ls_twcol]['持股权重']
    df_holdic['后25%'] = df_holdexec[df_holdexec['持股权重'] < df_holdexec['持股权重'].quantile(0.25)][['持股权重'] + ls_twcol].corr().loc[ls_twcol]['持股权重']
    df_holdic.fillna(0, inplace = True)
    
    df_holdric = pd.DataFrame({'持仓时间':ls_twcol}).set_index('持仓时间')
    df_holdric['全持仓'] = df_holdexec[['持股权重'] + ls_twcol].rank(pct = True, ascending = False).corr().loc[ls_twcol]['持股权重']
    df_holdric['前50%'] = df_holdexec[df_holdexec['持股权重'] > df_holdexec['持股权重'].quantile(0.5)][['持股权重'] + ls_twcol].rank(pct = True, ascending = False).corr().loc[ls_twcol]['持股权重']
    df_holdric['后50%'] = df_holdexec[df_holdexec['持股权重'] < df_holdexec['持股权重'].quantile(0.5)][['持股权重'] + ls_twcol].rank(pct = True, ascending = False).corr().loc[ls_twcol]['持股权重']
    df_holdric['前25%'] = df_holdexec[df_holdexec['持股权重'] > df_holdexec['持股权重'].quantile(0.75)][['持股权重'] + ls_twcol].rank(pct = True, ascending = False).corr().loc[ls_twcol]['持股权重']
    df_holdric['前25-50%'] = df_holdexec[df_holdexec['持股权重'].between(df_holdexec['持股权重'].quantile(0.5), df_holdexec['持股权重'].quantile(0.75))][['持股权重'] + ls_twcol].rank(pct = True, ascending = False).corr().loc[ls_twcol]['持股权重']
    df_holdric['后25-50%'] = df_holdexec[df_holdexec['持股权重'].between(df_holdexec['持股权重'].quantile(0.25), df_holdexec['持股权重'].quantile(0.5))][['持股权重'] + ls_twcol].rank(pct = True, ascending = False).corr().loc[ls_twcol]['持股权重']
    df_holdric['后25%'] = df_holdexec[df_holdexec['持股权重'] < df_holdexec['持股权重'].quantile(0.25)][['持股权重'] + ls_twcol].rank(pct = True, ascending = False).corr().loc[ls_twcol]['持股权重']
    df_holdric.fillna(0, inplace = True)

    df_predict = pd.DataFrame({'持仓时间':ls_twcol}).set_index('持仓时间')
    df_absholdreturn = df_holdreturn[ls_twcol].mul(df_holdreturn['多空方向'], axis = 0)
    df_predict['持股数量胜率'] = (df_absholdreturn > 0).sum(axis = 0) / df_fundhold.shape[0]
    df_predict['持股权重胜率'] = df_holdreturn['持股权重'].abs().dot(df_absholdreturn > 0) / df_holdreturn['持股权重'].abs().sum()
    df_predict['持股盈亏比'] = -df_holdreturn['持股权重'].abs().dot(df_absholdreturn * (df_absholdreturn > 0)) /\
        df_holdreturn['持股权重'].abs().dot(df_absholdreturn * (df_absholdreturn <= 0))
    
    df_absholdexec = df_holdexec[ls_twcol].mul(df_holdexec['多空方向'], axis = 0)
    df_predict['超额数量胜率'] = (df_absholdexec > 0).sum(axis = 0) / df_fundhold.shape[0]
    df_predict['超额权重胜率'] = df_holdexec['持股权重'].abs().dot(df_absholdexec > 0) / df_holdexec['持股权重'].abs().sum()
    df_predict['超额盈亏比'] = -df_holdexec['持股权重'].abs().dot(df_absholdexec * (df_absholdexec > 0)) /\
        df_holdexec['持股权重'].abs().dot(df_absholdexec * (df_absholdexec <= 0))
    df_predict.fillna(0, inplace = True)

    # FFutures
    df_rawhold_ffut = df_rawhold[df_rawhold['资产类型'] == '股指期货'].copy()
    df_rawhold_ffut['合约类型'] = df_rawhold_ffut['代码'].str[0: 2]
    
    df_ffut_sta = pd.DataFrame()
    dic_sta_ffut = {1: {}, -1: {}}
    for direction in [1, -1]:
        df_tmp = df_rawhold_ffut[df_rawhold_ffut['多空方向'] == direction].copy()
        if df_tmp.empty:
            continue
        dic_sta_ffut[direction]['合约仓位'] = df_tmp['市值占比'].sum()
        dic_sta_ffut[direction]['合约市值'] = df_tmp['市值'].sum()
        dic_sta_ffut[direction]['合约类型'] = ' '.join(df_tmp['合约类型'].unique().tolist())
        dic_sta_ffut[direction]['最大持仓市值'] = df_tmp['市值'].abs().max() * direction
        dic_sta_ffut[direction]['最大持仓比例'] = df_tmp['市值'].abs().max() / df_tmp['市值'].sum()
        dic_sta_ffut[direction]['最大持仓合约'] = df_tmp[df_tmp['市值'] == dic_sta_ffut[direction]['最大持仓市值']]['代码'].values[0]
    df_ffut_sta = pd.concat([pd.DataFrame(dic_sta_ffut[1], index = ['多头合约']), pd.DataFrame(dic_sta_ffut[-1], index = ['空头合约'])]).dropna(how = 'all').T

    df_ffut_ref = pd.DataFrame({'合约类型': ['IH', 'IF', 'IC', 'IM'], '指数代码': ['000016.SH', '000300.SH', '000905.SH', '000852.SH']})
    df_ffut_bm = df_rawhold_ffut.groupby('合约类型')[['市值']].sum().reset_index()
    df_ffut_bm = df_ffut_bm.merge(df_ffut_ref, on = '合约类型', how = 'left')
    df_ffut_bm['权重'] = df_ffut_bm['市值'] / df_rawhold_stk.query('多空方向 == 1')['市值'].sum()
    
    ls_ffut = df_ffut_bm['指数代码'].unique().tolist()
    df_hold_industry['综合敞口'] = df_hold_industry['持股权重']
    df_hold_industry['综合偏离'] = df_hold_industry['行业偏离']
    df_hold_style['综合敞口'] = df_hold_style['风格敞口']
    df_hold_style['综合偏离'] = df_hold_style['风格偏离']
    for benchmark_code in ls_ffut:
        ff_name = df_ffut_bm[df_ffut_bm['指数代码'] == benchmark_code]['合约类型'].values[0]
        ff_weight = df_ffut_bm[df_ffut_bm['指数代码'] == benchmark_code]['权重'].values[0]

        df_ffbmhold = get_indexweight(tdate_mpoint, benchmark_code)
        df_ffbmhold = df_ffbmhold.merge(df_industry, on = 'STOCK_CODE', how = 'left').merge(df_style, on = 'STOCK_CODE', how = 'left')
        df_ffbm_industry = df_ffbmhold.groupby('申万一级')['WEIGHT'].sum().sort_values(ascending = False).reset_index().rename(columns = {'WEIGHT': ff_name})
        df_ffbm_style = df_ffbmhold['WEIGHT'].dot(df_ffbmhold[ls_style].fillna(0)).to_frame(ff_name).rename_axis('风格因子').reset_index()
        
        df_hold_industry = df_hold_industry.merge(df_ffbm_industry, on = '申万一级', how = 'left').fillna(0)
        df_hold_style = df_hold_style.merge(df_ffbm_style, on = '风格因子', how = 'left')

        df_hold_industry['综合敞口'] += df_hold_industry[ff_name] * ff_weight
        df_hold_style['综合敞口'] += df_hold_style[ff_name] * ff_weight

        df_hold_industry['综合偏离'] += (df_hold_industry[ff_name] - df_hold_industry['基准权重']) * ff_weight
        df_hold_style['综合偏离'] += (df_hold_style[ff_name] - df_hold_style['基准敞口']) * ff_weight


    if save:
        writer = pd.ExcelWriter(os.path.join(savepath, '{}_{}.xlsx'.format(fund_name, tdate_point)))
        df_info.to_excel(excel_writer = writer, sheet_name = '基本信息')
        df_hold_sta.to_excel(excel_writer = writer, sheet_name = '多空持仓统计')
        df_hold_weight.to_excel(excel_writer = writer, sheet_name = '持仓权重分布', index = False)
        df_hold_cumweight.to_excel(excel_writer = writer, sheet_name = '持仓累计权重分布', index = False)
        df_hold_index.to_excel(excel_writer = writer, sheet_name = '持仓指数分布', index = False)
        df_hold_industry.to_excel(excel_writer = writer, sheet_name = '持仓行业分布', index = False)
        df_hold_style.to_excel(excel_writer = writer, sheet_name = '持仓风格分布', index = False)
        df_holdic.to_excel(excel_writer = writer, sheet_name = '持股IC')
        df_holdric.to_excel(excel_writer = writer, sheet_name = '持股RankIC')
        df_predict.to_excel(excel_writer = writer, sheet_name = '持股胜率')
        df_ffut_sta.to_excel(excel_writer = writer, sheet_name = '期指持仓统计')
        writer.close()

    return {'info': df_info,
            'sta': df_hold_sta, 
            'weight': df_hold_weight, 
            'cumweight': df_hold_cumweight, 
            'index': df_hold_index, 
            'industry': df_hold_industry, 
            'style': df_hold_style, 
            'ic': df_holdic, 
            'ric': df_holdric,
            'predict': df_predict,
            'sta_ffut': df_ffut_sta,
            'rawhold_stock': df_rawhold_stk,
            'rawhold_ffuture': df_rawhold_ffut
            }

if __name__ == '__main__':
    pass
