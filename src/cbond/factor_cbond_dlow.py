# -*- coding: utf-8 -*-
# 计算转债双低策略收益率

import pandas as pd
from basic import get_tradedate

def factor_cbond_dlow(df_mktdata, n_hold = 20, reset = 5, startdate = '20190101'):
    '''
    计算转债双低策略收益率

    Parameters
    ----------
    df_mktdata : DataFrame - 日频行情数据
    n_hold : int - 双低持仓转债数
    reset : int - 策略换仓周期
    startdate : str - 策略开始日期, YYYYMMDD
    conn : DB_Engine - 数据库链接引擎
    
    Returns
    -------
    DataFrame
    '''
    # 计算前一日的双低指标
    df_dlow = df_mktdata.pivot(index = 'TRADEDATE', columns = 'CBOND_CODE', values = 'DLOW')
    df_predlow = df_dlow.shift(1).stack().to_frame(name = 'PRE_DLOW').reset_index()
    df_cbondmkt = df_mktdata.merge(df_predlow, on = ['TRADEDATE', 'CBOND_CODE'], how = 'inner')

    # 确定换仓日期
    ls_tdate = get_tradedate(startdate = startdate, enddate = df_cbondmkt['TRADEDATE'].max())
    ls_sdate = [ls_tdate[i] for i in range(0, len(ls_tdate), reset)]
    df_sdate = pd.DataFrame({'TRADEDATE': ls_tdate}).merge(pd.DataFrame({'TRADEDATE': ls_sdate, 'RESETDATE': ls_sdate}), on = 'TRADEDATE', how = 'left').ffill()

    df_cbondmkt = df_cbondmkt[df_cbondmkt['TRADEDATE'] >= startdate].copy()
    # 换仓日成交量10000手以上的转债，并以双低指标排序
    df_cbondmkt_reset = df_cbondmkt[(df_cbondmkt['VOL'] < 10000) & (df_cbondmkt['TRADEDATE'].isin(ls_sdate))].copy()
    df_cbondmkt_reset['PDL_RANK'] = df_cbondmkt_reset.groupby('TRADEDATE').rank(method = 'min')['PRE_DLOW']

    # 根据持仓数计算持仓各转债权重
    df_weight = (df_cbondmkt_reset[df_cbondmkt_reset['PDL_RANK'] <= n_hold].pivot(index = 'TRADEDATE', columns = 'CBOND_CODE', values = 'PDL_RANK') * 0 + 1).fillna(0)
    df_hold = pd.DataFrame(index = ls_tdate).join(df_weight.div(df_weight.sum(axis = 1), axis = 0)).ffill().replace(0, None).stack().to_frame().reset_index()
    df_hold.columns = ['TRADEDATE', 'CBOND_CODE', 'WEIGHT']
    # 计算持仓转债在每个持有周期内的累计收益率
    df_holdret = df_cbondmkt[['CBOND_CODE', 'TRADEDATE', 'RETURN']].merge(df_hold, on = ['TRADEDATE', 'CBOND_CODE'], how = 'right').merge(df_sdate, on = 'TRADEDATE', how = 'left').fillna(0)
    df_holdret['CUMRET'] = df_holdret['RETURN'] + 1
    df_holdret['CUMRET'] = df_holdret.sort_values('TRADEDATE').groupby(['CBOND_CODE', 'RESETDATE'])[['CUMRET']].cumprod()
    df_holdret['WCUMRET'] = df_holdret['WEIGHT'] * df_holdret['CUMRET']
    # 计算各持仓转债的每日组合收益贡献
    df_holdweight = df_holdret.pivot(index = 'TRADEDATE', columns = 'CBOND_CODE', values = 'CUMRET').shift(1)
    df_holdweight.loc[ls_sdate] = df_holdret.pivot(index = 'TRADEDATE', columns = 'CBOND_CODE', values = 'CUMRET').loc[ls_sdate] * 0 + 1
    df_holdret = df_holdret.merge(df_holdweight.div(df_holdweight.sum(axis = 1), axis = 0).stack().to_frame('HOLD_WEIGHT').reset_index(), on = ['TRADEDATE', 'CBOND_CODE'])
    df_holdret['HOLD_RETURN'] = df_holdret['HOLD_WEIGHT'] * df_holdret['RETURN']

    # 根据持仓权重计算组合收益率
    df_factor_ret = df_holdret.groupby('TRADEDATE')[['HOLD_RETURN']].sum().reset_index().rename({'HOLD_RETURN': 'RETURN'}, axis = 1)
    df_factor_hold = df_holdret[['CBOND_CODE', 'TRADEDATE', 'RESETDATE', 'HOLD_WEIGHT', 'HOLD_RETURN']].rename({'CBOND_CODE': 'HOLD_CODE'}, axis = 1).copy()
    df_factor_hold['STDCODE'] = 'CBOND'

    return df_factor_ret, df_factor_hold

if __name__ == '__main__':
    pass