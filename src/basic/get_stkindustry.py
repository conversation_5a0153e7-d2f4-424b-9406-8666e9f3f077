# -*- coding: utf-8 -*-
# 提取截面个股行业代码

import pandas as pd
from basic import db_vulpes, get_tradedate

def get_stkindustry():
    """
    提取截面个股行业代码

    Parameters
    ----------
    None

    Returns
    -------
    DataFrame
        index : 日期
        columns : WIND股票代码

    """
    conn = db_vulpes()
    sql = '''
    SELECT 
        INDEX_CODE, 
        STOCK_CODE, 
        IN_DATE,
        OUT_DATE
    FROM INDEX_SWMEMBER
    '''
    df_date = pd.read_sql(sql, con = conn).drop_duplicates()

    df_dup_in = df_date.groupby(['STOCK_CODE', 'IN_DATE']).count().query('INDEX_CODE > 1').reset_index()[['STOCK_CODE', 'IN_DATE']]
    df_dup_in['DUP_IN'] = 1
    df_dup_out = df_date.groupby(['STOCK_CODE', 'OUT_DATE']).count().query('INDEX_CODE > 1').reset_index()[['STOCK_CODE', 'OUT_DATE']]
    df_dup_out['DUP_OUT'] = 1
    df_filter = df_date.merge(df_dup_in, on = ['STOCK_CODE', 'IN_DATE'], how = 'left').merge(df_dup_out, on = ['STOCK_CODE', 'OUT_DATE'], how = 'left')

    df_filter['QUAL'] = 1
    df_filter.loc[(df_filter['DUP_OUT'] == 1) & (~df_filter['INDEX_CODE'].isin(['801960.SI', '801970.SI', '801980.SI'])), 'QUAL'] = 0
    df_filter.loc[(df_filter['DUP_IN'] == 1) & (df_filter['OUT_DATE'] == '20211210'), 'QUAL'] = 0
    df_filter.loc[(df_filter['DUP_IN'] == 1) & (~df_filter['OUT_DATE'].isna()) & (~df_filter['INDEX_CODE'].isin(['801960.SI', '801970.SI', '801980.SI'])), 'QUAL'] = 0

    df_indin = df_filter.query('QUAL == 1')[['IN_DATE', 'STOCK_CODE', 'INDEX_CODE']].drop_duplicates().pivot(index ='IN_DATE', columns = 'STOCK_CODE', values = 'INDEX_CODE')
    df_output = pd.DataFrame(index = get_tradedate(startdate = df_indin.index[0])).join(df_indin, how = 'left').ffill()

    return df_output

if __name__ == '__main__':
    pass