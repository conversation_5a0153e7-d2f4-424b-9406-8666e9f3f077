# -*- coding: utf-8 -*-

def factor_barra_etop(df_dind, ls_tdate):
    """
    计算ETOP因子

    Computed by dividing the trailing 12-month earnings by 
    the current market capitalization.

    Parameters
    ----------
        df_dind : DataFrame - 日频股票数据
        ls_tdate : List [str] - 交易日序列

    Returns
    -------
    DataFrame

    """
    df_output = df_dind[df_dind['TRADEDATE'].isin(ls_tdate)][['TRADEDATE', 'STOCK_CODE', 'PE_TTM']].copy()
    df_output['PE_TTM'] = 1 / df_output['PE_TTM']
    df_output.columns = ['TRADEDATE', 'STOCK_CODE', 'VALUE']

    df_output['FACTOR'] = 'ETOP'

    return df_output

if __name__ == '__main__':
    pass