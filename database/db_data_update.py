# -*- coding: utf-8 -*-
import sys
import os

sys.path.append(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))

from threading import Thread
import argparse
from src.basic import *
from src.options import cal_hvolcone
from src.data import *
from src.fund import cal_stkfundscore
from wework.wework_msg import *

import pandas as pd
from datetime import date, datetime
from tqdm import tqdm

def update_tradedate(conn_from, conn_to):
    '''
    [BASIC_TRADEDATE]
    '''
    try:
        df_tdate = download_tradedate(conn_from)
        maxdate = df_tdate['CAL_DATE'].max()
    except:
        sendmsg_text(content = '[BASIC_TRADEDATE]: Source access failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
        raise SystemError('[BASIC_TRADEDATE]: Source access failed.')

    try:
        startdate = pd.read_sql('SELECT MAX(CAL_DATE) AS CAL_DATE FROM BASIC_TRADEDATE', con = conn_to)['CAL_DATE'].values[0]
    except:
        sendmsg_text(content = '[BASIC_TRADEDATE]: Table connect failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
        raise SystemError('[BASIC_TRADEDATE]: Table connect failed.')

    try:
        if not startdate:
            df_tdate.to_sql(name = 'BASIC_TRADEDATE', con = conn_to, index = False, if_exists = 'append', chunksize = 10000)
            print('[BASIC_TRADEDATE]: Update to {}.'.format(maxdate))
        elif maxdate > startdate:
            df_tdate[df_tdate['CAL_DATE'] > startdate].to_sql(name = 'BASIC_TRADEDATE', con = conn_to, index = False, if_exists = 'append', chunksize = 10000)
            print('[BASIC_TRADEDATE]: Update to {}.'.format(maxdate))
        else:
            print('[BASIC_TRADEDATE]: No data to update.')
    except:
        sendmsg_text(content = '[BASIC_TRADEDATE]: Update failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
        raise SystemError('[BASIC_TRADEDATE]: Update failed.')
    
    return

def update_index_dailymarket(enddate, conn_from, conn_to):
    '''
    [INDEX_DAILYMARKET]
    '''
    try:
        startdate = pd.read_sql('SELECT MAX(TRADEDATE) AS TRADEDATE FROM INDEX_DAILYMARKET', con = conn_to)['TRADEDATE'].values[0]
    except:
        sendmsg_text(content = '[INDEX_DAILYMARKET]: Table connect failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
        raise SystemError('[INDEX_DAILYMARKET]: Table connect failed.')
    
    if not startdate:
        startdate = ''
    else:
        startdate = get_tradedate(startdate = startdate)[1]

    if enddate >= startdate:
        try:
            df_dmarket = download_index_dailymarket(startdate, enddate, conn_from)
        except:
            sendmsg_text(content = '[INDEX_DAILYMARKET]: Source access failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
            raise SystemError('[INDEX_DAILYMARKET]: Source access failed.')
        try:
            df_dmarket.to_sql(name = 'INDEX_DAILYMARKET', con = conn_to, index = False, if_exists = 'append', chunksize = 10000)
            raw_conn = conn_to.raw_connection()
            cursor = raw_conn.cursor()
            cursor.execute('OPTIMIZE TABLE INDEX_DAILYMARKET FINAL DEDUPLICATE')
            raw_conn.commit()
        except:
            sendmsg_text(content = '[INDEX_DAILYMARKET]: Update failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
            raise SystemError('[INDEX_DAILYMARKET]: Update failed.')

        if df_dmarket.shape[0] > 0:
            newdate = df_dmarket['TRADEDATE'].max()
            print('[INDEX_DAILYMARKET]: Update to {}.'.format(newdate))
        else:
            print('[INDEX_DAILYMARKET]: No data received, check source.')
    else:
        print('[INDEX_DAILYMARKET]: No data to update.')

    return

def update_index_consweight(endmdate, conn_from, conn_to):
    '''
    [INDEX_CONSWEIGHT]
    '''
    try:
        startdate = pd.read_sql('SELECT MAX(TRADEDATE) AS TRADEDATE FROM INDEX_CONSWEIGHT', con = conn_to)['TRADEDATE'].values[0]
    except:
        sendmsg_text(content = '[INDEX_CONSWEIGHT]: Table connect failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
        raise SystemError('[INDEX_CONSWEIGHT]: Table connect failed.')
    
    if not startdate:
        startdate = ''
    else:
        startdate = get_tradedate(startdate = startdate)[1]

    if endmdate >= startdate:
        ls_trange = get_tradedate(startdate = startdate, enddate = endmdate, frq = 'M')
        for i in tqdm(range(len(ls_trange) // 12 + 1), desc = '[INDEX_CONSWEIGHT]'):
            if i * 12 == len(ls_trange):
                continue
            range_start = ls_trange[i * 12]
            if i == len(ls_trange) // 12:
                range_end = ls_trange[-1]
            else:
                range_end = ls_trange[(i + 1) * 12 - 1]
                
            try:
                df_weight = download_index_consweight(range_start, range_end, conn_from)
            except:
                sendmsg_text(content = '[INDEX_CONSWEIGHT]: Source access failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
                raise SystemError('[INDEX_CONSWEIGHT]: Source access failed.')
            try:
                df_weight.to_sql(name = 'INDEX_CONSWEIGHT', con = conn_to, index = False, if_exists = 'append', chunksize = 10000)
                raw_conn = conn_to.raw_connection()
                cursor = raw_conn.cursor()
                cursor.execute('OPTIMIZE TABLE INDEX_CONSWEIGHT FINAL DEDUPLICATE')
                raw_conn.commit()
            except:
                sendmsg_text(content = '[INDEX_CONSWEIGHT]: Update failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
                raise SystemError('[INDEX_CONSWEIGHT]: Update failed.')

        if df_weight.shape[0] > 0:
            newdate = df_weight['TRADEDATE'].max()
            print('[INDEX_CONSWEIGHT]: Update to {}.'.format(newdate))
        else:
            print('[INDEX_CONSWEIGHT]: No data received, check source.')
    else:
        print('[INDEX_CONSWEIGHT]: No data to update.')

    return

def update_index_swmember(conn_from, conn_to):
    '''
    [INDEX_SWMEMBER]
    '''
    raw_conn = conn_to.raw_connection()
    cursor = raw_conn.cursor()
    try:
        num_records = pd.read_sql('SELECT COUNT(CREATE_TIME) AS RECORDS FROM INDEX_SWMEMBER', con = conn_to)['RECORDS'].values[0]
    except:
        sendmsg_text(content = '[INDEX_SWMEMBER]: Table connect failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
        raise SystemError('[INDEX_SWMEMBER]: Table connect failed.')
    
    if not num_records:
        num_records = 0

    try:
        df_swmember = download_index_swmember(conn_from)
    except:
        sendmsg_text(content = '[INDEX_SWMEMBER]: Source access failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
        raise SystemError('[INDEX_SWMEMBER]: Source access failed.')

    if num_records == 0:
        try:
            df_swmember.to_sql(name = 'INDEX_SWMEMBER', con = conn_to, index = False, if_exists = 'append', chunksize = 10000)
            raw_conn = conn_to.raw_connection()
            cursor = raw_conn.cursor()
            cursor.execute('OPTIMIZE TABLE INDEX_SWMEMBER FINAL DEDUPLICATE')
            raw_conn.commit()
            print('[INDEX_SWMEMBER]: {} records added.'.format(df_swmember.shape[0] - num_records))
        except:
            sendmsg_text(content = '[INDEX_SWMEMBER]: Update failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
            raise SystemError('[INDEX_SWMEMBER]: Update failed.')
    elif df_swmember.shape[0] >= num_records:
        sql = 'TRUNCATE TABLE INDEX_SWMEMBER'
        cursor.execute(sql)
        df_swmember.to_sql(name = 'INDEX_SWMEMBER', con = conn_to, index = False, if_exists = 'append', chunksize = 10000)
        print('[INDEX_SWMEMBER]: {} records added.'.format(df_swmember.shape[0] - num_records))
    else:
        print('[INDEX_SWMEMBER]: No data to update.')
    
    raw_conn.commit()
    return

def update_foption_volatilitycone(enddate, conn_to):
    '''
    [FOPTION_VOLATILITYCONE]
    '''
    raw_conn = conn_to.raw_connection()
    cursor = raw_conn.cursor()
    try:
        num_records = pd.read_sql('SELECT COUNT(CREATE_TIME) AS RECORDS FROM FOPTION_VOLATILITYCONE', con = conn_to)['RECORDS'].values[0]
    except:
        sendmsg_text(content = '[FOPTION_VOLATILITYCONE]: Table connect failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
        raise SystemError('[FOPTION_VOLATILITYCONE]: Table connect failed.')
    
    if not num_records:
        num_records = 0

    try:
        df_volcone = cal_hvolcone(enddate = enddate)
    except:
        sendmsg_text(content = '[FOPTION_VOLATILITYCONE]: Data calculating failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
        raise SystemError('[FOPTION_VOLATILITYCONE]: Data calculating failed.')

    if num_records > 0:
        sql = 'TRUNCATE TABLE FOPTION_VOLATILITYCONE'
        cursor.execute(sql)
        raw_conn.commit()

    try:
        df_volcone.to_sql(name = 'FOPTION_VOLATILITYCONE', con = conn_to, index = False, if_exists = 'append', chunksize = 10000)
        print('[FOPTION_VOLATILITYCONE]: Data refreshed.')
    except:
        sendmsg_text(content = '[FOPTION_VOLATILITYCONE]: Update failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
        raise SystemError('[FOPTION_VOLATILITYCONE]: Update failed.')
    
    return

def update_stock_dailymarket(enddate, conn_from, conn_to):
    '''
    [STOCK_DAILYMARKET]
    '''
    try:
        startdate = pd.read_sql('SELECT MAX(TRADEDATE) AS TRADEDATE FROM STOCK_DAILYMARKET', con = conn_to)['TRADEDATE'].values[0]
    except:
        sendmsg_text(content = '[STOCK_DAILYMARKET]: Table connect failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
        raise SystemError('[STOCK_DAILYMARKET]: Table connect failed.')
    
    if not startdate:
        startdate = ''
    else:
        startdate = get_tradedate(startdate = startdate)[1]

    if enddate >= startdate:
        ls_trange = get_tradedate(startdate = startdate, enddate = enddate)
        for i in tqdm(range(len(ls_trange) // 120 + 1), desc = '[STOCK_DAILYMARKET]'):
            if i * 120 == len(ls_trange):
                continue
            range_start = ls_trange[i * 120]
            if i == len(ls_trange) // 120:
                range_end = ls_trange[-1]
            else:
                range_end = ls_trange[(i + 1) * 120 - 1]
            
            try:
                df_dmarket = download_stock_dailymarket(range_start, range_end, conn_from)
            except:
                sendmsg_text(content = '[STOCK_DAILYMARKET]: Source access failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
                raise SystemError('[STOCK_DAILYMARKET]: Source access failed.')
            try:
                df_dmarket.to_sql(name = 'STOCK_DAILYMARKET', con = conn_to, index = False, if_exists = 'append', chunksize = 10000)
                raw_conn = conn_to.raw_connection()
                cursor = raw_conn.cursor()
                cursor.execute('OPTIMIZE TABLE STOCK_DAILYMARKET FINAL DEDUPLICATE')
                raw_conn.commit()
            except:
                sendmsg_text(content = '[STOCK_DAILYMARKET]: Update failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
                raise SystemError('[STOCK_DAILYMARKET]: Update failed.')

        if df_dmarket.shape[0] > 0:
            newdate = df_dmarket['TRADEDATE'].max()
            print('[STOCK_DAILYMARKET]: Update to {}.'.format(newdate))
        else:
            print('[STOCK_DAILYMARKET]: No data received, check source.')
    else:
        print('[STOCK_DAILYMARKET]: No data to update.')

    return

def update_stock_dailyindicator(enddate, conn_from, conn_to):
    '''
    [STOCK_DAILYINDICATOR]
    '''
    try:
        startdate = pd.read_sql('SELECT MAX(TRADEDATE) AS TRADEDATE FROM STOCK_DAILYINDICATOR', con = conn_to)['TRADEDATE'].values[0]
    except:
        sendmsg_text(content = '[STOCK_DAILYINDICATOR]: Table connect failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
        raise SystemError('[STOCK_DAILYINDICATOR]: Table connect failed.')
    
    if not startdate:
        startdate = ''
    else:
        startdate = get_tradedate(startdate = startdate)[1]

    if enddate >= startdate:
        ls_trange = get_tradedate(startdate = startdate, enddate = enddate)
        for i in tqdm(range(len(ls_trange) // 120 + 1), desc = '[STOCK_DAILYINDICATOR]'):
            if i * 120 == len(ls_trange):
                continue
            range_start = ls_trange[i * 120]
            if i == len(ls_trange) // 120:
                range_end = ls_trange[-1]
            else:
                range_end = ls_trange[(i + 1) * 120 - 1]
            
            try:
                df_dmarket = download_stock_dailyindicator(range_start, range_end, conn_from)
            except:
                sendmsg_text(content = '[STOCK_DAILYINDICATOR]: Source access failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
                raise SystemError('[STOCK_DAILYINDICATOR]: Source access failed.')
            try:
                df_dmarket.to_sql(name = 'STOCK_DAILYINDICATOR', con = conn_to, index = False, if_exists = 'append', chunksize = 10000)
                raw_conn = conn_to.raw_connection()
                cursor = raw_conn.cursor()
                cursor.execute('OPTIMIZE TABLE STOCK_DAILYINDICATOR FINAL DEDUPLICATE')
                raw_conn.commit()
            except:
                sendmsg_text(content = '[STOCK_DAILYINDICATOR]: Update failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
                raise SystemError('[STOCK_DAILYINDICATOR]: Update failed.')

        if df_dmarket.shape[0] > 0:
            newdate = df_dmarket['TRADEDATE'].max()
            print('[STOCK_DAILYINDICATOR]: Update to {}.'.format(newdate))
        else:
            print('[STOCK_DAILYINDICATOR]: No data received, check source.')
    else:
        print('[STOCK_DAILYINDICATOR]: No data to update.')

    return

def update_cfuture_dailymarket(enddate, conn_from, conn_to):
    '''
    [CFUTURE_DAILYMARKET]
    '''
    try:
        startdate = pd.read_sql('SELECT MAX(TRADEDATE) AS TRADEDATE FROM CFUTURE_DAILYMARKET', con = conn_to)['TRADEDATE'].values[0]
    except:
        sendmsg_text(content = '[CFUTURE_DAILYMARKET]: Table connect failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
        raise SystemError('[CFUTURE_DAILYMARKET]: Table connect failed.')
    
    if not startdate:
        startdate = '19950101'
    else:
        startdate = get_tradedate(startdate = startdate)[1]

    if enddate >= startdate:
        ls_trange = get_tradedate(startdate = startdate, enddate = enddate)
        for i in tqdm(range(len(ls_trange) // 240 + 1), desc = '[CFUTURE_DAILYMARKET]'):
            if i * 240 == len(ls_trange):
                continue
            range_start = ls_trange[i * 240]
            if i == len(ls_trange) // 240:
                range_end = ls_trange[-1]
            else:
                range_end = ls_trange[(i + 1) * 240 - 1]

            try:
                df_dmarket = download_cfuture_dailymarket(range_start, range_end, conn_from)
            except:
                sendmsg_text(content = '[CFUTURE_DAILYMARKET]: Source access failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
                raise SystemError('[CFUTURE_DAILYMARKET]: Source access failed.')
            try:
                df_dmarket.to_sql(name = 'CFUTURE_DAILYMARKET', con = conn_to, index = False, if_exists = 'append', chunksize = 10000)
                raw_conn = conn_to.raw_connection()
                cursor = raw_conn.cursor()
                cursor.execute('OPTIMIZE TABLE CFUTURE_DAILYMARKET FINAL DEDUPLICATE')
                raw_conn.commit()
            except:
                sendmsg_text(content = '[CFUTURE_DAILYMARKET]: Update failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
                raise SystemError('[CFUTURE_DAILYMARKET]: Update failed.')

        if df_dmarket.shape[0] > 0:
            newdate = df_dmarket['TRADEDATE'].max()
            print('[CFUTURE_DAILYMARKET]: Update to {}.'.format(newdate))
        else:
            print('[CFUTURE_DAILYMARKET]: No data received, check source.')
    else:
        print('[CFUTURE_DAILYMARKET]: No data to update.')
    
    return

def update_cfuture_info(getfile, conn_to):
    '''
    [CFUTURE_INFO]
    '''
    if getfile:
        try:
            df_cfut_info = pd.read_csv('/root/zerda/src/files/CFUTURE_INFO.csv')
            ls_stdcode_file = df_cfut_info['STDCODE'].to_list()
        except:
            sendmsg_text(content = '[CFUTURE_INFO]: No local file to update.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
            raise SystemError('[CFUTURE_INFO]: No local file to update, check source.')

        if len(ls_stdcode_file) == 0:
            sendmsg_text(content = '[CFUTURE_INFO]: No data in file.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
            raise SystemError('[CFUTURE_INFO]: No data in file, check source.')

        try:
            ls_stdcode_old = pd.read_sql('SELECT DISTINCT STDCODE FROM CFUTURE_INFO', con = conn_to)['STDCODE'].tolist()
        except:
            sendmsg_text(content = '[CFUTURE_INFO]: Table connect failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
            raise SystemError('[CFUTURE_INFO]: Table connect failed.')
        
        try:
            ls_stdcode_now = pd.read_sql('SELECT DISTINCT STDCODE FROM CFUTURE_DAILYMARKET', con = conn_to)['STDCODE'].tolist()
        except:
            sendmsg_text(content = '[CFUTURE_INFO]: Table [CFUTURE_DAILYMARKET] connect failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
            raise SystemError('[CFUTURE_INFO]: Table [CFUTURE_DAILYMARKET] connect failed.')
        
        try:
            df_cfut_info[~df_cfut_info['STDCODE'].isin(ls_stdcode_old)].to_sql(name = 'CFUTURE_INFO', con = conn_to, index = False, if_exists = 'append', chunksize = 10000)
            print('[CFUTURE_INFO]: Update from local file.')
        except:
            sendmsg_text(content = '[CFUTURE_INFO]: Update failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
            raise SystemError('[CFUTURE_INFO]: Update failed.')
        
        ls_unmatch = [i for i in ls_stdcode_now if i not in df_cfut_info['STDCODE'].values]
        if len(ls_unmatch) > 0:
            print('\033[1;31m[CFUTURE_INFO_WARNING]: STDCODE {} MISSED!\033[0m'.format(ls_unmatch))
            
    else:
        try:
            ls_stdcode_old = pd.read_sql('SELECT DISTINCT STDCODE FROM CFUTURE_INFO', con = conn_to)['STDCODE'].tolist()
        except:
            sendmsg_text(content = '[CFUTURE_INFO]: Table connect failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
            raise SystemError('[CFUTURE_INFO]: Table connect failed.')
        
        try:
            ls_stdcode_now = pd.read_sql('SELECT DISTINCT STDCODE FROM CFUTURE_DAILYMARKET', con = conn_to)['STDCODE'].tolist()
        except:
            sendmsg_text(content = '[CFUTURE_DAILYMARKET]: Table connect failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
            raise SystemError('[CFUTURE_DAILYMARKET]: Table connect failed.')
        
        ls_unmatch = [i for i in ls_stdcode_now if i not in ls_stdcode_old]
        if len(ls_unmatch) > 0:
            sendmsg_text(content = '[CFUTURE_INFO_WARNING]: STDCODE {} MISSED, PLEASE MANNUAL UPDATE!\n[{}]'.format(ls_unmatch, datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
            print('\033[1;31m[CFUTURE_INFO_WARNING]: STDCODE {} MISSED, PLEASE MANNUAL UPDATE!\033[0m'.format(ls_unmatch))

        print('[CFUTURE_INFO]: No data to update.')
    
    return

def update_ffuture_info(getfile, conn_to):
    '''
    [FFUTURE_INFO]
    '''
    if getfile:
        try:
            df_ffut_info = pd.read_csv('/root/zerda/src/files/FFUTURE_INFO.csv')
            ls_stdcode_file = df_ffut_info['STDCODE'].to_list()
        except:
            sendmsg_text(content = '[FFUTURE_INFO]: No local file to update.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
            raise SystemError('[FFUTURE_INFO]: No local file to update, check source.')

        if len(ls_stdcode_file) == 0:
            sendmsg_text(content = '[FFUTURE_INFO]: No data in file.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
            raise SystemError('[FFUTURE_INFO]: No data in file, check source.')

        try:
            ls_stdcode_old = pd.read_sql('SELECT DISTINCT STDCODE FROM FFUTURE_INFO', con = conn_to)['STDCODE'].tolist()
        except:
            sendmsg_text(content = '[FFUTURE_INFO]: Table connect failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
            raise SystemError('[FFUTURE_INFO]: Table connect failed.')
        
        try:
            ls_stdcode_now = pd.read_sql('SELECT DISTINCT STDCODE FROM FFUTURE_DAILYMARKET', con = conn_to)['STDCODE'].tolist()
        except:
            sendmsg_text(content = '[FFUTURE_INFO]: Table [FFUTURE_DAILYMARKET] connect failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
            raise SystemError('[FFUTURE_INFO]: Table [FFUTURE_DAILYMARKET] connect failed.')
        
        try:
            df_ffut_info[~df_ffut_info['STDCODE'].isin(ls_stdcode_old)].to_sql(name = 'FFUTURE_INFO', con = conn_to, index = False, if_exists = 'append', chunksize = 10000)
            print('[FFUTURE_INFO]: Update from local file.')
        except:
            sendmsg_text(content = '[FFUTURE_INFO]: Update failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
            raise SystemError('[FFUTURE_INFO]: Update failed.')
        
        ls_unmatch = [i for i in ls_stdcode_now if i not in df_ffut_info['STDCODE'].values]
        if len(ls_unmatch) > 0:
            print('\033[1;31m[FFUTURE_INFO_WARNING]: STDCODE {} MISSED!\033[0m'.format(ls_unmatch))
            
    else:
        try:
            ls_stdcode_old = pd.read_sql('SELECT DISTINCT STDCODE FROM FFUTURE_INFO', con = conn_to)['STDCODE'].tolist()
        except:
            sendmsg_text(content = '[FFUTURE_INFO]: Table connect failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
            raise SystemError('[FFUTURE_INFO]: Table connect failed.')
        
        try:
            ls_stdcode_now = pd.read_sql('SELECT DISTINCT STDCODE FROM FFUTURE_DAILYMARKET', con = conn_to)['STDCODE'].tolist()
        except:
            sendmsg_text(content = '[FFUTURE_DAILYMARKET]: Table connect failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
            raise SystemError('[FFUTURE_DAILYMARKET]: Table connect failed.')
        
        ls_unmatch = [i for i in ls_stdcode_now if i not in ls_stdcode_old]
        if len(ls_unmatch) > 0:
            sendmsg_text(content = '[FFUTURE_INFO_WARNING]: STDCODE {} MISSED, PLEASE MANNUAL UPDATE!\n[{}]'.format(ls_unmatch, datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
            print('\033[1;31m[FFUTURE_INFO_WARNING]: STDCODE {} MISSED, PLEASE MANNUAL UPDATE!\033[0m'.format(ls_unmatch))

        print('[FFUTURE_INFO]: No data to update.')
    
    return

def update_ffuture_dailymarket(enddate, conn_from, conn_to):
    '''
    [FFUTURE_DAILYMARKET]
    '''
    try:
        startdate = pd.read_sql('SELECT MAX(TRADEDATE) AS TRADEDATE FROM FFUTURE_DAILYMARKET', con = conn_to)['TRADEDATE'].values[0]
    except:
        sendmsg_text(content = '[FFUTURE_DAILYMARKET]: Table connect failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
        raise SystemError('[FFUTURE_DAILYMARKET]: Table connect failed.')
    
    if not startdate:
        startdate = ''
    else:
        startdate = get_tradedate(startdate = startdate)[1]

    if enddate >= startdate:
        try:
            df_dmarket = download_ffuture_dailymarket(startdate, enddate, conn_from)
        except:
            sendmsg_text(content = '[FFUTURE_DAILYMARKET]: Source access failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
            raise SystemError('[FFUTURE_DAILYMARKET]: Source access failed.')
        try:
            df_dmarket.to_sql(name = 'FFUTURE_DAILYMARKET', con = conn_to, index = False, if_exists = 'append', chunksize = 10000)
            raw_conn = conn_to.raw_connection()
            cursor = raw_conn.cursor()
            cursor.execute('OPTIMIZE TABLE FFUTURE_DAILYMARKET FINAL DEDUPLICATE')
            raw_conn.commit()
        except:
            sendmsg_text(content = '[FFUTURE_DAILYMARKET]: Update failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
            raise SystemError('[FFUTURE_DAILYMARKET]: Update failed.')

        if df_dmarket.shape[0] > 0:
            newdate = df_dmarket['TRADEDATE'].max()
            print('[FFUTURE_DAILYMARKET]: Update to {}.'.format(newdate))
        else:
            print('[FFUTURE_DAILYMARKET]: No data received, check source.')
    else:
        print('[FFUTURE_DAILYMARKET]: No data to update.')

    return

def update_foption_etfmarket(enddate, conn_from, conn_to):
    '''
    [FOPTION_ETFMARKET]
    '''
    try:
        startdate = pd.read_sql('SELECT MAX(TRADEDATE) AS TRADEDATE FROM FOPTION_ETFMARKET', con = conn_to)['TRADEDATE'].values[0]
    except:
        sendmsg_text(content = '[FOPTION_ETFMARKET]: Table connect failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
        raise SystemError('[FOPTION_ETFMARKET]: Table connect failed.')
    
    if not startdate:
        startdate = ''
    else:
        startdate = get_tradedate(startdate = startdate)[1]

    if enddate >= startdate:
        try:
            df_dmarket = download_foption_etfmarket(startdate, enddate, conn_from)
        except:
            sendmsg_text(content = '[FOPTION_ETFMARKET]: Source access failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
            raise SystemError('[FOPTION_ETFMARKET]: Source access failed.')
        try:
            df_dmarket.to_sql(name = 'FOPTION_ETFMARKET', con = conn_to, index = False, if_exists = 'append', chunksize = 10000)
            raw_conn = conn_to.raw_connection()
            cursor = raw_conn.cursor()
            cursor.execute('OPTIMIZE TABLE FOPTION_ETFMARKET FINAL DEDUPLICATE')
            raw_conn.commit()
        except:
            sendmsg_text(content = '[FOPTION_ETFMARKET]: Update failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
            raise SystemError('[FOPTION_ETFMARKET]: Update failed.')

        if df_dmarket.shape[0] > 0:
            newdate = df_dmarket['TRADEDATE'].max()
            print('[FOPTION_ETFMARKET]: Update to {}.'.format(newdate))
        else:
            print('[FOPTION_ETFMARKET]: No data received, check source.')
    else:
        print('[FOPTION_ETFMARKET]: No data to update.')
    
    return

def update_foption_dailymarket(enddate, conn_from, conn_to):
    '''
    [FOPTION_DAILYMARKET]
    '''
    try:
        startdate = pd.read_sql('SELECT MAX(TRADEDATE) AS TRADEDATE FROM FOPTION_DAILYMARKET', con = conn_to)['TRADEDATE'].values[0]
    except:
        sendmsg_text(content = '[FOPTION_DAILYMARKET]: Table connect failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
        raise SystemError('[FOPTION_DAILYMARKET]: Table connect failed.')
    
    if not startdate:
        startdate = '20150101'
    else:
        startdate = get_tradedate(startdate = startdate)[1]

    if enddate >= startdate:
        ls_trange = get_tradedate(startdate = startdate, enddate = enddate)
        for i in tqdm(range(len(ls_trange) // 120 + 1), desc = '[FOPTION_DAILYMARKET]'):
            if i * 120 == len(ls_trange):
                continue
            range_start = ls_trange[i * 120]
            if i == len(ls_trange) // 120:
                range_end = ls_trange[-1]
            else:
                range_end = ls_trange[(i + 1) * 120 - 1]

            try:
                df_dmarket = download_foption_dailymarket(range_start, range_end, conn_from)
            except:
                sendmsg_text(content = '[FOPTION_DAILYMARKET]: Source access failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
                raise SystemError('[FOPTION_DAILYMARKET]: Source access failed.')
            try:
                df_dmarket.to_sql(name = 'FOPTION_DAILYMARKET', con = conn_to, index = False, if_exists = 'append', chunksize = 10000)
                raw_conn = conn_to.raw_connection()
                cursor = raw_conn.cursor()
                cursor.execute('OPTIMIZE TABLE FOPTION_DAILYMARKET FINAL DEDUPLICATE')
                raw_conn.commit()
            except:
                sendmsg_text(content = '[FOPTION_DAILYMARKET]: Update failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
                raise SystemError('[FOPTION_DAILYMARKET]: Update failed.')

        if df_dmarket.shape[0] > 0:
            newdate = df_dmarket['TRADEDATE'].max()
            print('[FOPTION_DAILYMARKET]: Update to {}.'.format(newdate))
        else:
            print('[FOPTION_DAILYMARKET]: No data received, check source.')
    else:
        print('[FOPTION_DAILYMARKET]: No data to update.')
    
    return

def update_cbond_info(conn_from, conn_to):
    '''
    [CBOND_INFO]
    '''
    try:
        ls_code = pd.read_sql('SELECT CBOND_CODE FROM CBOND_INFO', con = conn_to)['CBOND_CODE'].to_list()
    except:
        sendmsg_text(content = '[CBOND_INFO]: Table connect failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
        raise SystemError('[CBOND_INFO]: Table connect failed.')
    
    try:
        df_cbondinfo = download_cbond_info(conn_from)
    except:
        sendmsg_text(content = '[CBOND_INFO]: Source access failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
        raise SystemError('[CBOND_INFO]: Source access failed.')

    if df_cbondinfo.shape[0] > 0:
        df_cbondinfo_update = df_cbondinfo[~df_cbondinfo['CBOND_CODE'].isin(ls_code)]
    else:
        print('[CBOND_INFO]: No data received, check source.')

    if df_cbondinfo_update.shape[0] > 0:
        try:
            df_cbondinfo_update.to_sql(name = 'CBOND_INFO', con = conn_to, index = False, if_exists = 'append', chunksize = 10000)
            raw_conn = conn_to.raw_connection()
            cursor = raw_conn.cursor()
            cursor.execute('OPTIMIZE TABLE CBOND_INFO FINAL DEDUPLICATE')
            raw_conn.commit()
            print('[CBOND_INFO]: {} records added.'.format(df_cbondinfo_update.shape[0]))
        except:
            sendmsg_text(content = '[CBOND_INFO]: Update failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
            raise SystemError('[CBOND_INFO]: Update failed.')
    else:
        print('[CBOND_INFO]: No data to update.')
    
    return

def update_cbond_dailymarket(enddate, conn_from, conn_to):
    '''
    [CBOND_DAILYMARKET]
    '''
    try:
        startdate = pd.read_sql('SELECT MAX(TRADEDATE) AS TRADEDATE FROM CBOND_DAILYMARKET', con = conn_to)['TRADEDATE'].values[0]
    except:
        sendmsg_text(content = '[CBOND_DAILYMARKET]: Table connect failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
        raise SystemError('[CBOND_DAILYMARKET]: Table connect failed.')
    
    if not startdate:
        startdate = ''
    else:
        startdate = get_tradedate(startdate = startdate)[1]

    if enddate >= startdate:
        try:
            df_dmarket = download_cbond_dailymarket(startdate, enddate, conn_from)
        except:
            sendmsg_text(content = '[CBOND_DAILYMARKET]: Source access failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
            raise SystemError('[CBOND_DAILYMARKET]: Source access failed.')
        try:
            df_dmarket.to_sql(name = 'CBOND_DAILYMARKET', con = conn_to, index = False, if_exists = 'append', chunksize = 10000)
            raw_conn = conn_to.raw_connection()
            cursor = raw_conn.cursor()
            cursor.execute('OPTIMIZE TABLE CBOND_DAILYMARKET FINAL DEDUPLICATE')
            raw_conn.commit()
        except:
            sendmsg_text(content = '[CBOND_DAILYMARKET]: Update failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
            raise SystemError('[CBOND_DAILYMARKET]: Update failed.')

        if df_dmarket.shape[0] > 0:
            newdate = df_dmarket['TRADEDATE'].max()
            print('[CBOND_DAILYMARKET]: Update to {}.'.format(newdate))
        else:
            print('[CBOND_DAILYMARKET]: No data received, check source.')
    else:
        print('[CBOND_DAILYMARKET]: No data to update.')

    return

def update_stock_yearlyreport(enddate, conn_from, conn_to):
    '''
    [STOCK_YEARLYREPORT]
    '''
    raw_conn = conn_to.raw_connection()
    cursor = raw_conn.cursor()
    try:
        startdate = pd.read_sql('SELECT MAX(REPORT_DATE) AS REPORT_DATE FROM STOCK_YEARLYREPORT', con = conn_to)['REPORT_DATE'].values[0]
        startyear = startdate[0: 4]
    except:
        sendmsg_text(content = '[STOCK_YEARLYREPORT]: Table connect failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
        raise SystemError('[STOCK_YEARLYREPORT]: Table connect failed.')
    
    if not startyear:
        startyear = '1989'

    endyear = str(int(enddate[0: 4]) - 1)

    if endyear >= startyear:
        try:
            df_yreport = download_stock_yearlyreport(startyear = startyear, endyear = endyear, conn_ts = conn_from)
        except:
            sendmsg_text(content = '[STOCK_YEARLYREPORT]: Source access failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
            raise SystemError('[STOCK_YEARLYREPORT]: Source access failed.')
        
        try:        
            if endyear > startyear:
                df_yreport.to_sql(name = 'STOCK_YEARLYREPORT', con = conn_to, index = False, if_exists = 'append', chunksize = 10000)
                print('[STOCK_YEARLYREPORT]: Update to {}.'.format(endyear))
            elif df_yreport.shape[0] > 0:
                sql = "ALTER TABLE STOCK_YEARLYREPORT DELETE WHERE REPORT_DATE = '{}'".format(startdate)
                cursor.execute(sql)
                df_yreport.to_sql(name = 'STOCK_YEARLYREPORT', con = conn_to, index = False, if_exists = 'append', chunksize = 10000)
                print('[STOCK_YEARLYREPORT]: Update to {}.'.format(endyear))

            cursor.execute('OPTIMIZE TABLE STOCK_YEARLYREPORT FINAL DEDUPLICATE')
        except:
            sendmsg_text(content = '[STOCK_YEARLYREPORT]: Update failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
            raise SystemError('[STOCK_YEARLYREPORT]: Update failed.')
    else:
        print('[STOCK_YEARLYREPORT]: No data to update.')

    raw_conn.commit()
    return

def update_stock_dailyreturn(enddate, conn_to):
    '''
    [STOCK_DAILYRETURN]
    '''
    try:
        startdate = pd.read_sql('SELECT MAX(TRADEDATE) AS TRADEDATE FROM STOCK_DAILYRETURN', con = conn_to)['TRADEDATE'].values[0]
    except:
        sendmsg_text(content = '[STOCK_DAILYRETURN]: Table connect failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
        raise SystemError('[STOCK_DAILYRETURN]: Table connect failed.')
    
    if not startdate:
        startdate = ''
    else:
        startdate = get_tradedate(startdate = startdate)[1]

    if enddate >= startdate:
        ls_trange = get_tradedate(startdate = startdate, enddate = enddate)
        for i in tqdm(range(len(ls_trange) // 120 + 1), desc = '[STOCK_DAILYRETURN]'):
            if i * 120 == len(ls_trange):
                continue
            range_start = ls_trange[i * 120]
            if i == len(ls_trange) // 120:
                range_end = ls_trange[-1]
            else:
                range_end = ls_trange[(i + 1) * 120 - 1]
            try:
                # 提取历史最新收盘价
                sql = '''
                    SELECT 
                        A.STOCK_CODE, 
                        A.TRADEDATE, 
                        A.CLOSE, 
                        A.ADJ_FACTOR 
                    FROM STOCK_DAILYMARKET A
                    INNER JOIN (
                    SELECT
                        STOCK_CODE,
                        MAX(TRADEDATE) AS LASTDATE
                    FROM STOCK_DAILYMARKET
                    WHERE TRADEDATE < '{}'
                    GROUP BY STOCK_CODE
                    ) B
                    ON 
                        A.STOCK_CODE = B.STOCK_CODE AND
                        A.TRADEDATE = B.LASTDATE
                    '''.format(range_start)
                df_init_stkdata = pd.read_sql(sql, con = conn_to)
                # 提取待更新收盘价
                sql = '''
                    SELECT 
                        STOCK_CODE, 
                        TRADEDATE, 
                        CLOSE, 
                        ADJ_FACTOR 
                    FROM STOCK_DAILYMARKET
                    WHERE 
                        TRADEDATE >= '{}' AND
                        TRADEDATE <= '{}'
                    '''.format(range_start, range_end)
                df_new_stkdata = pd.read_sql(sql, con = conn_to)
            except:
                sendmsg_text(content = '[STOCK_DAILYRETURN]: Table [STOCK_DAILYMARKET] connect failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
                raise SystemError('[STOCK_DAILYRETURN]: Table [STOCK_DAILYMARKET] connect failed.')
            try:
                if df_init_stkdata.shape[0] > 0:
                    df_stkdata = pd.concat([df_init_stkdata, df_new_stkdata])
                else:
                    df_stkdata = df_new_stkdata
                df_stkreturn = cal_stock_dailyreturn(df_stkdata)
                df_stkreturn = df_stkreturn[df_stkreturn['TRADEDATE'] >= range_start].copy()
                df_stkreturn.to_sql(name = 'STOCK_DAILYRETURN', con = conn_to, index = False, if_exists = 'append', chunksize = 10000)
            except:
                sendmsg_text(content = '[STOCK_DAILYRETURN]: Update failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
                raise SystemError('[STOCK_DAILYRETURN]: Update failed.')

        if df_stkreturn.shape[0] > 0:
            newdate = df_stkreturn['TRADEDATE'].max()
            print('[STOCK_DAILYRETURN]: Update to {}.'.format(newdate))
        else:
            print('[STOCK_DAILYRETURN]: No data received, check source.')
    else:
        print('[STOCK_DAILYRETURN]: No data to update.')

    return

def update_cfuture_maincode(enddate, conn_to):
    '''
    [CFUTURE_MAINCODE]
    '''
    try:
        startdate = pd.read_sql('SELECT MAX(TRADEDATE) AS TRADEDATE FROM CFUTURE_MAINCODE', con = conn_to)['TRADEDATE'].values[0]
    except:
        sendmsg_text(content = '[CFUTURE_MAINCODE]: Table connect failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
        raise SystemError('[CFUTURE_MAINCODE]: Table connect failed.')
    
    if not startdate:
        startdate = pd.read_sql('SELECT MIN(TRADEDATE) AS TRADEDATE FROM CFUTURE_DAILYMARKET', con = conn_to)['TRADEDATE'].values[0]
        df_init_main = None
        df_init_submain = None
    else:
        df_init_main = pd.read_sql("SELECT FUT_CODE, TRADEDATE, STDCODE FROM CFUTURE_MAINCODE WHERE TRADEDATE = '{}' AND MTYPE = 1".format(startdate), con = conn_to)
        df_init_submain = pd.read_sql("SELECT FUT_CODE, TRADEDATE, STDCODE FROM CFUTURE_MAINCODE WHERE TRADEDATE = '{}' AND MTYPE = 2".format(startdate), con = conn_to)

    range_preend = startdate
    if enddate > startdate:
        ls_trange = get_tradedate(startdate = startdate, enddate = enddate)
        for i in tqdm(range(len(ls_trange) // 240 + 1), desc = '[CFUTURE_MAINCODE]'):
            if i * 240 == len(ls_trange):
                continue
            range_start = ls_trange[i * 240]
            if i == len(ls_trange) // 240:
                range_end = ls_trange[-1]
            else:
                range_end = ls_trange[(i + 1) * 240 - 1]

            try:
                sql = '''
                SELECT 
                    FUT_CODE, 
                    TRADEDATE, 
                    STDCODE, 
                    VOL, 
                    OI, 
                    DLISTDATE 
                FROM 
                    CFUTURE_DAILYMARKET
                WHERE 
                    STDCODE <> 'SCTAS' AND
                    TRADEDATE >= '{}' AND
                    TRADEDATE <= '{}'
                '''.format(range_preend, range_end)
                df_cfutdata = pd.read_sql(sql, con = conn_to)
            except:
                sendmsg_text(content = '[CFUTURE_MAINCODE]: Table [CFUTURE_DAILYMARKET] connect failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
                raise SystemError('[CFUTURE_MAINCODE]: Table [CFUTURE_DAILYMARKET] connect failed.')
        
            try:
                df_maincode = cal_cfuture_maincode(df_cfutdata, df_initmain = df_init_main)
                df_cfutdata_demain = df_cfutdata.merge(df_maincode, on = ['TRADEDATE', 'STDCODE', 'FUT_CODE'], how = 'left', indicator = True).query('_merge == "left_only"').drop(columns = '_merge')
                df_submaincode = cal_cfuture_maincode(df_cfutdata_demain, df_initmain = df_init_submain)
                
                df_maincode['MTYPE'] = 1
                df_maincode = df_maincode[df_maincode['TRADEDATE'] > range_preend].copy()
                df_maincode.to_sql(name = 'CFUTURE_MAINCODE', con = conn_to, index = False, if_exists = 'append', chunksize = 10000)
                df_submaincode['MTYPE'] = 2
                df_submaincode = df_submaincode[df_submaincode['TRADEDATE'] > range_preend].copy()
                df_submaincode.to_sql(name = 'CFUTURE_MAINCODE', con = conn_to, index = False, if_exists = 'append', chunksize = 10000)

                df_init_main = pd.read_sql("SELECT FUT_CODE, TRADEDATE, STDCODE FROM CFUTURE_MAINCODE WHERE TRADEDATE = '{}' AND MTYPE = 1".format(range_end), con = conn_to)
                df_init_submain = pd.read_sql("SELECT FUT_CODE, TRADEDATE, STDCODE FROM CFUTURE_MAINCODE WHERE TRADEDATE = '{}' AND MTYPE = 2".format(range_end), con = conn_to)
                range_preend = range_end
            except:
                sendmsg_text(content = '[CFUTURE_MAINCODE]: Update failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
                raise SystemError('[CFUTURE_MAINCODE]: Update failed.')


        if df_maincode.shape[0] > 0:
            newdate = df_maincode['TRADEDATE'].max()
            print('[CFUTURE_MAINCODE]: Update to {}.'.format(newdate))
        else:
            print('[CFUTURE_MAINCODE]: No data received, check source.')
    else:
        print('[CFUTURE_MAINCODE]: No data to update.')
    
    return

def update_foption_dailygreeks(enddate, conn_to):
    '''
    [FOPTION_DAILYGREEKS]
    '''
    try:
        startdate = pd.read_sql('SELECT MAX(TRADEDATE) AS TRADEDATE FROM FOPTION_DAILYGREEKS', con = conn_to)['TRADEDATE'].values[0]
    except:
        sendmsg_text(content = '[FOPTION_DAILYGREEKS]: Table connect failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
        raise SystemError('[FOPTION_DAILYGREEKS]: Table connect failed.')
    
    if not startdate:
        startdate = '20150101'
    else:
        startdate = get_tradedate(startdate = startdate)[1]

    if enddate >= startdate:
        ls_trange = get_tradedate(startdate = startdate, enddate = enddate)
        for i in tqdm(range(len(ls_trange) // 120 + 1), desc = '[FOPTION_DAILYGREEKS]'):
            if i * 120 == len(ls_trange):
                continue
            range_start = ls_trange[i * 120]
            if i == len(ls_trange) // 120:
                range_end = ls_trange[-1]
            else:
                range_end = ls_trange[(i + 1) * 120 - 1]
            try:
                # 提取最新期权行情数据
                sql = '''
                    SELECT 
                        A.OPT_CODE AS OPT_CODE, 
                        A.TRADEDATE AS TRADEDATE, 
                        A.EXCHANGE AS EXCHANGE, 
                        A.TARGET_CODE AS TARGET_CODE,
                        A.FLAG AS FLAG, 
                        A.STRIKE_PRICE AS STRIKE_PRICE, 
                        COALESCE(B.`CLOSE`, C.`CLOSE`) AS TARGET_PRICE,
                        A.SETTLE  AS OPTION_PRICE,
                        A.OI AS OI,
                        A.TMONTH AS TMONTH, 
                        A.OP_TIME AS OP_TIME 
                    FROM FOPTION_DAILYMARKET A
                    LEFT JOIN FOPTION_ETFMARKET B
                    ON
                        A.TRADEDATE = B.TRADEDATE AND
                        A.TARGET_CODE = B.FUND_CODE
                    LEFT JOIN INDEX_DAILYMARKET C
                    ON
                        A.TRADEDATE = C.TRADEDATE AND
                        A.TARGET_CODE = C.INDEX_CODE
                    WHERE 
                        A.TRADEDATE >= '{}' AND
                        A.TRADEDATE <= '{}'
                    '''.format(range_start, range_end)
                df_optdata = pd.read_sql(sql, con = conn_to)
            except:
                sendmsg_text(content = '[FOPTION_DAILYGREEKS]: Table [FOPTION_DAILYMARKET] connect failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
                raise SystemError('[FOPTION_DAILYGREEKS]: Table [FOPTION_DAILYMARKET] connect failed.')
            if df_optdata.shape[0] > 0:
                try:
                    df_greeks = cal_option_dailygreeks(df_optdata)
                    df_greeks.to_sql(name = 'FOPTION_DAILYGREEKS', con = conn_to, index = False, if_exists = 'append', chunksize = 10000)
                except:
                    sendmsg_text(content = '[FOPTION_DAILYGREEKS]: Update failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
                    raise SystemError('[FOPTION_DAILYGREEKS]: Update failed.')
            else:
                print('[FOPTION_DAILYGREEKS]: No raw data received, check source.')

        if df_greeks.shape[0] > 0:
            newdate = df_greeks['TRADEDATE'].max()
            print('[FOPTION_DAILYGREEKS]: Update to {}.'.format(newdate))
        else:
            print('[FOPTION_DAILYGREEKS]: No data received, check source.')
    else:
        print('[FOPTION_DAILYGREEKS]: No data to update.')

    return

def update_foption_dailyskew(enddate, conn_to):
    '''
    [FOPTION_DAILYSKEW]
    '''
    try:
        startdate = pd.read_sql('SELECT MAX(TRADEDATE) AS TRADEDATE FROM FOPTION_DAILYSKEW', con = conn_to)['TRADEDATE'].values[0]
    except:
        sendmsg_text(content = '[FOPTION_DAILYSKEW]: Table connect failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
        raise SystemError('[FOPTION_DAILYSKEW]: Table connect failed.')
    
    if not startdate:
        startdate = '20150101'
    else:
        startdate = get_tradedate(startdate = startdate)[1]

    if enddate >= startdate:
        ls_trange = get_tradedate(startdate = startdate, enddate = enddate)
        for i in tqdm(range(len(ls_trange) // 240 + 1), desc = '[FOPTION_DAILYSKEW]'):
            if i * 240 == len(ls_trange):
                continue
            range_start = ls_trange[i * 240]
            if i == len(ls_trange) // 240:
                range_end = ls_trange[-1]
            else:
                range_end = ls_trange[(i + 1) * 240 - 1]
            try:
                # 提取最新期权行情数据
                sql = '''
                    SELECT
                        TRADEDATE, 
                        TARGET_CODE,
                        FLAG, 
                        STRIKE_PRICE, 
                        TARGET_PRICE,
                        TMONTH, 
                        IV,
                        DELTA
                    FROM FOPTION_DAILYGREEKS
                    WHERE 
                        TRADEDATE >= '{}' AND
                        TRADEDATE <= '{}'
                    '''.format(range_start, range_end)
                df_optgreeks = pd.read_sql(sql, con = conn_to)
            except:
                sendmsg_text(content = '[FOPTION_DAILYSKEW]: Table [FOPTION_DAILYGREEKS] connect failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
                raise SystemError('[FOPTION_DAILYSKEW]: Table [FOPTION_DAILYGREEKS] connect failed.')
            
            if df_optgreeks.shape[0] > 0:
                try:
                    df_skew = cal_option_dailyskew(df_optgreeks)
                    df_skew.to_sql(name = 'FOPTION_DAILYSKEW', con = conn_to, index = False, if_exists = 'append', chunksize = 10000)
                except:
                    sendmsg_text(content = '[FOPTION_DAILYSKEW]: Update failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
                    raise SystemError('[FOPTION_DAILYSKEW]: Update failed.')
            else:
                print('[FOPTION_DAILYSKEW]: No raw data received, check source.')

        if df_skew.shape[0] > 0:
            newdate = df_skew['TRADEDATE'].max()
            print('[FOPTION_DAILYSKEW]: Update to {}.'.format(newdate))
        else:
            print('[FOPTION_DAILYSKEW]: No data received, check source.')
    
    else:
        print('[FOPTION_DAILYSKEW]: No data to update.')

    return

def update_stock_dailywinratio(enddate, conn_to):
    '''
    [STOCK_DAILYWINRATIO]
    '''
    try:
        startdate = pd.read_sql('SELECT MAX(TRADEDATE) AS TRADEDATE FROM STOCK_DAILYWINRATIO', con = conn_to)['TRADEDATE'].values[0]
    except:
        sendmsg_text(content = '[STOCK_DAILYWINRATIO]: Table connect failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
        raise SystemError('[STOCK_DAILYWINRATIO]: Table connect failed.')
    
    if not startdate:
        startdate = '20150101'
    else:
        startdate = get_tradedate(startdate = startdate)[1]

    if enddate >= startdate:
        ls_index = ['000300.SH', '000905.SH', '000852.SH', '000985.CSI']
        dic_stkqual = {}
        for index in ls_index:
            dic_stkqual[index] = get_stkqual(index)
        ls_trange = get_tradedate(startdate = startdate, enddate = enddate)
        for i in tqdm(range(len(ls_trange) // 60 + 1), desc = '[STOCK_DAILYWINRATIO]'):
            if i * 60 == len(ls_trange):
                continue
            range_start = ls_trange[i * 60]
            if i == len(ls_trange) // 60:
                range_end = ls_trange[-1]
            else:
                range_end = ls_trange[(i + 1) * 60 - 1]
            try:
                # 提取股票收益率数据
                sql = '''
                    SELECT 
                        TRADEDATE, 
                        STOCK_CODE, 
                        RETURN 
                    FROM 
                        STOCK_DAILYMARKET 
                    WHERE 
                        TRADEDATE >= '{}' AND
                        TRADEDATE <= '{}'
                    '''.format(range_start, range_end)
                df_stkdata = pd.read_sql(sql, con = conn_to)

            except:
                sendmsg_text(content = '[STOCK_DAILYWINRATIO]: Stock Return download failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
                raise SystemError('[STOCK_DAILYWINRATIO]: Stock Return download failed.')
            
            if df_stkdata.shape[0] > 0:
                try:
                    df_winratio = cal_stock_dailywinratio(range_start, range_end, df_stkdata, dic_stkqual)
                    df_winratio.to_sql(name = 'STOCK_DAILYWINRATIO', con = conn_to, index = False, if_exists = 'append', chunksize = 10000)
                except:
                    sendmsg_text(content = '[STOCK_DAILYWINRATIO]: Update failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
                    raise SystemError('[STOCK_DAILYWINRATIO]: Update failed.')
            else:
                print('[STOCK_DAILYWINRATIO]: No raw data received, check source.')

        if df_winratio.shape[0] > 0:
            newdate = df_winratio['TRADEDATE'].max()
            print('[STOCK_DAILYWINRATIO]: Update to {}.'.format(newdate))
        else:
            print('[STOCK_DAILYWINRATIO]: No data received, check source.')
    
    else:
        print('[STOCK_DAILYWINRATIO]: No data to update.')

    return

def update_fund_rawnav(conn_smpp, conn_to):
    '''
    [FUND_RAWNAV]
    '''
    try:
        df_funds = pd.read_csv('/root/zerda/src/files/FUND180.csv')
    except:
        sendmsg_text(content = '[FUND_RAWNAV]: No local file to update.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
        raise SystemError('[FUND_RAWNAV]: No local file to update, check source.')

    try:
        startdate = pd.read_sql('SELECT MAX(CREATE_TIME) AS STARTDATE FROM FUND_RAWNAV', con = conn_vulpes)['STARTDATE'].dt.strftime('%Y%m%d').values[0]
        startdate = '20150101' if startdate < '20150101' else get_tradedate(enddate = startdate, frq = 'W')[-3]
    except:
        sendmsg_text(content = '[FUND_RAWNAV]: Table connect failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
        raise SystemError('[FUND_RAWNAV]: Table connect failed.')

    if not startdate:
        startdate = '20150101'

    try:
        df_fundnav = download_fundnav(startdate, df_funds, conn_smpp)
        if df_fundnav.empty:
            print('[FUND_RAWNAV]: No smpp data received, check source.')
            return
    except:
        sendmsg_text(content = '[FUND_RAWNAV]: Source access failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
        raise SystemError('[FUND_RAWNAV]: Source access failed.')

    try:
        df_fundnav.to_sql(name = 'FUND_RAWNAV', con = conn_to, index = False, if_exists = 'append', chunksize = 10000)
        raw_conn = conn_to.raw_connection()
        cursor = raw_conn.cursor()
        cursor.execute('OPTIMIZE TABLE FUND_RAWNAV FINAL DEDUPLICATE')
        raw_conn.commit()
    except:
        sendmsg_text(content = '[FUND_RAWNAV]: Update failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
        raise SystemError('[FUND_RAWNAV]: Update failed.')

    if df_fundnav.shape[0] > 0:
        newdate = df_fundnav['TRADEDATE'].max()
        print('[FUND_RAWNAV]: Update to {}.'.format(newdate))

    return

def update_fund_weeklymarket(conn_to):
    '''
    [FUND_WEEKLYMARKET]
    '''
    raw_conn = conn_to.raw_connection()
    cursor = raw_conn.cursor()
    try:
        num_records = pd.read_sql('SELECT COUNT(CREATE_TIME) AS RECORDS FROM FUND_WEEKLYMARKET', con = conn_to)['RECORDS'].values[0]
    except:
        sendmsg_text(content = '[FUND_WEEKLYMARKET]: Table connect failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
        raise SystemError('[FUND_WEEKLYMARKET]: Table connect failed.')
    
    if not num_records:
        num_records = 0

    try:
        df_fundnav = pd.read_sql(sql = 'SELECT * FROM FUND_RAWNAV ORDER BY TRADEDATE', con = conn_to)
    except:
        sendmsg_text(content = '[FUND_WEEKLYMARKET]: No rawnav data to wash, check rawnav table.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
        raise SystemError('[FUND_WEEKLYMARKET]: No rawnav data to wash, check rawnav table.')
    
    try:
        df_fundmarket = cal_fund_weeklymarket(df_fundnav, conn_to)
        df_funddata = df_fundmarket[df_fundmarket['TRADEDATE'] >= '20211231'].copy()
        df_fundindex = cal_fund_weeklyindex(df_funddata)
    except:
        sendmsg_text(content = '[FUND_WEEKLYMARKET]: Data calculating failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
        raise SystemError('[FUND_WEEKLYMARKET]: Data calculating failed.')

    if num_records > 0:
        sql = 'TRUNCATE TABLE FUND_WEEKLYMARKET'
        cursor.execute(sql)
        raw_conn.commit()

    try:
        df_fundmarket.to_sql(name = 'FUND_WEEKLYMARKET', con = conn_to, index = False, if_exists = 'append', chunksize = 10000)
        df_fundindex.to_sql(name = 'FUND_WEEKLYMARKET', con = conn_to, index = False, if_exists = 'append', chunksize = 10000)
        print('[FUND_WEEKLYMARKET]: Data refreshed.')
    except:
        sendmsg_text(content = '[FUND_WEEKLYMARKET]: Update failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
        raise SystemError('[FUND_WEEKLYMARKET]: Update failed.')

    return


def update_fund_stkfundscore(conn_to):
    '''
    [FUND_STKFUNDSCORE]
    '''
    raw_conn = conn_to.raw_connection()
    cursor = raw_conn.cursor()
    try:
        num_records = pd.read_sql('SELECT COUNT(CREATE_TIME) AS RECORDS FROM FUND_STKFUNDSCORE', con = conn_to)['RECORDS'].values[0]
    except:
        sendmsg_text(content = '[FUND_STKFUNDSCORE]: Table connect failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
        raise SystemError('[FUND_STKFUNDSCORE]: Table connect failed.')
    
    if not num_records:
        num_records = 0

    try:
        df_funddata = pd.read_sql(sql = 'SELECT * FROM FUND_WEEKLYMARKET ORDER BY TRADEDATE', con = conn_to)
    except:
        sendmsg_text(content = '[FUND_STKFUNDSCORE]: No weeklymarket data fetched, check weeklymarket table.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
        raise SystemError('[FUND_STKFUNDSCORE]: No weeklymarket data fetched, check weeklymarket table.')
    
    try:
        df_stkfundscore = cal_stkfundscore(df_funddata)
    except:
        sendmsg_text(content = '[FUND_STKFUNDSCORE]: Data calculating failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
        raise SystemError('[FUND_STKFUNDSCORE]: Data calculating failed.')

    if num_records > 0:
        sql = 'TRUNCATE TABLE FUND_STKFUNDSCORE'
        cursor.execute(sql)
        raw_conn.commit()

    try:
        df_stkfundscore.to_sql(name = 'FUND_STKFUNDSCORE', con = conn_to, index = False, if_exists = 'append', chunksize = 10000)
        print('[FUND_STKFUNDSCORE]: Data refreshed.')
    except:
        sendmsg_text(content = '[FUND_STKFUNDSCORE]: Update failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
        raise SystemError('[FUND_STKFUNDSCORE]: Update failed.')

    return

def db_data_update(uptodate, conn_to, conn_ts, conn_smpp, getfile = False):
    """
    基础数据更新

    Parameters
    ----------
    uptodate : str - 'yyyymmdd' 数据库更新截至时点
    conn_to : dbconnector - 目标数据库链接
    conn_ts : dbconnector - tushare数据库链接
    conn_smpp : dbconnector - smpp数据库链接
    getfile : Boolean - [False] 是否从本地csv更新数据

    Returns
    -------
    """
    update_tradedate(conn_ts, conn_to)
    enddate = get_tradedate(enddate = uptodate)[-1]
    endmdate = get_tradedate(enddate = uptodate, frq = 'M')[-1]

    # Update raw data
    ls_threads = []
    ls_threads.append(Thread(target = update_index_dailymarket, args = (enddate, conn_ts, conn_to)))
    ls_threads.append(Thread(target = update_index_consweight, args = (endmdate, conn_ts, conn_to)))
    ls_threads.append(Thread(target = update_index_swmember, args = (conn_ts, conn_to)))
    ls_threads.append(Thread(target = update_stock_dailymarket, args = (enddate, conn_ts, conn_to)))
    ls_threads.append(Thread(target = update_stock_dailyindicator, args = (enddate, conn_ts, conn_to)))
    ls_threads.append(Thread(target = update_cfuture_dailymarket, args = (enddate, conn_ts, conn_to)))
    ls_threads.append(Thread(target = update_ffuture_dailymarket, args = (enddate, conn_ts, conn_to)))
    ls_threads.append(Thread(target = update_foption_etfmarket, args = (enddate, conn_ts, conn_to)))
    ls_threads.append(Thread(target = update_foption_dailymarket, args = (enddate, conn_ts, conn_to)))
    ls_threads.append(Thread(target = update_cbond_info, args = (conn_ts, conn_to)))
    ls_threads.append(Thread(target = update_cbond_dailymarket, args = (enddate, conn_ts, conn_to)))
    ls_threads.append(Thread(target = update_stock_yearlyreport, args = (enddate, conn_ts, conn_to)))
    # ls_threads.append(Thread(target = update_fund_rawnav, args = (conn_smpp, conn_to)))
    
    for t in ls_threads:
        t.start()
    
    for t in ls_threads:
        t.join()

    # Update handmade data Part 1
    ls_threads = []
    ls_threads.append(Thread(target = update_stock_dailyreturn, args = (enddate, conn_to)))
    ls_threads.append(Thread(target = update_cfuture_maincode, args = (enddate, conn_to)))
    ls_threads.append(Thread(target = update_foption_dailygreeks, args = (enddate, conn_to)))
    ls_threads.append(Thread(target = update_stock_dailywinratio, args = (enddate, conn_to)))
    ls_threads.append(Thread(target = update_foption_volatilitycone, args = (enddate, conn_to)))
    ls_threads.append(Thread(target = update_cfuture_info, args = (getfile, conn_to)))
    ls_threads.append(Thread(target = update_ffuture_info, args = (getfile, conn_to)))
    ls_threads.append(Thread(target = update_fund_weeklymarket, args = (conn_to, )))
    
    for t in ls_threads:
        t.start()
    
    for t in ls_threads:
        t.join()
    
    # Update handmade data Part 2
    ls_threads = []
    ls_threads.append(Thread(target = update_foption_dailyskew, args = (enddate, conn_to)))
    ls_threads.append(Thread(target = update_fund_stkfundscore, args = (conn_to,)))
    
    for t in ls_threads:
        t.start()
    
    for t in ls_threads:
        t.join()


    return

if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('-f', action = 'store_true', help = 'Update from file.')
    parser.add_argument('-day', type = int, default = 1, help = 'Update to N days before today.')
    args = parser.parse_args()

    uptodate = (date.today() - pd.Timedelta('{}D'.format(args.day))).strftime('%Y%m%d')
    conn_vulpes = db_vulpes(writer = True)
    # conn_smpp = db_smpp()
    conn_smpp = None
    conn_ts = api_ts()
    
    try:
        db_data_update(uptodate, conn_to = conn_vulpes, conn_ts = conn_ts, conn_smpp = conn_smpp, getfile = args.f)
        print('[DATA]: Update Finished.')
        # sendmsg_text(content = '【通知】基础数据更新成功\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
    except:
        sendmsg_text(content = '【警告】基础数据更新失败\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
        raise SystemError('[DATA]: Update Failed.')