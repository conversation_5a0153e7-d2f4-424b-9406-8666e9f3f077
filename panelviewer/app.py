# -*- coding: utf-8 -*-
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))

import streamlit as st
import streamlit_authenticator as stauth

import altair as alt
import pandas as pd
from src.basic import get_tradedate
from src.fund import cal_stkfundrawhold, cal_stkfundholdanalyse
from io import BytesIO

import yaml
from yaml.loader import SafeLoader

st.set_page_config(
    page_title = 'PanelViewer - StockFund',
    page_icon = ':clipboard:',
    initial_sidebar_state = 'expanded')

with open('config.yaml') as file:
    config = yaml.load(file, Loader = SafeLoader)

authenticator = stauth.Authenticate(
    config['credentials'],
    config['cookie']['name'],
    config['cookie']['key'],
    config['cookie']['expiry_days'],
)
# authenticator.register_user(pre_authorized=[])
@st.dialog('PanelViewer')
def reset():
    if st.session_state['authentication_status']:
        try:
            if authenticator.reset_password(st.session_state['username'], fields = {
                'Form name': '重置密码',
                'Current password':'当前密码',
                'New password':'新密码',
                'Repeat password': '重复新密码',
                'Reset':'重置'
            }):
                st.success('Password modified successfully')
                with open('config.yaml', 'w') as file:
                    yaml.dump(config, file, default_flow_style = False)
        except Exception as e:
            st.error(e)
    return

@st.cache_data(show_spinner = False, ttl = 3600)
def cal_reports(df_rawhold, fund_name: str, benchmark_code: str, benchmark_date: str):
    dic_results = cal_stkfundholdanalyse(df_rawhold, fund_name, benchmark_code, benchmark_date, save = False)
    return dic_results

def sayhi():
    st.markdown('## Welcome to PanelViewer')
    if st.session_state.vip >= 1:
        st.markdown(':first_place_medal:$~$:rainbow[***{}***]'.format(st.session_state['name']))
    else:
        st.markdown(':second_place_medal:$~$:blue[***{}***]'.format(st.session_state['name']))
    col_left_reset, col_right_logout = st.columns([3, 1])
    with col_left_reset:
        if st.button('重置密码', disabled = st.session_state.vip == 2):
            reset()
    with col_right_logout:
        if st.button('登出'):
            authenticator.logout(location = 'unrendered')
            st.session_state.reports = None
    return

def uploader():
    dic_mbcode = {'沪深300': '000300.SH', '中证500': '000905.SH', '中证1000': '000852.SH', '中证A500': '000510.CSI', '中证2000': '932000.CSI', '无基准': ''}
    need_date = False
    can_read = False
    st.write('1.上传四级估值表')
    file = st.file_uploader(label = '四级估值表', type=['xlsx', 'xls'], accept_multiple_files = False, label_visibility = 'collapsed')

    if file is not None:
        try:
            df_rawhold = cal_stkfundrawhold(file)
            st.success('文件读取成功')
            args_tabledate = st.date_input(label = '估值表日期', value = pd.to_datetime(df_rawhold['日期'][0]), disabled = True)
            
            path_uploads = os.path.join(os.path.dirname(os.path.realpath(__file__)), 'uploads')
            if not os.path.exists(os.path.join(path_uploads, st.session_state.username)):
                os.makedirs(os.path.join(path_uploads, st.session_state.username), exist_ok = True)
            with open(os.path.join(path_uploads, st.session_state.username, file.name), "wb") as f:
                f.write(file.getbuffer())
            
            can_read = True
        except Exception as e:
            if '[WARNING]' in str(e):
                st.warning('未能读取日期，请手动录入')
                need_date = True
            else:
                st.error('文件读取失败，请检查文件格式')
                need_date = False

        if need_date:
            args_tabledate = st.date_input(label = ':red[*]估值表日期', value = None)
            if args_tabledate is not None:
                try:
                    df_rawhold = cal_stkfundrawhold(file, paneldate = pd.to_datetime(args_tabledate).strftime('%Y%m%d'))
                    can_read = True
                except Exception as e:
                    print(f'Failed to cal_stkfundrawhold: {e}')
                    st.error('文件读取失败，请检查文件格式')
            else:
                can_read = False

    if can_read and (st.session_state.vip == 1):
        buffer = BytesIO()
        with pd.ExcelWriter(buffer) as writer:
            df_rawhold.to_excel(writer, index = False)
        st.download_button(
            label = ':inbox_tray: 下载清洗数据',
            data = buffer,
            file_name = 'download.xlsx',
            mime = 'application/vnd.ms-excel',
            disabled = not can_read)
    if can_read:
        st.write('2.参数选择')
        try:
            args_fundname = file.name.split('_')[1].replace('私募证券投资基金', '')
        except Exception as e:
            print(f'Failed to get fund name: {e}')
            args_fundname = ''
        args_fundname = st.text_input(label = ':red[*]基金名称', value = args_fundname).replace(' ', '')

        ls_benchmark = list(dic_mbcode.keys())
        for i_bm in range(len(ls_benchmark)):
            match_bm = ''.join(i for i in ls_benchmark[i_bm] if i.isdigit())
            match_bm = 'A500' if ls_benchmark[i_bm] == '中证A500' else match_bm
            if match_bm in args_fundname:
                break
        
        args_benchmark = st.selectbox(label = ':red[*]业绩基准', options = ls_benchmark, index = i_bm)

        if can_read:
            args_benchmarkdate = get_tradedate(enddate = pd.to_datetime(args_tabledate).strftime('%Y%m%d'), frq = 'M', n = 1)[-1]
            args_benchmarkdate = st.date_input(label = ':red[*]基准日期', value = pd.to_datetime(args_benchmarkdate), help = '基准指数持仓只能选择月底交易日')
            args_benchmarkdate = pd.to_datetime(args_benchmarkdate).strftime('%Y%m%d')

        if can_read & (len(args_fundname) > 0):
            can_analyse = True
        else:
            can_analyse = False

        if st.button(label = ':rocket: 开始分析', disabled = not can_analyse):
            with st.spinner('分析中...', show_time = True):
                try:
                    st.session_state.reports = cal_reports(df_rawhold, args_fundname, dic_mbcode[args_benchmark], args_benchmarkdate)
                except Exception as e:
                    print(f'Failed to cal_reports: {e}')
                    st.error(':x:分析失败')
                
                if st.session_state.reports['sta'].empty:
                    st.error(':x: 无股票持仓')
                else:
                    st.info(':sparkles: 分析完成')

def report_stk(dic_results):
    ##########################################################################################
    st.markdown('### 股票持仓分析')
    df_info = dic_results['info'].copy()
    print(df_info)
    df_info['基准占比'] = df_info['基准占比'].map('{:.2%}'.format)
    
    # df_info = df_info.astype(str)
    info_benchmark = df_info['基准指数'].values[0]

    st.dataframe(df_info, use_container_width = True)

    col_left_info, col_right_weight = st.columns(2, gap = 'medium')
    df_sta = dic_results['sta'].map('{:.2%}'.format).T
    df_sta['持股数'] =  dic_results['sta'].T['持股数'].map('{:.0f}'.format)
    df_sta['持股市值'] =  dic_results['sta'].T['持股市值'].map('{:,.2f}'.format)
    df_sta['盈亏比'] =  dic_results['sta'].T['盈亏比'].map('{:.2f}'.format)
    col_left_info.dataframe(df_sta.T.astype(str), use_container_width = True)
    ##########################################################################################
    col_right_weight.markdown('##### 持股集中度分布')
    df_weight = dic_results['weight'].set_index('权重区间')[['持股权重', '数量权重']].copy()
    df_weight.columns = ['权重占比', '股数占比']
    df_weight = df_weight.stack().reset_index()
    df_weight.columns = ['权重区间', '分组类型', '占比']
    bar_weight = alt.Chart(df_weight, height = 380).mark_bar().encode(
        x = alt.X('权重区间'),
        xOffset = '分组类型',
        y = alt.Y('占比', axis = alt.Axis(grid = True), title = None, ).axis(format = '.1%'),
        color = alt.Color('分组类型', legend = {'orient': 'top-right', 'title': None}),
    )
    col_right_weight.altair_chart(bar_weight, use_container_width = True)
    ##########################################################################################
    col_left_style, col_right_index = st.columns([4, 6], gap = 'medium')
    col_left_style.markdown('##### 持仓风格$~$-$~${}'.format(info_benchmark))
    
    df_style = dic_results['style'].head(10).copy()
    df_style['绝对偏离'] = df_style['风格偏离'].abs()
    df_style_most = df_style.sort_values('绝对偏离', ascending = False).head(1)
    style_name = df_style_most['风格因子'].values[0]
    style_diff = df_style_most['风格偏离'].values[0]
    style_color = 'red' if style_diff >= 0 else 'green'
    col_left_style.markdown('最大偏离风格$~$**{}**$~$:{}[**{:.2f}**]'.format(style_name, style_color, style_diff))

    tab_style_diff, tab_style_all = col_left_style.tabs(['风格偏离', '风格分布'])
    bar_style_diff = alt.Chart(df_style, height = 400).mark_bar().encode(
        x = alt.X('风格偏离', axis = alt.Axis(grid = True), title = None).axis(format = '.2f'),
        y = alt.Y('风格因子', sort = df_style['风格因子'].tolist(), title = None),
    )
    tab_style_diff.altair_chart(bar_style_diff, use_container_width = True)
    
    bar_style_all = alt.Chart(df_style, height = 400).mark_bar().encode(
        x = alt.X('风格敞口', axis = alt.Axis(grid = True), title = None).axis(format = '.2f'),
        y = alt.Y('风格因子', sort = df_style['风格因子'].tolist(), title = None),
    )
    tab_style_all.altair_chart(bar_style_all, use_container_width = True)
    ##########################################################################################
    col_right_index.markdown('##### 指数成分股占比')
    col_right_index.markdown('$~$')
    df_index = dic_results['index'].sort_values(by = '持股权重', ascending = False)
    df_index['权重占比'] = (df_index['持股权重'] * 100).round(2)
    index_name = df_index['成分指数'].values[0]
    index_name = '其他小市值股' if index_name == '其他' else index_name
    index_weight = df_index['持股权重'].values[0]
    col_right_index.markdown('持仓集中于:blue[**{}**]范围$~$权重占比:blue[**{:.2%}**]'.format(index_name, index_weight))
    chart_index = alt.Chart(df_index, height = 400).encode(
        theta = alt.Theta('权重占比:Q', stack = True),
        color = alt.Color('成分指数:N', legend = None),
        order = alt.Order('权重占比:Q', sort = 'descending'),
    )
    pie_index = chart_index.mark_arc(outerRadius = 120, innerRadius = 70)
    pie_text = chart_index.mark_text(radius = 150, size = 12, font = 'bold').encode(text = '成分指数:N')
    pie_pct = chart_index.mark_text(radius = 95, size = 12, fill = 'white').encode(text = alt.Text('持股权重:Q', format = '.2%'))
    col_right_index.altair_chart(pie_index + pie_text + pie_pct, use_container_width = True)
    ##########################################################################################
    st.markdown('##### 持仓行业')
    df_industry_diff = dic_results['industry'].sort_values('行业偏离', ascending = True).copy()
    df_industry_diff['偏离'] = ''
    df_industry_diff.loc[df_industry_diff['行业偏离'] > 0, '偏离'] = '超配'
    df_industry_diff.loc[df_industry_diff['行业偏离'] == 0, '偏离'] = '平配'
    df_industry_diff.loc[df_industry_diff['行业偏离'] < 0, '偏离'] = '欠配'
    
    industry_min = df_industry_diff['申万一级'].values[0]
    industry_max = df_industry_diff['申万一级'].values[-1]
    industry_diff_min = df_industry_diff['行业偏离'].values[0]
    industry_diff_max = df_industry_diff['行业偏离'].values[-1]
    st.markdown('相对**{}**最大超配行业$~$**{}**$~$:red[**{:.2%}**]$~~$最大欠配行业$~$**{}**$~$:green[**{:.2%}**]'.format(
        info_benchmark, industry_max, industry_diff_max, industry_min, industry_diff_min))

    tab_industry_diff, tab_industry_all = st.tabs(['行业偏离', '行业分布'])
    bar_indestry_diff = alt.Chart(df_industry_diff).mark_bar().encode(
        x = alt.X('申万一级', sort = df_industry_diff['行业偏离'].tolist()),
        y = alt.Y('行业偏离', axis = alt.Axis(grid = True), title = None).axis(format = '.2%'),
        color = alt.Color('偏离', sort = ['超配', '平配', '欠配']).legend(None),
    )
    tab_industry_diff.altair_chart(bar_indestry_diff, use_container_width = True)

    df_industry_all = dic_results['industry'].sort_values('持股权重', ascending = True).copy()
    bar_indestry_all = alt.Chart(df_industry_all).mark_bar().encode(
        x = alt.X('申万一级', sort = df_industry_diff['行业偏离'].tolist()),
        y = alt.Y('持股权重', axis = alt.Axis(grid = True), title = None).axis(format = '.2%'),
    )
    tab_industry_all.altair_chart(bar_indestry_all, use_container_width = True)
    ##########################################################################################
    col_left_ic, col_right_winratio = st.columns(2)
    col_left_ic.markdown('##### 持股预测天数')
    
    df_ic = dic_results['ic'].copy()
    df_icstack = df_ic.stack().reset_index()
    df_icstack.columns = ['持仓时间', '分组', 'IC']
    ic_tbest = df_ic.sort_values('全持仓', ascending = False).index[0]
    ic_bestvalue = df_ic['全持仓'].max().round(3)
    ic_bestcolor = 'red' if ic_bestvalue > 0 else 'green'

    df_ric = dic_results['ric'].copy()
    df_ricstack = df_ric.stack().reset_index()
    df_ricstack.columns = ['持仓时间', '分组', 'IC']
    ric_tbest = df_ric.sort_values('全持仓', ascending = False).index[0]
    ric_bestvalue = df_ric['全持仓'].max().round(3)
    ric_bestcolor = 'red' if ric_bestvalue > 0 else 'green'

    col_left_ic.markdown(':blue[**IC全持仓**]最佳预测周期:blue[**{}**]$~~$IC = :{}[{}]'.format(ic_tbest, ic_bestcolor, ic_bestvalue))
    col_left_ic.markdown(':blue[**RIC全持仓**]最佳预测周期:blue[**{}**]$~~$RankIC = :{}[{}]'.format(ric_tbest, ric_bestcolor, ric_bestvalue))
    
    ls_ysort = df_ic.index.tolist()
    ls_ysort.reverse()
    
    df_ic2fold = df_ic[['全持仓', '前50%', '后50%']].stack().reset_index()
    df_ic2fold.columns = ['持仓时间', '分组', 'IC']
    df_ic2fold['MAX'] = df_ic2fold['IC'].max() * 1.5
    
    df_ic4fold = df_ic[['前25%', '前25-50%', '后25-50%', '后25%']].stack().reset_index()
    df_ic4fold.columns = ['持仓时间', '分组', 'IC']
    df_ic4fold['MAX'] = df_ic4fold['IC'].max() * 1.5
    
    df_ric2fold = df_ric[['全持仓', '前50%', '后50%']].stack().reset_index()
    df_ric2fold.columns = ['持仓时间', '分组', 'IC']
    df_ric2fold['MAX'] = df_ric2fold['IC'].max() * 1.5
    
    df_ric4fold = df_ric[['前25%', '前25-50%', '后25-50%', '后25%']].stack().reset_index()
    df_ric4fold.columns = ['持仓时间', '分组', 'IC']
    df_ric4fold['MAX'] = df_ric4fold['IC'].max() * 1.5
    
    # Add Tabs
    tab_ic2fold, tab_ic4fold, tab_ric2fold, tab_ric4fold = col_left_ic.tabs(['50%分组IC', '25%分组IC', '50%分组RIC', '25%分组RIC'])
    # Darw ic 2folds
    bar_ic2fold = alt.Chart(df_ic2fold, height = 160, width = 250).mark_bar(point = True).encode(
        y = alt.Y('持仓时间', sort = ls_ysort, title = None),
        # row = alt.Row('分组', sort = ['全持仓', '前50%', '后50%'], title = None),
        x = alt.X('IC', axis = alt.Axis(grid = True), title = None).axis(format = '.3f'),
        color = alt.Color('分组', sort = df_ic.columns.tolist(), legend = None),
    )
    text_ic2fold = bar_ic2fold.mark_text(
        align = 'right',
        baseline = 'middle',
        font = 'bold',
        # dx = 40
    ).encode(
        x = alt.X('MAX', axis = alt.Axis(grid = True), title = None).axis(format = '.3f'),
        text = alt.Text('IC:Q', format = '.3f')
    )
    faceted_ic2fold = (bar_ic2fold + text_ic2fold).facet(row = alt.Row('分组', sort = ['全持仓', '前50%', '后50%'], title = None))
    tab_ic2fold.altair_chart(faceted_ic2fold, use_container_width = True)
    # Darw ic 4folds
    bar_ic4fold = alt.Chart(df_ic4fold, height = 120, width = 250).mark_bar(point = True).encode(
        y = alt.Y('持仓时间', sort = ls_ysort, title = None),
        # row = alt.Row('分组', sort = ['前25%', '前25-50%', '后25-50%', '后25%'], title = None),
        x = alt.X('IC', axis = alt.Axis(grid = True), title = None).axis(format = '.3f'),
        color = alt.Color('分组', sort = df_ic.columns.tolist(), legend = None),
    )
    text_ic4fold = bar_ic4fold.mark_text(
        align = 'right',
        baseline = 'middle',
        font = 'bold',
        # dx = 40
    ).encode(
        x = alt.X('MAX', axis = alt.Axis(grid = True), title = None).axis(format = '.3f'),
        text = alt.Text('IC:Q', format = '.3f')
    )
    faceted_ic4fold = (bar_ic4fold + text_ic4fold).facet(row = alt.Row('分组', sort = ['前25%', '前25-50%', '后25-50%', '后25%'], title = None))
    tab_ic4fold.altair_chart(faceted_ic4fold, use_container_width = True)
    # Darw rank ic 2folds
    bar_ric2fold = alt.Chart(df_ric2fold, height = 160, width = 250).mark_bar(point = True).encode(
        y = alt.Y('持仓时间', sort = ls_ysort, title = None),
        # row = alt.Row('分组', sort = ['全持仓', '前50%', '后50%'], title = None),
        x = alt.X('IC', axis = alt.Axis(grid = True), title = None).axis(format = '.3f'),
        color = alt.Color('分组', sort = df_ric.columns.tolist(), legend = None),
    )
    text_ric2fold = bar_ric2fold.mark_text(
        align = 'right',
        baseline = 'middle',
        font = 'bold',
        # dx = 40
    ).encode(
        x = alt.X('MAX', axis = alt.Axis(grid = True), title = None).axis(format = '.3f'),
        text = alt.Text('IC:Q', format = '.3f')
    )
    faceted_ric2fold = (bar_ric2fold + text_ric2fold).facet(row = alt.Row('分组', sort = ['全持仓', '前50%', '后50%'], title = None))
    tab_ric2fold.altair_chart(faceted_ric2fold, use_container_width = True)
    # Darw rank ic 4folds
    bar_ric4fold = alt.Chart(df_ric4fold, height = 120, width = 250).mark_bar(point = True).encode(
        y = alt.Y('持仓时间', sort = ls_ysort, title = None),
        # row = alt.Row('分组', sort = ['前25%', '前25-50%', '后25-50%', '后25%'], title = None),
        x = alt.X('IC', axis = alt.Axis(grid = True), title = None).axis(format = '.3f'),
        color = alt.Color('分组', sort = df_ric.columns.tolist(), legend = None),
    )
    text_ric4fold = bar_ric4fold.mark_text(
        align = 'right',
        baseline = 'middle',
        font = 'bold',
        # dx = 40
    ).encode(
        x = alt.X('MAX', axis = alt.Axis(grid = True), title = None).axis(format = '.3f'),
        text = alt.Text('IC:Q', format = '.3f')
    )
    faceted_ric4fold = (bar_ric4fold + text_ric4fold).facet(row = alt.Row('分组', sort = ['前25%', '前25-50%', '后25-50%', '后25%'], title = None))
    tab_ric4fold.altair_chart(faceted_ric4fold, use_container_width = True)
    ##########################################################################################
    col_right_winratio.markdown('##### 持股超额胜率$~$-$~$:red[{}]'.format(info_benchmark))
    df_predict = dic_results['predict'][['超额数量胜率', '超额权重胜率', '超额盈亏比']].copy()
    df_predict.columns = ['数量胜率', '权重胜率', '盈亏比']

    predict_pnum = df_predict.sort_values('数量胜率', ascending = False).index[0]
    predict_rnum = df_predict['数量胜率'][predict_pnum]
    predict_pweight = df_predict.sort_values('权重胜率', ascending = False).index[0]
    predict_rweight = df_predict['权重胜率'][predict_pweight]
    predict_pwlr = df_predict.sort_values('盈亏比', ascending = False).index[0]
    predict_vpwlr = df_predict['盈亏比'][predict_pwlr]
    
    col_right_winratio.markdown('持股数量最优周期:red[**{}**]$~~$超额胜率:red[**{:0.2%}**]'.format(predict_pnum, predict_rnum))
    col_right_winratio.markdown('持股权重最优周期:blue[**{}**]$~~$超额胜率:blue[**{:0.2%}**]'.format(predict_pweight, predict_rweight))
    col_right_winratio.markdown('盈亏比最优周期:green[**{}**]$~~$超额盈亏比:green[**{:0.2f}**]'.format(predict_pwlr, predict_vpwlr))

    predict_style = df_predict.style.\
    format({
        '数量胜率': '{:.2%}', 
        '权重胜率': '{:.2%}', 
        '盈亏比': '{:.2f}'}).\
    highlight_max(subset = ['数量胜率', '权重胜率'], axis = 0, color = 'pink').\
    background_gradient(subset = '盈亏比', cmap = 'Blues')
    col_right_winratio.dataframe(predict_style, use_container_width = True)
    
    return

def report_ffut(dic_results):
    df_sta_ffut = dic_results['sta_ffut'].T
    st.markdown('### 期货持仓分析')
    col_left_ffut, col_right_hedge = st.columns([1, 1])
    
    df_sta_ffut['合约市值'] =  df_sta_ffut['合约市值'].map('{:,.2f}'.format)
    df_sta_ffut['最大持仓市值'] =  df_sta_ffut['最大持仓市值'].map('{:,.2f}'.format)
    df_sta_ffut['合约仓位'] =  df_sta_ffut['合约仓位'].map('{:.2%}'.format)
    df_sta_ffut['最大持仓比例'] =  df_sta_ffut['最大持仓比例'].map('{:.2%}'.format)
    col_left_ffut.dataframe(df_sta_ffut.T.astype(str), use_container_width = True)
    
    df_hvalue_stk = dic_results['rawhold_stock'].groupby('多空方向')[['市值']].sum().reset_index()
    df_hvalue_fut = dic_results['rawhold_ffuture'].groupby(['合约类型', '多空方向'])[['市值']].sum().reset_index().rename(columns = {'合约类型': '标的资产'})
    df_hvalue_stk['标的资产'] = '股票'
    df_hvalue = pd.concat([df_hvalue_stk, df_hvalue_fut], ignore_index = True).replace({'多空方向': {1: '多头', -1: '空头'}})
    df_hvalue['相对权重'] = df_hvalue['市值'] / df_hvalue_stk.query('多空方向 == 1')['市值'].sum()
    
    bar_weight = alt.Chart(df_hvalue, height = 280).mark_bar().encode(
        x = alt.X('多空方向', title = None, sort = ['多头', '空头'], axis = alt.Axis(grid = False, labelAngle = 0)),
        y = alt.Y('相对权重', axis = alt.Axis(grid = True), title = None).axis(format = '.2%'),
        color = alt.Color('标的资产', title = None),
    )
    col_right_hedge.altair_chart(bar_weight, use_container_width = True)
    
    df_hvalue_order = df_hvalue.query("标的资产 != '股票'").sort_values(by = '市值', ascending = False).copy()
    ls_hvalue = []
    for i in range(df_hvalue_order.shape[0]):
        if df_hvalue_order.iloc[i]['多空方向'] == '多头':
            ls_hvalue.append(':red[**' + df_hvalue_order[['相对权重']].iloc[i].map('{:+.1%}'.format).values[0] + '**]**' + df_hvalue_order.iloc[i]['标的资产'] + '**')
        else:
            ls_hvalue.append(':green[**' + df_hvalue_order[['相对权重']].iloc[i].map('{:+.1%}'.format).values[0] + '**]**' + df_hvalue_order.iloc[i]['标的资产'] + '**')
    st.markdown('综合:red[**100%**]**股票多头**$~$' + '$~$'.join(ls_hvalue) + '计算组合风格与行业敞口')

    st.markdown('##### 风格敞口')
    df_style = dic_results['style'].head(10).copy()
    df_style['绝对综合敞口'] = df_style['综合敞口'].abs()
    df_style_most = df_style.sort_values('绝对综合敞口', ascending = False).head(1)
    style_name = df_style_most['风格因子'].values[0]
    style_diff = df_style_most['综合敞口'].values[0]
    style_color = 'red' if style_diff >= 0 else 'green'
    st.markdown('最大风格敞口$~$**{}**$~$:{}[**{:.2f}**]'.format(style_name, style_color, style_diff))

    bar_style_diff = alt.Chart(df_style).mark_bar().encode(
        x = alt.X('风格因子', sort = df_style['风格因子'].tolist(), title = None, axis = alt.Axis(grid = False, labelAngle = -45)),
        y = alt.Y('综合敞口', axis = alt.Axis(grid = True), title = None).axis(format = '.2f'),
        
    )
    st.altair_chart(bar_style_diff, use_container_width = True)
    
    st.markdown('##### 行业敞口')
    df_industry_diff = dic_results['industry'].sort_values('综合敞口', ascending = True).copy()
    df_industry_diff['偏离'] = ''
    df_industry_diff.loc[df_industry_diff['综合敞口'] > 0, '偏离'] = '超配'
    df_industry_diff.loc[df_industry_diff['综合敞口'] == 0, '偏离'] = '平配'
    df_industry_diff.loc[df_industry_diff['综合敞口'] < 0, '偏离'] = '欠配'
    
    industry_min = df_industry_diff['申万一级'].values[0]
    industry_max = df_industry_diff['申万一级'].values[-1]
    industry_diff_min = df_industry_diff['综合敞口'].values[0]
    industry_diff_max = df_industry_diff['综合敞口'].values[-1]
    st.markdown('最大超配行业$~$**{}**$~$:red[**{:.2%}**]$~~$最大欠配行业$~$**{}**$~$:green[**{:.2%}**]'.format(
        industry_max, industry_diff_max, industry_min, industry_diff_min))

    bar_indestry_diff = alt.Chart(df_industry_diff).mark_bar().encode(
        x = alt.X('申万一级', sort = df_industry_diff['综合敞口'].tolist()),
        y = alt.Y('综合敞口', axis = alt.Axis(grid = True), title = None).axis(format = '.2%'),
        color = alt.Color('偏离', sort = ['超配', '平配', '欠配']).legend(None),
    )
    st.altair_chart(bar_indestry_diff, use_container_width = True)

    return

@st.fragment
def reporter(dic_results):
    if not dic_results['sta'].empty:
        report_stk(dic_results)
    
    if not dic_results['sta_ffut'].empty:
        report_ffut(dic_results)
    
    return
#===================================================================================================
# Main Page

authenticator.login(fields = {
    'Form name': '系统登录',
    'Username':'用户名', 
    'Password':'密码', 
    'Login':'Login'})

if st.session_state['authentication_status']:
    st.session_state.vip = config['credentials']['usernames'][st.session_state.username]['vip']
    st.session_state.validdate = config['credentials']['usernames'][st.session_state.username]['validdate']
    if st.session_state.validdate == '':
        st.session_state.isvalid = True
    elif pd.Timestamp.today() <= pd.to_datetime(st.session_state.validdate):
        st.session_state.isvalid = True
    else:
        st.session_state.isvalid = False

    if 'reports' not in st.session_state:
        st.session_state.reports = None

    with st.sidebar:
        sayhi()

    if st.session_state.isvalid:
        with st.sidebar:
            uploader()
        if st.session_state.reports is not None:
            reporter(st.session_state.reports)
    else:
        st.error('账户已过期，请联系管理员')

elif st.session_state['authentication_status'] is False:
    st.error('用户名/密码错误，请重新输入')
elif st.session_state['authentication_status'] is None:
    pass
    # st.warning('Please enter your username and password')

with open('config.yaml', 'w') as file:
    yaml.dump(config, file, default_flow_style = False)

#===================================================================================================