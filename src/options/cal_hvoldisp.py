# -*- coding: utf-8 -*-
# 计算ETF期权标的资产历史波动率

import pandas as pd
from basic.db_conn import db_vulpes

def cal_hvoldisp(ls_twindow = [1, 3, 5, 10], wl = 750, dl = 250):
    '''
    计算ETF期权标的资产历史波动率

    Parameters
    ----------
    ls_twindow : list - 用于计算波动率的收益率跨度
    wl : int - 波动率测算样本长度, 默认滚动测算3年
    dl : int - 波动率时序展示区间, 默认展示最近1年
    
    Returns
    -------
    DataFrame
    '''

    conn = db_vulpes()
    sql = '''
    SELECT
        FUND_CODE,
        TRADEDATE,
        RETURN,
        FUND_NAME,
        EXCHANGE
    FROM FOPTION_ETFMARKET
    '''
    df_etfmkt = pd.read_sql(sql, con = conn)
    df_cumret = (df_etfmkt.pivot(index = 'TRADEDATE', columns = 'FUND_CODE', values = 'RETURN').tail(wl + dl + max(ls_twindow)) + 1).cumprod()

    df_output = pd.DataFrame(index = df_cumret.columns)
    # 各收益率跨度下，展示期内各ETF的波动率序列
    dic_tvol = {}
    for t_window in ls_twindow:
        df_output['{}D'.format(t_window)] = df_cumret.iloc[range(df_cumret.shape[0] - 1, df_cumret.shape[0] - wl - 1, -t_window)].sort_index().pct_change(fill_method = None).std()

        ls_tvol = []
        for d in range(0, dl):
            ls_tvol.append(df_cumret.iloc[range(df_cumret.shape[0] - 1 - d, df_cumret.shape[0] - wl - 1 - d, -t_window)].sort_index().pct_change(fill_method = None).std().to_frame(name = df_cumret.index[-d - 1]).T)
        dic_tvol[t_window] = pd.concat(ls_tvol, axis = 0).sort_index()

    # 各ETF，展示期内各收益率跨度下的波动率序列
    dic_cvol = {}
    for code in df_cumret.columns:
        dic_cvol[code] = pd.DataFrame()
        for t_window in ls_twindow:
            dic_cvol[code]['{}D'.format(t_window)] = dic_tvol[t_window][code]

    df_output = (df_output * 100).round(2).astype(str) + '%'

    df_output = df_etfmkt.drop(columns = ['TRADEDATE', 'RETURN']).drop_duplicates().set_index('FUND_CODE').join(df_output).reset_index()
    df_output = df_output.sort_values('EXCHANGE', ascending = True).set_index(['EXCHANGE', 'FUND_CODE'])

    return df_output, dic_tvol, dic_cvol

if __name__ == '__main__':
    pass