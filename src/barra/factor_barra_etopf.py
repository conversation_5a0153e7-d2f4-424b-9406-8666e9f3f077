# -*- coding: utf-8 -*-

def factor_barra_etopf(df_dcons, ls_tdate):
    """
    计算ETOPF因子

    Computed by dividing the 12-month forward-looking earnings by the current market capitalization.

    Parameters
    ----------
        df_dcons : DataFrame - 日频股票分析师预期数据
        ls_tdate : List [str] - 交易日序列

    Returns
    -------
    DataFrame

    """
    df_output = df_dcons[df_dcons['TRADEDATE'].isin(ls_tdate)][['TRADEDATE', 'STOCK_CODE', 'EST_PE']].copy()
    df_output.columns = ['TRADEDATE', 'STOCK_CODE', 'VALUE']
    df_output['VALUE'] = 1 / df_output['VALUE']

    df_output['FACTOR'] = 'ETOPF'

    return df_output

if __name__ == '__main__':
    pass