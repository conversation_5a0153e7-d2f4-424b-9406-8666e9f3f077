# Linux系统如何设置SimHei字体

1. 找到本地缓存并删除

```python
import matplotlib as mpl
print(mpl.get_cachedir())
# /root/.cache/matplotlib
```

可以在先备份的前提下删除缓存文件夹内的文件: rm -r /root/.cache/matplotlib/*

2. 将SimHei.ttf字体文件存入~/.fonts文件夹

访问文件夹: cd ~/.fonts

如果没有文件夹可以新建: mkdir ~/.fonts

```text
cp /root/zerda/fonts/SimHei.ttf /root/.fonts/
```

3. 安装fontconfig

```text
# 如果你是centos 
sudo yum install fontconfig -y

# 如果你是ubuntu
sudo apt-get install fontconfig -y

# 如果你是mac
brew install fontconfig
```

4. 刷新字体缓存

```text
fc-cache -fv
```
