# -*- coding: utf-8 -*-
# 提取交易日时序，可根据始末时点或者其中一个时点与窗口天数返回交易日时序，默认为全时序

import pandas as pd
from basic import db_vulpes

def get_tradedate(startdate = None, enddate = None, n = 0, frq = 'D', form = 'str'):
    """
    提取交易日时序，可根据始末时点或者其中一个时点与窗口天数返回交易日时序，默认为全时序

    Parameters
    ----------
    startdate : str - 'yyyymmdd' 时间窗口起点
    enddate : str - 'yyyymmdd' 时间窗口终点
    n: int - [0] 窗口天数
    frq : str - ['D', 'W', 'M', 'S', 'Y'] 交易日序列频率，'D'为日频，'W'为周频，'M'为月频，'S'为季频，'Y'为年频
    form : str - ['str', 'datetime'] 序列格式，默认为字符串格式'yyyymmdd'

    Returns
    -------
    List ['yyyymmdd'] 交易日时序
    """
    if frq not in ['D', 'W', 'M', 'S', 'Y']:
        raise Exception("Parameter [frq] must be in ['D', 'W', 'M', 'S', 'Y'].")

    if n < 0:
        raise Exception("Parameter [n] must be nonnegative.")
    
    conn = db_vulpes()
    sql = '''
    SELECT 
        CAL_DATE
    FROM BASIC_TRADEDATE
    WHERE
        FRQ_{} = 1
    ORDER BY CAL_DATE;
    '''.format(frq)
    ls_out = pd.read_sql(sql, con = conn).sort_values('CAL_DATE', ascending = True)['CAL_DATE'].tolist()

    if startdate is None:
        if enddate is None:
            ls_output = ls_out
        elif n > 0:
            ls_output = [i for i in ls_out if i <= enddate][-n: ]
        else:
            ls_output = [i for i in ls_out if i <= enddate]
    elif enddate is None:
        if n > 0:
            ls_output = [i for i in ls_out if i >= startdate][: n]
        else:
            ls_output = [i for i in ls_out if i >= startdate]
    else:
        ls_output = [i for i in ls_out if i >= startdate and i <= enddate]

    if form == 'str':
        pass
    elif form == 'datetime':
        ls_output = [pd.to_datetime(i, format = '%Y%m%d') for i in ls_output]
    else:
        raise Exception("Parameter [form] must be 'str' or 'datetime'.")

    return ls_output

if __name__ == '__main__':
    pass