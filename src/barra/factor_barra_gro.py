# -*- coding: utf-8 -*-

import numpy as np
import pandas as pd
from barra import mod_makeupann

def factor_barra_gro(df_yreport, ls_tdate):
    """
    计算GRO类因子，包含EGRO, SGRO, AGRO, IGRO

    Computed by dividing the slope coefficient from the regression of the annual *** 
    for the last five fiscal years against time, by the average annual ***.

    Parameters
    ----------
        df_yreport : DataFrame - 年度财务指标表数据
        ls_tdate : List [str] - 交易日序列

    Returns
    -------
    DataFrame
    
    """
    # five fiscal years
    n = 5

    df_NI = df_yreport.pivot(index = 'REPORT_DATE', columns = 'STOCK_CODE', values = 'NI')
    df_TS = df_yreport.pivot(index = 'REPORT_DATE', columns = 'STOCK_CODE', values = 'TS')
    df_EPS = df_NI / df_TS
    df_TOR = df_yreport.pivot(index = 'REPORT_DATE', columns = 'STOCK_CODE', values = 'TOR')
    df_TA = df_yreport.pivot(index = 'REPORT_DATE', columns = 'STOCK_CODE', values = 'TA')
    df_TE = df_yreport.pivot(index = 'REPORT_DATE', columns = 'STOCK_CODE', values = 'TE')
    df_ANN = df_yreport.pivot(index = 'REPORT_DATE', columns = 'STOCK_CODE', values = 'DISCLOSE_DATE')

    X = np.array([[1] * n, range(1, n + 1)])
    RX = np.linalg.inv(np.dot(X, X.T))

    # Rolling计算回归
    df_EGRO = df_EPS.rolling(n).apply(lambda x: np.dot(RX, np.dot(X, x))[-1]) / df_EPS.rolling(n).mean()
    df_EGRO.dropna(how = 'all', inplace = True)

    df_SGRO = df_TOR.rolling(n).apply(lambda x: np.dot(RX, np.dot(X, x))[-1]) / df_TOR.rolling(n).mean()
    df_SGRO.dropna(how = 'all', inplace = True)

    df_AGRO = df_TA.rolling(n).apply(lambda x: np.dot(RX, np.dot(X, x))[-1]) / df_TA.rolling(n).mean()
    df_AGRO.dropna(how = 'all', inplace = True)

    df_IGRO = df_TE.rolling(n).apply(lambda x: np.dot(RX, np.dot(X, x))[-1]) / df_TE.rolling(n).mean()
    df_IGRO.dropna(how = 'all', inplace = True)

    df_dEGRO = mod_makeupann(df_EGRO, df_ANN, ls_tdate).stack().reset_index()
    df_dSGRO = mod_makeupann(df_SGRO, df_ANN, ls_tdate).stack().reset_index()
    df_dAGRO = mod_makeupann(df_AGRO, df_ANN, ls_tdate).stack().reset_index()
    df_dIGRO = mod_makeupann(df_IGRO, df_ANN, ls_tdate).stack().reset_index()

    df_dEGRO.columns = ['TRADEDATE', 'STOCK_CODE', 'VALUE']
    df_dSGRO.columns = ['TRADEDATE', 'STOCK_CODE', 'VALUE']
    df_dAGRO.columns = ['TRADEDATE', 'STOCK_CODE', 'VALUE']
    df_dIGRO.columns = ['TRADEDATE', 'STOCK_CODE', 'VALUE']

    df_dEGRO['FACTOR'] = 'EGRO'
    df_dSGRO['FACTOR'] = 'SGRO'
    df_dAGRO['FACTOR'] = 'AGRO'
    df_dIGRO['FACTOR'] = 'IGRO'

    df_output = pd.concat([df_dEGRO, df_dSGRO, df_dAGRO, df_dIGRO], ignore_index = True)

    return df_output

if __name__ == '__main__':
    pass