# Linux下修改psql命令行方向键与退格键乱码

Linux(Debian环境)下使用Postgresql的psql命令行会出现方向键与退格键乱码，可以通过安装readline和rlwrap包来解决相关问题。

1. 安装readline和rlwrap

   ```
   sudo apt-get -y install readline*
   sudo apt-get -y install rlwrap
   ```
2. 进入postgres用户，并修改~/.bash_profile或者~/.profile文件

   ```
   su - postgres
   vim ~/.profile
   ```

   在文件中加入命令

   ```
   alias psql='rlwrap psql'
   ```
3. 刷新配置文件

   ```
   source ~/.profile

   # 测试
   psql
   ```
