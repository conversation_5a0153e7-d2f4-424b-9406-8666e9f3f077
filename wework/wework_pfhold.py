#!/usr/bin/env python
# -*- encoding:utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))

from src.portfolio import PortfolioOnline
import pandas as pd

from wework.wework_msg import *

def send_pfhold(userid: str = '', userlist: list = []):
    '''
    刷新持仓基金数据并汇报
    '''
    try:
        pfo = PortfolioOnline()
        pfo.run()
        df_fundsta = pfo.df_statistics.copy()
        # ls_fundname = df_fundsta['FUND_NAME'].tolist()
        ls_fundname = ['慕途稳健3号']
        ls_text = []
        for fundname in ls_fundname:
            df_fundsta_fund = df_fundsta[df_fundsta['FUND_NAME'] == fundname].copy()
            media_id = get_media_id(file_path = os.path.join(pfo.path_figfolder, '{}.png'.format(fundname)), file_name = '{}.png'.format(fundname), file_type = 'image')

            if len(userlist) > 0:
                for user in userlist:
                    sendmsg_file(media_id = media_id, userid = user, file_type = 'image')
            else:
                sendmsg_file(media_id = media_id, userid = userid, file_type = 'image')

            ls_text.append('【{}】\n净值日期: {}\n当日收益: {:.3f}%\n近5日收益: {:.3f}%\n持有累计收益: {:.3f}%\n持有年化收益: {:.3f}%\n当前回撤: {:.3f}%'.format(
                fundname,
                df_fundsta_fund['TRADEDATE'].values[0],
                df_fundsta_fund['FUND_RETURN'].values[0] * 100,
                df_fundsta_fund['FUND_WEEKRETURN'].values[0] * 100,
                df_fundsta_fund['FUND_CUMRETURN'].values[0] * 100,
                df_fundsta_fund['HOLD_ANNRETURN'].values[0] * 100,
                df_fundsta_fund['FUND_DRAWDOWN'].values[0] * 100
                ))

        if len(userlist) > 0:
            for user in userlist:
                sendmsg_text(content = '\n\n'.join(ls_text), userid = user)
        else:
            sendmsg_text(content = '\n\n'.join(ls_text), userid = userid)
        
    except:
        sendmsg_text(content = '【警告】报告生成失败', userid = userid)
        raise SystemError('【警告】报告生成失败')

    return

if __name__ == '__main__':
    send_pfhold(userlist = ['Vulpes', 'XiaoXinYi', 'Gengkael']) # 'Gengkael', 