# -*- coding: utf-8 -*-
# 提取计算转债因子所需要的行情数据

import pandas as pd
from basic import db_vulpes

def get_cbond_mktdata(startdate = ''):
    '''
    提取转债行情数据

    Parameters
    ----------
    startdate : str - 行情起始日期

    Returns
    -------
    DataFrame
    '''

    conn = db_vulpes()
    sql = '''
    SELECT
        CBOND_CODE,
        TRADEDATE,
        RETURN,
        VOL,
        CLOSE + CB_PRATE AS DLOW
    FROM
        CBOND_DAILYMARKET
    WHERE
        TRADEDATE >= '{}'
    '''.format(startdate)
    df_mktdata = pd.read_sql(sql, con = conn)

    return df_mktdata

if __name__ == '__main__':
    pass