# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
from basic import *
from barra import *
from inspect import getfullargspec

class Barra():
    '''
    计算Barra因子载荷与截面单纯因子收益率
    '''
    def __init__(self, startdate, enddate, conn_to, df_mktret = None, df_stkqual = None, df_stkind = None):
        '''
        初始化函数，完成数据收集
        '''
        self.ls_tdate = get_tradedate(startdate = startdate, enddate = enddate)
        self.conn = conn_to

        t_start_0 = get_tradedate(enddate = self.ls_tdate[0], n =  300)[0]
        t_start_1 = self.ls_tdate[0]
        t_end = self.ls_tdate[-1]

        if df_mktret is None:
            self.df_mktret = get_indexmkt()
        else:
            self.df_mktret = df_mktret
        
        if df_stkqual is None:
            self.df_stkqual = get_stkqual()
        else:
            self.df_stkqual = df_stkqual
        
        if df_stkind is None:
            self.df_stkind = get_stkindustry()
        else:
            self.df_stkind = df_stkind

         # Download yearly report data
        sql = '''
        SELECT * FROM STOCK_YEARLYREPORT
        '''
        self.df_yrep = pd.read_sql(sql, con = self.conn)
        
        # Download daily return data
        sql = '''
        SELECT 
            TRADEDATE, STOCK_CODE, RETURN
        FROM STOCK_DAILYMARKET
        WHERE 
            TRADEDATE >= '{}' AND
            TRADEDATE <= '{}'
        '''.format(t_start_0, t_end)
        self.df_dret = pd.read_sql(sql, con = self.conn).drop_duplicates()

        # Download daily turnover data
        sql = '''
        SELECT 
            TRADEDATE, STOCK_CODE, TURNOVER
        FROM STOCK_DAILYINDICATOR
        WHERE 
            TRADEDATE >= '{}' AND
            TRADEDATE <= '{}'
        '''.format(t_start_0, t_end)
        self.df_dtover = pd.read_sql(sql, con = self.conn).drop_duplicates()

        # Download daily indicator data
        sql = '''
        SELECT *
        FROM STOCK_DAILYINDICATOR
        WHERE 
            TRADEDATE >= '{}' AND
            TRADEDATE <= '{}'
        '''.format(t_start_1, t_end)
        self.df_dind = pd.read_sql(sql, con = self.conn).drop_duplicates()
        self.df_LMV = self.df_dind.pivot(index = 'TRADEDATE', columns = 'STOCK_CODE', values = 'LMV')
        self.df_MV = self.df_dind.pivot(index = 'TRADEDATE', columns = 'STOCK_CODE', values = 'MV')

        return

    def cal_factorload(self):
        '''
        计算因子载荷，输出可以直接入库的数据结构
        '''
        # Data Setup
        ls_func = [
            factor_barra_ato, 
            factor_barra_atvr,
            factor_barra_blev,
            factor_barra_btop,
            factor_barra_cap,
            factor_barra_cetop,
            factor_barra_cmra,
            factor_barra_dastd,
            factor_barra_dtoa,
            factor_barra_dtop,
            factor_barra_em,
            factor_barra_etop,
            factor_barra_ev,
            factor_barra_gp,
            factor_barra_gpm,
            factor_barra_gro,
            factor_barra_hcapm,
            factor_barra_mlev,
            factor_barra_rstr,
            factor_barra_roa,
            factor_barra_ston,
            factor_barra_strev]

        dic_args = {
            'df_stkqual': self.df_stkqual,
            'df_dret': self.df_dret, 
            'df_dind': self.df_dind, 
            'df_mktret': self.df_mktret, 
            'df_dturnover': self.df_dtover, 
            'df_yreport': self.df_yrep, 
            'ls_tdate': self.ls_tdate,
            'wl': 252,
            'hl': 63}

        ls_factorload = []
        for func in ls_func:
            ls_args = getfullargspec(func).args
            kwargs = {i : dic_args[i] for i in ls_args}
            df_raw = func(**kwargs)

            ls_factors = df_raw['FACTOR'].unique().tolist()
            ls_load = []
            for factor in ls_factors:
                df_tows = df_raw[df_raw['FACTOR'] == factor].pivot(index = 'TRADEDATE', columns = 'STOCK_CODE', values = 'VALUE')
                df_wsr = mod_datawinsorize(df_tows, self.df_stkqual, threshold = 3)
                df_std = mod_datastandardize(df_wsr, self.df_LMV, self.df_stkqual)
                
                df_load = df_std.stack().reset_index().copy()
                df_load.columns = ['TRADEDATE', 'STOCK_CODE', 'LOAD']
                df_load['FACTOR_ID'] = factor
                ls_load.append(df_load.copy())
            df_factorload = pd.concat(ls_load, ignore_index = True)

            ls_factorload.append(df_factorload.copy())
        df_input = pd.concat(ls_factorload, ignore_index = True)
        df_output_L2 = mod_makeupind(df_input, self.df_stkind, self.df_stkqual).dropna()

        self.df_load_L2 = df_output_L2.pivot(index = ['TRADEDATE', 'STOCK_CODE'], columns = 'FACTOR_ID', values = 'LOAD')
        
        df_unstd_L1 = pd.DataFrame(index = self.df_load_L2.index)
        df_unstd_L1['BETA'] = self.df_load_L2['HBETA']
        df_unstd_L1['MOMENTUM'] = 0.5 * self.df_load_L2['RSTR'] + 0.5 * self.df_load_L2['HALPHA']
        df_unstd_L1['SIZE'] = self.df_load_L2['LNCAP']
        df_unstd_L1['EARNING'] = 0.4 * self.df_load_L2['EM'] + 0.2 * self.df_load_L2['ETOP'] + 0.4 * self.df_load_L2['CETOP']
        df_unstd_L1['MIDCAP'] = self.df_load_L2['MIDCAP']
        df_unstd_L1['BTOP'] = self.df_load_L2['BTOP']
        df_unstd_L1['VOLATILITY'] = 0.1 * self.df_load_L2['HSE'] + 0.74 * self.df_load_L2['DASTD'] + 0.16 * self.df_load_L2['CMRA']
        df_unstd_L1['GROWTH'] = 0.5 * self.df_load_L2['SGRO'] + 0.5 * self.df_load_L2['EGRO']
        df_unstd_L1['LEVERAGE'] = 0.38 * self.df_load_L2['MLEV'] + 0.27 * self.df_load_L2['BLEV'] + 0.35 * self.df_load_L2['DTOA']
        df_unstd_L1['LIQUIDITY'] = 0.3 * self.df_load_L2['STOM'] + 0.3 * self.df_load_L2['STOQ'] + 0.2 * self.df_load_L2['STOA']+ 0.2 * self.df_load_L2['ATVR']

        df_unstd_L1['STREVERSAL'] = self.df_load_L2['STREV']
        df_unstd_L1['PROFITABLITY'] = 0.25 * self.df_load_L2['ATO'] + 0.25 * self.df_load_L2['GP'] + 0.25 * self.df_load_L2['GPM'] + 0.25 * self.df_load_L2['ROA']
        df_unstd_L1['EARNINGVAR'] = 0.35 * self.df_load_L2['VSAL'] + 0.35 * self.df_load_L2['VERN'] + 0.3 * self.df_load_L2['VFLO']
        df_unstd_L1['INVENSTMENT'] =  0.5 * self.df_load_L2['AGRO'] + 0.5 * self.df_load_L2['IGRO']
        df_unstd_L1['DIVIDEND'] = self.df_load_L2['DTOP']

        ls_load_L1 = []
        for t in self.ls_tdate:
            df_std = df_unstd_L1.loc[t].div(df_unstd_L1.loc[t].std(axis = 0), axis = 1).reset_index().copy()
            df_std['TRADEDATE'] = t
            ls_load_L1.append(df_std.copy())
        self.df_load_L1 = pd.concat(ls_load_L1, ignore_index = True).set_index(['TRADEDATE', 'STOCK_CODE'])

        df_output_L1 = self.df_load_L1.stack().reset_index().dropna()
        df_output_L1.columns = ['TRADEDATE', 'STOCK_CODE', 'FACTOR_ID', 'LOAD']

        df_israw_L2 = df_output_L2.pivot(index = ['TRADEDATE', 'STOCK_CODE'], columns = 'FACTOR_ID', values = 'ISRAW')
        df_israw_L1 = pd.DataFrame(index = df_israw_L2.index)
        df_israw_L1['BETA'] = df_israw_L2['HBETA']
        df_israw_L1['MOMENTUM'] = df_israw_L2['RSTR'] * df_israw_L2['HALPHA']
        df_israw_L1['SIZE'] = df_israw_L2['LNCAP']
        df_israw_L1['EARNING'] = df_israw_L2['EM'] * df_israw_L2['ETOP'] * df_israw_L2['CETOP']
        df_israw_L1['MIDCAP'] = df_israw_L2['MIDCAP']
        df_israw_L1['BTOP'] = df_israw_L2['BTOP']
        df_israw_L1['VOLATILITY'] = df_israw_L2['HSE'] * df_israw_L2['DASTD'] * df_israw_L2['CMRA']
        df_israw_L1['GROWTH'] = df_israw_L2['SGRO'] * df_israw_L2['EGRO']
        df_israw_L1['LEVERAGE'] = df_israw_L2['MLEV'] * df_israw_L2['BLEV'] * df_israw_L2['DTOA']
        df_israw_L1['LIQUIDITY'] = df_israw_L2['STOM'] * df_israw_L2['STOQ'] * df_israw_L2['STOA']* df_israw_L2['ATVR']

        df_israw_L1['STREVERSAL'] = df_israw_L2['STREV']
        df_israw_L1['PROFITABLITY'] = df_israw_L2['ATO'] * df_israw_L2['GP'] * df_israw_L2['GPM'] * df_israw_L2['ROA']
        df_israw_L1['EARNINGVAR'] = df_israw_L2['VSAL'] * df_israw_L2['VERN'] * df_israw_L2['VFLO']
        df_israw_L1['INVENSTMENT'] =  df_israw_L2['AGRO'] * df_israw_L2['IGRO']
        df_israw_L1['DIVIDEND'] = df_israw_L2['DTOP']

        df_israw_L1 = df_israw_L1.stack().to_frame(name = 'ISRAW').reset_index().rename(columns = {'level_2': 'FACTOR_ID'})
        df_israw_L1['ISRAW'] = df_israw_L1['ISRAW'].astype(int)

        df_output_L1 = df_output_L1.merge(df_israw_L1, on = ['TRADEDATE', 'STOCK_CODE', 'FACTOR_ID'], how = 'left')

        return df_output_L1, df_output_L2

    def cal_factorreturn(self, model: str):
        '''
        计算因子收益率，输出可以直接入库的数据结构
        '''
        if model not in ['CNE5', 'CNE6']:
            raise ValueError('Model must be CNE5 or CNE6')
        
        ls_load = ['BETA', 'MOMENTUM', 'SIZE', 'EARNING', 'MIDCAP', 'BTOP', 'VOLATILITY', 'GROWTH', 'LEVERAGE', 'LIQUIDITY']
        if model == 'CNE6':
            ls_load += ['STREVERSAL', 'PROFITABLITY', 'EARNINGVAR', 'INVENSTMENT', 'DIVIDEND']
        
        df_stkret = self.df_dret.pivot(index = 'TRADEDATE', columns = 'STOCK_CODE', values = 'RETURN')
        ls_factor_ret = []
        ls_rsquared = []

        for i in range(len(self.ls_tdate) - 1):
            t0 = self.ls_tdate[i]
            t1 = self.ls_tdate[i + 1]

            ls_tstk = self.df_stkqual.loc[t0].dropna().index.tolist()
            ls_tstk = [i for i in ls_tstk if i in self.df_LMV.columns]
            ls_tstk = [i for i in ls_tstk if i in self.df_stkind.columns]
            ls_tstk = [i for i in ls_tstk if i in self.df_load_L1.loc[t0].index]
            
            df_reg = self.df_load_L1.loc[t0][ls_load].loc[ls_tstk]
                
            df_reg = pd.concat([pd.get_dummies(self.df_stkind.loc[t0][ls_tstk]) * 1.0, df_reg], axis = 1)
            df_reg['COUNTRY'] = 1

            # 计算限制矩阵
            df_INDLMV = pd.DataFrame()
            df_INDLMV['IND'] = self.df_stkind.loc[t0][ls_tstk]
            df_INDLMV['LMV'] = self.df_LMV.loc[t0][ls_tstk] / self.df_LMV.loc[t0][ls_tstk].sum()


            df_INDLMV = df_INDLMV.groupby('IND').sum()
            ls_INDLMV = df_INDLMV['LMV'].to_list()
            R = np.vstack(([-i / ls_INDLMV[0] for i in ls_INDLMV[1: ]] + [0] * (df_reg.shape[1] - len(ls_INDLMV)), np.eye(df_reg.shape[1] - 1)))

            # 提取收益序列和权重矩阵
            df_reg['ret'] = df_stkret.loc[t1][ls_tstk]
            df_reg['V'] = np.sqrt(self.df_LMV.loc[t0][ls_tstk]) / np.sqrt(self.df_LMV.loc[t0][ls_tstk]).sum()
            df_reg = df_reg.dropna()

            # WLS
            Y = df_reg['ret']
            V = np.diag(df_reg['V'])
            X = (df_reg.drop(['ret', 'V'], axis = 1).values * 1).astype(float)
            XR = X.dot(R).T
            factor_ret = R.dot(np.linalg.inv(XR.dot(V).dot(XR.T)).dot(XR.dot(V).dot(Y)))
            resid = Y - X.dot(factor_ret)

            # 计算coef和Rsquared
            df_fr_t = pd.Series(factor_ret, index = df_reg.drop(['ret', 'V'], axis = 1).columns).to_frame(name = t1).transpose()

            ls_factor_ret.append(df_fr_t)
            ls_rsquared.append(1 - df_reg['V'].dot(resid ** 2) / df_reg['V'].dot((Y - df_reg['V'].dot(Y)) ** 2))

        self.df_factor_ret = pd.concat(ls_factor_ret)
        self.df_factor_ret.index.name = 'TRADEDATE'
        self.df_rsquared = pd.DataFrame({'R_squared': ls_rsquared}, index = self.df_factor_ret.index)
        self.df_rsquared.index.name = 'TRADEDATE'

        df_output = self.df_factor_ret.stack().reset_index()
        df_output.columns = ['TRADEDATE', 'FACTOR_ID', 'RETURN']
        df_output['MODEL'] = model

        return df_output

if __name__ == '__main__':
    pass