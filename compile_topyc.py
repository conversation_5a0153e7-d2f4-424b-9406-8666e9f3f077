# -*- coding: utf-8 -*-

import compileall
import os
import shutil
import sys

def compile_topyc(path = 'src'):
    '''
    整体打包编译为pyc文件

    Parameters
    ----------
    path : str - 代码路径

    Returns
    -------
    '''
    compileall.compile_dir(path)
    for root, dirs, files in os.walk(path):
        dirpath = root.split('__pycache__')[0] if root.__contains__('__pycache__') else root
        dirpath = os.path.join('compile', dirpath)
        os.makedirs(dirpath, exist_ok = True)
        for file in files:
            if file.__contains__('.pyc'):
                name = file.split('.cpython-')[0] + '.pyc'
                os.rename(os.path.join(root, file) , os.path.join(root, name))
                shutil.move(os.path.join(root, name), os.path.join(dirpath, name))
    
    print('COMPILE FINISHED.')
    return

if __name__ == '__main__':
    if len(sys.argv) == 1:
        compile_topyc()
    else:
        compile_topyc(sys.argv[1])
