# -*- coding: utf-8 -*-
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))

from src.basic import *
from src.data import *
from wework.wework_msg import *

import argparse
from datetime import date, datetime

def db_data_rollback(backtodate, conn_to, ls_table):
    """
    数据表回滚

    Parameters
    ----------
    backtodate : str - 'yyyymmdd' 数据表回滚截至时点
    conn_to : dbconnector - 目标数据库链接
    ls_table : List - 被回滚的表格列表

    Returns
    -------
    """
    raw_conn = conn_to.raw_connection()
    cursor = raw_conn.cursor()

    for table in ls_table:
        try:
            sql = '''
            ALTER TABLE {} DELETE WHERE TRADEDATE > '{}'
            '''.format(table, backtodate)
            cursor.execute(sql)
            print('[{}]: Rollback to {}.'.format(table, backtodate))
        except:
            print('[{}]: Table rollback failed.'.format(table))
            continue
    raw_conn.commit()
    return

if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('-day', type = int, default = 2, help = 'Rollback to N days before today.')
    args = parser.parse_args()

    backtodate = (date.today() - pd.Timedelta('{}D'.format(args.day))).strftime('%Y%m%d')
    conn_vulpes = db_vulpes(writer = True)

    ls_table = ['INDEX_DAILYMARKET', 
                'STOCK_DAILYMARKET',
                'STOCK_DAILYINDICATOR',
                'CFUTURE_DAILYMARKET',
                'FFUTURE_DAILYMARKET',
                'FOPTION_ETFMARKET',
                'FOPTION_DAILYMARKET',
                'FOPTION_DAILYGREEKS',
                'FOPTION_DAILYSKEW',
                'FOPTION_VOLATILITYCONE',
                'CBOND_DAILYMARKET',
                'STOCK_DAILYRETURN',
                'CFUTURE_MAINCODE',
                'FACTOR_STGHOLD',
                'FACTOR_STGRETURN',
                'STOCK_DAILYWINRATIO']

    try:
        db_data_rollback(backtodate, conn_vulpes, ls_table)
        print('[DATA]: Rollback Finished.')
        # sendmsg_text(content = '【通知】数据回滚成功\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
    except:
        sendmsg_text(content = '【警告】数据回滚失败\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
        raise SystemError('[DATA]: Rollback Failed.')