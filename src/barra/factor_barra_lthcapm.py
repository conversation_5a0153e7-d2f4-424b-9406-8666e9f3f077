# -*- coding: utf-8 -*-

from basic import get_tradedate
import numpy as np
import pandas as pd

def factor_barra_lthcapm(df_dret, df_mktret, ls_tdate):
    """
    计算LTHALPHA因子

    The non-lagged Long-term Historical Alpha is first computed as the intercept term 
    from a CAPM regression similar to the one used to compute the HBETA descriptor, 
    except with a 1040-day window and a 260-day half-life. The final LTHALPHA descriptor 
    is then computed by first taking the equal-weighted average of the non-lagged values 
    over an 11-day window lagged by 273 days, and then multiplying it by -1 to reverse the sign.

    Parameters
    ----------
        df_dret : DataFrame - 日频股票收益数据
        df_mktret : DataFrame - 全市场收益率
        ls_tdate : List [str] - 交易日序列

    Returns
    -------

    """
    # 统一时间窗口
    wl = 1040
    hl = 260
    ls_twindow = get_tradedate(enddate= ls_tdate[-1], n = len(ls_tdate) + wl + 11 + 273 + 4)
    df_ret = df_dret.pivot(index = 'TRADEDATE', columns = 'WINDCODE', values = 'RETURN').loc[ls_twindow].fillna(0).copy()
    df_mret = df_mktret.loc[ls_twindow][['RETURN']].fillna(0).copy()

    # MSCI: 计算4日累计收益，减少序列相关性和异方差性
    # The returns are aggregated over four-day windows to reduce the effect of non-synchronicity and auto-correlation.
    df_cumret = np.exp(np.log(df_ret + 1).rolling(4, min_periods = 1).sum().dropna(how = 'all')) - 1
    df_mcumret = np.exp(np.log(df_mret + 1).rolling(4, min_periods = 1).sum().dropna(how = 'all')) - 1
    ls_tindex = df_cumret.index.tolist()

    a = 0.5 ** (1 / hl)
    eweight = [(1 - a) * (a ** i) for i in np.arange(wl, 0, -1) - 1]
    # eweight[0] = eweight[0] / (1 - a)
    eweight /= sum(eweight)
    W = np.diag(eweight)

    # 矩阵法计算加权回归
    for i in range(0, df_mcumret.shape[0] - wl + 1):
        Y = df_cumret.loc[ls_tindex[i: i + wl]].values
        X = np.hstack([np.ones([wl, 1]), df_mcumret.loc[ls_tindex[i: i + wl]].values])
        RX = np.linalg.inv(np.dot(X.T, W).dot(X))

        wls_reg = RX.dot(np.dot(X.T, W).dot(Y))
        resid = (Y - X.dot(wls_reg))
        resid2 = (resid - resid.mean(axis = 0)) ** 2

        # 非加权se
        # if i != 0:
        #     wls_alpha = np.vstack((wls_alpha, wls_reg[0]))
        #     wls_beta = np.vstack((wls_beta, wls_reg[1]))
        #     wls_se = np.vstack((wls_se, (Y - X.dot(wls_reg)).std(axis = 0)))
        # else:
        #     wls_alpha = wls_reg[0]
        #     wls_beta = wls_reg[1]
        #     wls_se = (Y - X.dot(wls_reg)).std(axis = 0)
        
        # 加权se
        if i != 0:
            wls_alpha = np.vstack((wls_alpha, wls_reg[0]))
            wls_beta = np.vstack((wls_beta, wls_reg[1]))
            wls_se = np.vstack((wls_se, np.dot(eweight, resid2)))
        else:
            wls_alpha = wls_reg[0]
            wls_beta = wls_reg[1]
            wls_se = np.dot(eweight, resid2)

    df_ltalpha_raw = pd.DataFrame(wls_alpha, columns = df_cumret.columns, index = ls_tindex[wl - 1: ])
    df_ltalpha_raw.index.rename('TRADEDATE', inplace = True)

    df_output = df_ltalpha_raw.loc[ls_tdate].stack().reset_index().copy()
    df_output.columns = ['TRADEDATE', 'STOCK_CODE', 'VALUE']
    df_output['FACTOR'] = 'LTALPHA'

    return df_output

if __name__ == '__main__':
    pass