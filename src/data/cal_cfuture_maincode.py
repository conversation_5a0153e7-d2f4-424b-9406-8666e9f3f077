# -*- coding: utf-8 -*-
# 计算主力合约代码

import pandas as pd

def cal_cfuture_maincode(df_cfutdata, df_initmain = None):
    '''
    计算主力合约代码

    <<期货主力/次主力合约判定规则>>
    主力合约判定后, 再判定次主力合约, 先剔除当前的主力合约, 剔除后的次主力合约判定规则与主力合约规则一致:
    1. 每个品种只对应一个主力合约
    2. 月合约在最后交易日当日不能为主力合约, 剔除后剩下的月合约按照下述规则判定
    3. 选出当日成交量和持仓量同为最大的月合约作为新的主力合约, 于下一个交易日生效, 若未满足此条件, 则维持原来的主力合约不变
    4. 若当前主力合约指向月合约的成交量和持仓量都不是最大, 则指向合约必须重新判定, 新主力合约指向成交量最大的合约, 若N个合约成交量同为最大, 则选择持仓量最大的合约, 若持仓量也相同则选择近月合约

    Parameters
    ----------
    df_cfutdata : DataFrame - 商品期货基础数据，至少需要含有[FUT_CODE, TRADEDATE, STDCODE, VOL, OI, DLISTDATE]
    df_initmain : DataFrame - 初始主力合约数据，需要格式[FUT_CODE, TRADEDATE, STDCODE]

    Returns
    -------
    DataFrame
    '''
    df_0 = df_cfutdata[df_cfutdata['TRADEDATE'] != df_cfutdata['DLISTDATE']].copy()
    df_rep_vol = df_0.pivot(index = 'TRADEDATE', columns = 'FUT_CODE', values = 'VOL').shift(1).stack().to_frame(name = 'PRE_VOL').reset_index()
    df_pre_oi = df_0.pivot(index = 'TRADEDATE', columns = 'FUT_CODE', values = 'OI').shift(1).stack().to_frame(name = 'PRE_OI').reset_index()
    df_0 = df_0.merge(df_rep_vol, on = ['FUT_CODE', 'TRADEDATE'], how = 'left').merge(df_pre_oi, on = ['FUT_CODE', 'TRADEDATE'], how = 'left')

    df_vorank = df_0.groupby(['TRADEDATE', 'STDCODE'])[['VOL', 'OI']].rank(method = 'min', ascending = False).rename(columns = {'VOL': 'VOL_RANK', 'OI': 'OI_RANK'})
    df_pvorank = df_0.groupby(['TRADEDATE', 'STDCODE'])[['PRE_VOL', 'PRE_OI']].rank(method = 'min', ascending = False).rename(columns = {'PRE_VOL': 'PVOL_RANK', 'PRE_OI': 'POI_RANK'})
    df_dmrank = df_0.groupby(['TRADEDATE', 'STDCODE'])[['DLISTDATE']].rank(method = 'min', ascending = True).rename(columns = {'DLISTDATE': 'DL_RANK'})
    df_merge = pd.concat([df_0, df_vorank, df_pvorank, df_dmrank], axis = 1)
    df_merge['T_RANK'] = df_merge['VOL_RANK'] * 10000 + df_merge['OI_RANK'] * 100 + df_merge['DL_RANK']
    df_merge['PT_RANK'] = df_merge['PVOL_RANK'] * 10000 + df_merge['POI_RANK'] * 100 + df_merge['DL_RANK']
    df_merge['HARDMAIN'] = df_merge['PVOL_RANK'] + df_merge['POI_RANK']

    # 潜在主力合约
    df_candidates = df_merge.query('PVOL_RANK == 1 or POI_RANK == 1')[['FUT_CODE', 'TRADEDATE', 'STDCODE', 'PT_RANK']]
    # 初始主力合约
    if df_initmain is None:
        df_initmark = df_0.groupby('STDCODE')[['TRADEDATE']].min().reset_index().merge(df_merge.groupby(['TRADEDATE', 'STDCODE'])[['T_RANK']].min().reset_index(), on = ['TRADEDATE', 'STDCODE'], how = 'left')
        df_initmain = df_merge.merge(df_initmark, on = ['T_RANK', 'TRADEDATE', 'STDCODE'], how = 'inner')[['FUT_CODE', 'TRADEDATE', 'STDCODE']]

    # 唯一主力合约
    df_mainmark = df_merge.query('HARDMAIN == 2').groupby(['TRADEDATE', 'STDCODE']).count().query('FUT_CODE == 1')[['HARDMAIN']].reset_index().rename({'HARDMAIN': 'MARK'}, axis = 1)
    df_hardmain = df_merge.query('HARDMAIN == 2').merge(df_mainmark, on = ['STDCODE', 'TRADEDATE'], how = 'inner')[['FUT_CODE', 'TRADEDATE', 'STDCODE']]

    df_frange = df_0.groupby('STDCODE')[['TRADEDATE']].min().rename({'TRADEDATE': 'START_DATE'}, axis = 1).join(df_0.groupby('STDCODE')[['TRADEDATE']].max()).rename({'TRADEDATE': 'END_DATE'}, axis = 1).reset_index()

    # 主力合约矩阵
    df_fmain = pd.concat([df_initmain, df_hardmain]).pivot(index = 'TRADEDATE', columns = 'STDCODE', values = 'FUT_CODE')

    # 新合约补充
    ls_newcode = [i for i in df_frange['STDCODE'].unique() if i not in df_fmain.columns]
    for code in ls_newcode:
        new_startdate = df_frange[df_frange['STDCODE'] == code]['START_DATE'].tolist()[0]
        new_initmain = df_merge[(df_merge['STDCODE'] == code) & (df_merge['TRADEDATE'] == new_startdate)].sort_values('T_RANK')['FUT_CODE'].tolist()[0]
        df_fmain[code] = None
        df_fmain.loc[new_startdate, code] = new_initmain

    # 填补空白部分
    for i in range(df_frange.shape[0]):
        fut = df_frange.iloc[i]['STDCODE']
        start_date = df_frange.iloc[i]['START_DATE']
        end_date = df_frange.iloc[i]['END_DATE']

        ls_nadate = [i for i in df_fmain[df_fmain[fut].isna()].index.tolist() if i > start_date and i <= end_date]
        ls_lastnadate = [i for i in df_fmain[df_fmain[fut].shift(-1).isna()].index.tolist() if i >= start_date and i <= end_date]

        for j in range(len(ls_nadate)):
            lastmain = df_fmain.loc[ls_lastnadate[j], fut]
            
            if df_candidates.query('TRADEDATE == "{}" and FUT_CODE == "{}"'.format(ls_nadate[j], lastmain)).shape[0] == 1:
                thismain = lastmain
            else:
                try:
                    thismain = df_candidates.query('TRADEDATE == "{}" and STDCODE == "{}"'.format(ls_nadate[j], fut)).sort_values('PT_RANK')['FUT_CODE'].to_list()[0]
                except:
                    thismain = lastmain

            df_fmain.loc[ls_nadate[j], fut] = thismain

    df_maincode = df_fmain.stack().to_frame('FUT_CODE').reset_index()

    return df_maincode

if __name__ == '__main__':
    pass