# -*- coding: utf-8 -*-

from basic import get_tradedate
import numpy as np
import pandas as pd

def factor_barra_ltrstr(df_dret, df_mktret, ls_tdate):
    """
    计算LTRSTR因子

    The non-lagged Long-term Relative Strength is first computed as the exponentially-weighted 
    sum of the log excess returns of the stock relative to the market over a trailing 1040-day 
    window, with a 260-day half-life. The final LTRSTR descriptor is then computed by first 
    taking the equal-weighted average of the non-lagged values over an 11-day window lagged by 
    273 days, and then multiplying it by -1 to reverse the sign.

    Parameters
    ----------
        df_dret : DataFrame - 日频股票收益数据
        df_mktret : DataFrame - 全市场收益率
        ls_tdate : List [str] - 交易日序列

    Returns
    -------

    """
    ls_twindow = get_tradedate(enddate = ls_tdate[-1], n = len(ls_tdate) + 1040 + 11 + 273)
    df_ret = df_dret.pivot(index = 'TRADEDATE', columns = 'WINDCODE', values = 'RETURN').loc[ls_twindow].copy()
    df_mret = df_mktret.loc[ls_twindow][['RETURN']].copy()

    df_RS = np.log(df_ret + 1).subtract(np.log(df_mret + 1).values, axis = 1).copy()
   
    # 计算指数加权权重序列
    wl = 1040
    hl = 260
    a = 0.5 ** (1 / hl)
    eweight = [(1 - a) * (a ** i) for i in np.arange(wl, 0, -1) - 1]
    # eweight[0] = eweight[0] / (1 - a)
    eweight /= sum(eweight)

    # 利用对角阵生成加权乘数矩阵，矩阵算法较快但因为fillna为0，无法排除NaN数据
    x = 0
    for i in range(wl):
        x += np.eye(df_RS.shape[0] - wl + 1, df_RS.shape[0], k = i) * eweight[i]
    x = np.vstack((np.zeros([wl - 1, df_RS.shape[0]]), x))
    df_LTRSTR = pd.DataFrame(np.dot(x, df_RS.fillna(0)))
    df_LTRSTR.columns = df_RS.columns
    df_LTRSTR.index = df_RS.index
    
    # MSCI: equal-weighted average of the RS over an 11-day window lagged by 273 days, and then multiplying it by -1 to reverse the sign
    df_LTRSTR = -df_LTRSTR.rolling(11, min_periods = 1).mean().shift(273).loc[ls_tdate].copy()

    df_output = df_LTRSTR.stack().reset_index().copy()
    df_output.columns = ['TRADEDATE', 'WINDCODE', 'VALUE']
    df_output['FACTOR'] = 'LTRSTR'

    return df_output

if __name__ == '__main__':
    pass