# Superset使用笔记

1. Superset使用Docker搭建可通过Dockerfile自行make的方式进行初始化设置，建议更新pip源以及通过superset_config.py传入设置。

```bash
# Dockerfile

FROM apache/superset:3.1.1

RUN pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple
RUN pip install clickhouse-connect
RUN pip install mysqlclient

# add configuration
COPY superset_config.py /app/pythonpath/superset_config.py
ENV SUPERSET_CONFIG_PATH /app/pythonpath/superset_config.py
```

2. 可以在superset_config.py中设定语言、元数据库等信息，注意如果使用同Docker下的mysql作为源数据库时，Linux与Windows存在端口映射上的差异。Windows系统下Docker Desktop可以通过host.docker.internal路由至宿主服务器IP，从而通过数据库的映射端口访问容器数据库。而Linux无此功能建议直接通过内部路由IP访问，默认Linux系统下Docker的容器间路由IP为172.17.0.*，其中**********为宿主服务器IP。需要注意的是似乎无法直接通过其他容器映射至宿主的端口访问容器服务，建议直接路由至对应容器。

```python
# superset_config.py

SECRET_KEY = 'YOUR_SECURITY_KEY'

# For Windows Docker Desktop user
SQLALCHEMY_DATABASE_URI = "mysql://username:<EMAIL>:port/superset?charset=utf8"

# For Linux Docker user
SQLALCHEMY_DATABASE_URI = "mysql://username:password@172.17.0.#:port/superset?charset=utf8"

BABEL_DEFAULT_LOCALE = "zh"

PREFERRED_DATABASES: list[str] = [
    "PostgreSQL",
    "Presto",
    "MySQL",
    "SQLite",
    "ClickHouse"
    # etc.
]

CONTENT_SECURITY_POLICY_WARNING = False
```

3. 为了能够在superset中更好的适配中文，需要在构建mysql的时候设定utf8编码格式，而使用外部mysql数据库存储元数据也有利于superset迁移与管理，使用Docker容器生成mysql是较为便利的方法。

```bash
# Setup mysql through Docker

# Step 0: Switch docker image source
# See detials: https://cr.console.aliyun.com/

# Step 1: Pull mysql image
$ docker pull mysql:5.7

# Step 2: Run image in container
$ docker run -d --name mysql -p 3306:3306 -e MYSQL_ROOT_PASSWORD=yourpassword mysql

# Step 3: Create superset database and set character to utf8
$ docker exec -it mysql bash
$ mysql -uroot -p
mysql: CREATE DATABASE superset DEFAULT CHARACTER SET utf8 COLLATE utf8_general_ci;
```

4. 启动superset服务后，如果需要更新app版本或者进行迁移，只需要在保持SQLALCHEMY_DATABASE_URI设置能够对接到之前的元数据库并在启动时update数据库即可。

```bash
# Update db
$ docker exec -it superset superset db upgrade

# Init app
$ docker exec -it superset superset init
```

5. docker系统重启后可以直接start启动。

```bash
# Check all containers to get CONTAINER ID
$ docker ps -a

# Start mysql container
$ docker start /CONTAINER ID mysql/

# Start superset container
$ docker start /CONTAINER ID superset/
```
