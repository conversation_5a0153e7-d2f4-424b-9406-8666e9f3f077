# -*- coding: utf-8 -*-
# 提取金融期货主力合约

import pandas as pd
from basic import db_vulpes

def get_ffutmain(code: list = [], n_shift: int = 2):
    '''
    提取金融期货主力合约

    <<可交易金融期货判定规则>>
    以近月合约为基础, 提前n天切换至次月合约

    Parameters
    ----------
    code : list - 合约代码列表, 默认为空提取所有合约
    n_shift : int - 提前切换天数, 默认为2

    Returns
    -------
    DataFrame
    '''
    conn = db_vulpes()
    ls_code = pd.read_sql('SELECT DISTINCT STDCODE FROM FFUTURE_INFO', con = conn)['STDCODE'].tolist()
    if len(code) == 0:
        set_code = tuple(ls_code)
    elif type(code) is str:
        set_code = (code, )
    else:
        set_code = tuple(code)
    
    sql = '''
        SELECT
        FUT_CODE,
        TRADEDATE,
        <PERSON><PERSON>STDC<PERSON>E AS STDCODE,
        DLISTDATE,
        MTYPE,
        UNIT
    FROM 
        FFUTURE_DAILYMARKET A
    LEFT JOIN
        FFUTURE_INFO B
    ON A.STDCODE = B.STDCODE
    LEFT JOIN
        BASIC_TRADEDATE C
    ON A.TRADEDATE = C.CAL_DATE
    LEFT JOIN
        BASIC_TRADEDATE D
    ON A.DLISTDATE = D.CAL_DATE
    WHERE 
        MTYPE < 3 AND 
        STDCODE IN {} AND
        D.TDNUM - C.TDNUM >= {}
    '''.format(set_code, n_shift)

    df_ffutmain = pd.read_sql(sql, con = conn)
    df_ffutmain = df_ffutmain.sort_values('MTYPE').groupby(['TRADEDATE', 'STDCODE']).first().reset_index().sort_values(['TRADEDATE', 'STDCODE']).reset_index(drop = True)
    df_ffutmain.drop(columns = ['DLISTDATE', 'MTYPE'], inplace = True)
    
    return df_ffutmain

if __name__ == '__main__':
    pass