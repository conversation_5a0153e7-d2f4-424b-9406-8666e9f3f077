# -*- coding: utf-8 -*-
# 计算期权希腊字母

import pandas as pd
from options.cal_greeks import *

def cal_option_dailygreeks(df_optdata):
    '''
    计算期权希腊字母

    Parameters
    ----------
    df_optdata : DataFrame - 期权基本数据

    Returns
    -------
    DataFrame
    '''

    df_optdata = df_optdata.sort_values('OI', ascending = False).groupby(['TARGET_CODE', 'TRADEDATE', 'STRIKE_PRICE', 'TMONTH', 'FLAG']).head(1)
    df_optdata.drop(columns = ['OI'], inplace = True)

    # 计算合成期货价格
    df_pcp = df_optdata.pivot(index = ['TARGET_CODE', 'TRADEDATE', 'STRIKE_PRICE', 'TMONTH'], columns = 'FLAG', values = 'OPTION_PRICE').reset_index()
    df_pcp = df_pcp.merge(df_optdata[['TARGET_CODE', 'TRADEDATE', 'TMONTH', 'OP_TIME', 'TARGET_PRICE']].drop_duplicates(), on = ['TARGET_CODE', 'TRADEDATE', 'TMONTH'], how = 'left')
    df_pcp['HFUT_PRICE'] = put_call_parity_sfuture(df_pcp['CALL'], df_pcp['PUT'], df_pcp['STRIKE_PRICE'], df_pcp['OP_TIME'], 0.02)
    df_optdata = df_optdata.merge(df_pcp[['TARGET_CODE', 'TRADEDATE', 'STRIKE_PRICE', 'TMONTH', 'HFUT_PRICE']], on = ['TARGET_CODE', 'TRADEDATE', 'STRIKE_PRICE', 'TMONTH'], how = 'left')
    # 计算平均合成期货价格
    df_bot = df_pcp[['TARGET_CODE', 'TRADEDATE', 'STRIKE_PRICE', 'TARGET_PRICE']].drop_duplicates().query('STRIKE_PRICE <= TARGET_PRICE').groupby(['TARGET_CODE', 'TRADEDATE']).max()[['STRIKE_PRICE']].reset_index()
    df_top = df_pcp[['TARGET_CODE', 'TRADEDATE', 'STRIKE_PRICE', 'TARGET_PRICE']].drop_duplicates().query('STRIKE_PRICE > TARGET_PRICE').groupby(['TARGET_CODE', 'TRADEDATE']).min()[['STRIKE_PRICE']].reset_index()
    df_botop = pd.concat([df_bot, df_top])
    df_botop['NEAR'] = 1
    df_mhfut = df_pcp.merge(df_botop, on = ['TARGET_CODE', 'TRADEDATE', 'STRIKE_PRICE'], how = 'inner').groupby(['TARGET_CODE', 'TRADEDATE', 'TMONTH']).mean()[['HFUT_PRICE']].reset_index().rename({'HFUT_PRICE': 'MHFUT_PRICE'}, axis = 1)

    df_optgreeks = df_optdata.merge(df_mhfut, on = ['TARGET_CODE', 'TRADEDATE', 'TMONTH'], how = 'left')
    # 计算希腊字母
    ls_iv = []
    ls_delta = []
    ls_gamma = []
    ls_vega = []
    ls_vanna = []
    for i in range(df_optgreeks.shape[0]):
        option_price = df_optgreeks.iloc[i]['OPTION_PRICE']
        stock_price = df_optgreeks.iloc[i]['MHFUT_PRICE']
        strike_price = df_optgreeks.iloc[i]['STRIKE_PRICE']
        op_time = df_optgreeks.iloc[i]['OP_TIME']
        rf = 0.02
        divid = 0
        flag = df_optgreeks.iloc[i]['FLAG']

        iv = black_scholes_implied_volatility(option_price, stock_price, strike_price, op_time, rf, divid, flag)
        ls_iv.append(iv)
        ls_delta.append(black_scholes_delta(stock_price, strike_price, op_time, rf, divid, iv, flag))
        ls_gamma.append(black_scholes_gamma(stock_price, strike_price, op_time, rf, divid, iv))
        ls_vega.append(black_scholes_vega(stock_price, strike_price, op_time, rf, divid, iv))
        ls_vanna.append(black_scholes_vanna(stock_price, strike_price, op_time, rf, divid, iv))

    df_optgreeks['IV'] = ls_iv
    df_optgreeks['DELTA'] = ls_delta
    df_optgreeks['GAMMA'] = ls_gamma
    df_optgreeks['VEGA'] = ls_vega
    df_optgreeks['VANNA'] = ls_vanna
    df_optgreeks.loc[df_optgreeks['OPTION_PRICE'].isna(), 'IV'] = None
    df_optgreeks.loc[df_optgreeks['MHFUT_PRICE'].isna(), 'IV'] = None

    return df_optgreeks