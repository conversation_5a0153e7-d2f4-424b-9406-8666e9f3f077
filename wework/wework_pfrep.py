#!/usr/bin/env python
# -*- encoding:utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))

from src.portfolio import Portfolio
from src.basic import get_tradedate
import pandas as pd

from wework.wework_msg import *

def send_pfreport(userid: str = 'Vulpes', partyid: str = '', todate: str = '', current: bool = False, savepng: bool = False) -> str:
    '''
    刷新台账生成报告并发送
    '''

    if todate == '':
        todate = pd.Timestamp.today().strftime('%Y%m%d')

    try:
        pf = Portfolio()
        pf.download_rawdata()
        pf.get_rawnav()
        file_path = pf.cal_holdstatus(todate = todate, current = current, savepng = savepng)
        file_name = file_path.split('/')[-1]
    except:
        sendmsg_text(content = '【警告】报告生成失败', userid = userid, partyid = partyid)
        raise SystemError('【警告】报告生成失败')

    try:
        media_id = get_media_id(file_path, file_name)
        sendmsg_file(media_id = media_id, userid = userid, partyid = partyid)

        if savepng == True:
            png_path = file_path.split('台账汇总')[0] + 'report.png'
            media_id = get_media_id(png_path, 'report.png', file_type = 'image')
            sendmsg_file(media_id = media_id, userid = userid, partyid = partyid, file_type = 'image')

        if get_tradedate(enddate = todate)[-2] == get_tradedate(enddate = todate, frq = 'W')[-1]:
            png_path = file_path.split('台账汇总')[0] + 'weekreport.png'
            media_id = get_media_id(png_path, 'weekreport.png', file_type = 'image')
            sendmsg_file(media_id = media_id, userid = userid, partyid = partyid, file_type = 'image')
    except:
        sendmsg_text(content = '【警告】报告推送失败', userid = userid, partyid = partyid)
        raise SystemError('【警告】报告推送失败')

    return

if __name__ == '__main__':
    send_pfreport(savepng = True)