# -*- coding: utf-8 -*-
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))

from src.basic import *
from src.cbond import *
from src.cta import *
from wework.wework_msg import *

import pandas as pd
import argparse
from datetime import date, datetime

def db_factor_update(uptodate, conn_to):
    """
    因子数据更新

    Parameters
    ----------
    uptodate : str - 'yyyymmdd' 数据库更新截至时点
    conn_to : dbconnector - 目标数据库链接

    Returns
    -------
    """
    enddate = get_tradedate(enddate = uptodate)[-1]

    # >>>> [CBOND_DLOW]
    try:
        startdate = pd.read_sql("SELECT MAX(TRADEDATE) AS TRADEDATE FROM FACTOR_STGRETURN WHERE FACTOR_ID = 'CBOND_DLOW_H20_R5'", con = conn_to)['TRADEDATE'].values[0]
        resetdate = pd.read_sql("SELECT MAX(RESETDATE) AS RESETDATE FROM FACTOR_STGHOLD WHERE FACTOR_ID = 'CBOND_DLOW_H20_R5'", con = conn_to)['RESETDATE'].values[0]
    except:
        raise SystemError('[FACTOR_RETURN]: Table connect failed.')
    
    if not startdate:
        startdate = '20190101'
        resetdate = '20190101'
    else:
        startdate = get_tradedate(startdate = startdate)[1]

    if enddate >= startdate:
        try:
            df_cbond_mktdata = get_cbond_mktdata(startdate = get_tradedate(enddate = startdate)[-10])
            df_factor_ret, df_factor_hold = factor_cbond_dlow(df_cbond_mktdata, n_hold = 20, reset = 5, startdate = resetdate)
            df_factor_ret['FACTOR_ID'] = 'CBOND_DLOW_H20_R5'
            df_factor_hold['FACTOR_ID'] = 'CBOND_DLOW_H20_R5'
        except:
            raise SystemError('[CBOND_DLOW_H20_R5]: Calculation failed.')
        
        try:
            df_factor_ret = df_factor_ret[df_factor_ret['TRADEDATE'] >= startdate].copy()
            df_factor_hold = df_factor_hold[df_factor_hold['TRADEDATE'] >= startdate].copy()
            df_factor_ret.to_sql(name = 'FACTOR_STGRETURN', con = conn_to, index = False, if_exists = 'append', chunksize = 10000)
            df_factor_hold.to_sql(name = 'FACTOR_STGHOLD', con = conn_to, index = False, if_exists = 'append', chunksize = 10000)
        except:
            raise SystemError('[CBOND_DLOW_H20_R5]: Upload failed.')

        if df_factor_ret.shape[0] > 0:
            newdate = df_factor_ret['TRADEDATE'].max()
            print('[CBOND_DLOW_H20_R5]: Update to {}.'.format(newdate))
        else:
            print('[CBOND_DLOW_H20_R5]: No data received, check source.')
    else:
        print('[CBOND_DLOW_H20_R5]: No data to update.')

    # >>>> [CTA_FACTORS]
    df_cfut_mktdata = get_cfut_mktdata(startdate = '20140101')
    df_cfut_qualified = get_cfut_qualified()

    dic_holdpct = {}

    dic_holdpct['CTA_CMOM_M1_H10_R1'] = factor_cta_cmom(df_cfut_mktdata, 'CLSRET', df_cfut_qualified, n_momentum = 1, n_hold = 10)
    dic_holdpct['CTA_CMOM_M3_H10_R2'] = factor_cta_cmom(df_cfut_mktdata, 'CLSRET', df_cfut_qualified, n_momentum = 3, n_hold = 10)
    dic_holdpct['CTA_CMOM_M5_H10_R5'] = factor_cta_cmom(df_cfut_mktdata, 'CLSRET', df_cfut_qualified, n_momentum = 5, n_hold = 10)
    dic_holdpct['CTA_CMOM_M10_H10_R5'] = factor_cta_cmom(df_cfut_mktdata, 'CLSRET', df_cfut_qualified, n_momentum = 10, n_hold = 10)
    dic_holdpct['CTA_CMOM_M20_H10_R10'] = factor_cta_cmom(df_cfut_mktdata, 'CLSRET', df_cfut_qualified, n_momentum = 20, n_hold = 10)

    dic_holdpct['CTA_TMOM_M1_T1_R1'] = factor_cta_tmom(df_cfut_mktdata, 'CLSRET', df_cfut_qualified, n_momentum = 1, trg_value = 0.01)
    dic_holdpct['CTA_TMOM_M3_T2_R2'] = factor_cta_tmom(df_cfut_mktdata, 'CLSRET', df_cfut_qualified, n_momentum = 3, trg_value = 0.02)
    dic_holdpct['CTA_TMOM_M5_T3_R5'] = factor_cta_tmom(df_cfut_mktdata, 'CLSRET', df_cfut_qualified, n_momentum = 5, trg_value = 0.03)
    dic_holdpct['CTA_TMOM_M10_T5_R5'] = factor_cta_tmom(df_cfut_mktdata, 'CLSRET', df_cfut_qualified, n_momentum = 3, trg_value = 0.05)
    dic_holdpct['CTA_TMOM_M20_T7_R10'] = factor_cta_tmom(df_cfut_mktdata, 'CLSRET', df_cfut_qualified, n_momentum = 3, trg_value = 0.07)

    dic_holdpct['CTA_TDMOM_M3_D2_R2'] = factor_cta_tdmom(df_cfut_mktdata, 'CLSRET', df_cfut_qualified, n_momentum = 3, n_dummy = 2)
    dic_holdpct['CTA_TDMOM_M10_D6_R5'] = factor_cta_tdmom(df_cfut_mktdata, 'CLSRET', df_cfut_qualified, n_momentum = 10, n_dummy = 6)
    dic_holdpct['CTA_TDMOM_M20_D12_R10'] = factor_cta_tdmom(df_cfut_mktdata, 'CLSRET', df_cfut_qualified, n_momentum = 20, n_dummy = 12)

    dic_holdpct['CTA_TGMOM_M2_B3_R2'] = factor_cta_tgmom(df_cfut_mktdata, 'CLSRET', df_cfut_qualified, n_momentum = 2, n_benchmark = 3, n_shift = 2)
    dic_holdpct['CTA_TGMOM_M4_B6_R5'] = factor_cta_tgmom(df_cfut_mktdata, 'CLSRET', df_cfut_qualified, n_momentum = 4, n_benchmark = 6, n_shift = 4)
    dic_holdpct['CTA_TGMOM_M8_B12_R10'] = factor_cta_tgmom(df_cfut_mktdata, 'CLSRET', df_cfut_qualified, n_momentum = 8, n_benchmark = 12, n_shift = 8)

    dic_holdpct['CTA_CRYD_N10_R5'] = factor_cta_cryd(df_cfut_mktdata, df_cfut_qualified, n_hold = 10)
    dic_holdpct['CTA_TRYD_T25_R5'] = factor_cta_tryd(df_cfut_mktdata, df_cfut_qualified, trg_value = 0.25)

    dic_holdpct['CTA_CVOL_V5_H10_R5'] = factor_cta_cvol(df_cfut_mktdata, 'CLSRET', df_cfut_qualified, n_volatility = 5, n_hold = 10)
    dic_holdpct['CTA_CLIQ_L20_H10_R5'] = factor_cta_cliq(df_cfut_mktdata, df_cfut_qualified, n_liquidity = 20, n_hold = 10)
    dic_holdpct['CTA_COIG_O5_H10_R5'] = factor_cta_coig(df_cfut_mktdata, df_cfut_qualified, n_oigrowth = 5, n_hold = 10)

    dic_holdpct['CTA_CSKW_S120_H10_R5'] = factor_cta_cskw(df_cfut_mktdata, 'CLSRET', df_cfut_qualified, n_skewness = 120, n_hold = 10)
    dic_holdpct['CTA_TSKW_S120_T50_R5'] = factor_cta_tskw(df_cfut_mktdata, 'CLSRET', df_cfut_qualified, n_skewness = 120, trg_value = 0.5)

    for factor in dic_holdpct.keys():

        try:
            startdate = pd.read_sql("SELECT MAX(TRADEDATE) AS TRADEDATE FROM FACTOR_STGRETURN WHERE FACTOR_ID = '{}'".format(factor), con = conn_to)['TRADEDATE'].values[0]
            resetdate = pd.read_sql("SELECT MAX(RESETDATE) AS RESETDATE FROM FACTOR_STGHOLD WHERE FACTOR_ID = '{}'".format(factor), con = conn_to)['RESETDATE'].values[0]
        except:
            raise SystemError('[FACTOR_RETURN]: Table connect failed.')
        
        if not startdate:
            startdate = '20150101'
            resetdate = '20150101'
        else:
            startdate = get_tradedate(startdate = startdate)[1]

        if enddate >= startdate:
            try:
                df_factor_ret, df_factor_hold = cal_cta_factorret(df_mktdata = df_cfut_mktdata, df_holdpct = dic_holdpct[factor], reset = int(factor.split('_R')[-1]), startdate = resetdate)
                df_factor_ret['FACTOR_ID'] = factor
                df_factor_hold['FACTOR_ID'] = factor
            except:
                raise SystemError('[{}]: Calculation failed.'.format(factor))
            
            try:
                df_factor_ret = df_factor_ret[df_factor_ret['TRADEDATE'] >= startdate].copy()
                df_factor_hold = df_factor_hold[df_factor_hold['TRADEDATE'] >= startdate].copy()
                df_factor_ret.to_sql(name = 'FACTOR_STGRETURN', con = conn_to, index = False, if_exists = 'append', chunksize = 10000)
                df_factor_hold.to_sql(name = 'FACTOR_STGHOLD', con = conn_to, index = False, if_exists = 'append', chunksize = 10000)
            except:
                raise SystemError('[{}]: Upload failed.'.format(factor))

            if df_factor_ret.shape[0] > 0:
                newdate = df_factor_ret['TRADEDATE'].max()
                print('[{}]: Update to {}.'.format(factor, newdate))
            else:
                print('[{}]: No data received, check source.'.format(factor))
        else:
            print('[{}]: No data to update.'.format(factor))

    # >>>> FINISHED    
    return

if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('-day', type = int, default = 1, help = 'Update to N days before today.')
    args = parser.parse_args()
    uptodate = (date.today() - pd.Timedelta('{}D'.format(args.day))).strftime('%Y%m%d')
    
    conn_vulpes = db_vulpes(writer = True)

    try:
        db_factor_update(uptodate, conn_to = conn_vulpes)
        print('[FACTOR]: Update Finished.')
        # sendmsg_text(content = '【通知】因子数据更新成功\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
    except:
        sendmsg_text(content = '【警告】因子数据更新失败\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
        raise SystemError('[FACTOR]: Update Failed.')