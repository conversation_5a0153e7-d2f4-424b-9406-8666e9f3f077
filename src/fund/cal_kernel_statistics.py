# -*- coding: utf-8 -*-
# 基于基金净值序列计算业绩统计变量

import numpy as np

def cal_kernel_statistics(df_raw):
    '''
    基于基金净值序列计算业绩统计变量

    Parameters
    ----------
    df_raw : DataFrame - 基金净值序列, index为str日期'YYYYMMDD', columns为净值序列

    Returns
    -------
    dict
    '''
    if df_raw.shape[1] != 1:
        raise Exception('Check DataFrame, columns number must be 1.')
    
    df_nav = df_raw.sort_index()
    col = df_nav.columns[0]

    df_ret = df_nav.pct_change().dropna()
    df_cumdd = (df_nav / df_nav.cummax() - 1)

    firstdate = df_nav.index.min()
    lastdate = df_nav.index.max()
    
    
    weekret = df_ret.tail(1).values[0][0]
    monthret = (df_ret.tail(4) + 1).cumprod().values[-1][0] - 1
    qyearret = (df_ret.tail(12) + 1).cumprod().values[-1][0] - 1
    hyearret = (df_ret.tail(25) + 1).cumprod().values[-1][0] - 1
    ytdret = (df_ret[df_ret.index.str[0: 4] == lastdate[0: 4]] + 1).cumprod().values[-1][0] - 1
    yearret = (df_ret.tail(50) + 1).cumprod().values[-1][0] - 1
    twoyearret = (df_ret.tail(100) + 1).cumprod().values[-1][0] - 1
    yearstd = df_ret.tail(50).std().values[0] * np.sqrt(50)
    yearmdd = ((df_ret.tail(51) + 1).cumprod() / (df_ret.tail(51) + 1).cumprod().cummax() - 1).min().values[0]
    yearskew = df_ret.tail(50).skew().values[0]
    yearsharpe = (yearret - 0.02) / yearstd if yearstd > 0 else None
    yearcalmar = -yearret / yearmdd if yearmdd < 0 else None
    yearwinratio = (df_ret.tail(50) > 0).sum().values[0] / df_ret.tail(50).shape[0]
    yearplratio = (df_ret.tail(50)[df_ret.tail(50)[col] > 0].mean() / (-df_ret.tail(50)[df_ret.tail(50)[col] <= 0].mean())).values[0]

    annret = ((df_ret + 1).cumprod().values[-1][0]) ** (50 / df_ret.shape[0]) - 1
    cumret = df_nav[col].values[-1] - 1
    annstd = df_ret.std().values[0] *np.sqrt(50)
    maxdd = df_cumdd.min().values[0]
    histskew = df_ret.skew().values[0]
    annsharpe = (annret - 0.02) / annstd if annstd > 0 else None
    anncalmar = -annret / maxdd if maxdd < 0 else None
    cumcalmar = -cumret / maxdd if maxdd < 0 else None
    cumwinratio = (df_ret > 0).sum().values[0] / df_ret.shape[0]
    cumplratio = (df_ret[df_ret[col] > 0].mean() / (-df_ret[df_ret[col] <= 0].mean())).values[0]

    date_mddend = df_cumdd[df_cumdd[col] == maxdd].index[-1]
    date_mddstart = df_cumdd[(df_cumdd.index <= date_mddend) & (df_cumdd[col] == 0)].index[-1]
    date_mddfix = df_cumdd[(df_cumdd.index > date_mddend) & (df_cumdd[col] == 0)].index[0] if df_cumdd[(df_cumdd.index > date_mddend) & (df_cumdd[col] == 0)].shape[0] > 0 else None
    term_mdd = df_cumdd[(df_cumdd.index > date_mddstart) & (df_cumdd.index <= date_mddend)].shape[0]
    term_fix = df_cumdd[(df_cumdd.index > date_mddend) & (df_cumdd.index <= date_mddfix)].shape[0] if date_mddfix is not None else None

    dic_statistics = {
        'FIRSTDATE': firstdate,
        'LASTDATE': lastdate,
        'WEEKRET': weekret,
        'MONTHRET': monthret,
        'QUARTYRET': qyearret,
        'HALFYRET': hyearret,
        'YTDRET': ytdret,
        'YEARRET': yearret,
        'TWOYRET': twoyearret,
        'YEARSTD': yearstd,
        'YEARMDD': yearmdd,
        'YEARSKEW': yearskew,
        'YEARSHARPE': yearsharpe,
        'YEARCALMAR': yearcalmar,
        'YEARWINRATIO': yearwinratio,
        'YEARPLRATIO': yearplratio,
        
        'ANNRET': annret,
        'CUMRET': cumret,
        'ANNSTD': annstd,
        'HISTSKEW': histskew,
        'MAXDD': maxdd,
        'MAXDDSTART': date_mddstart,
        'MAXDDEND': date_mddend,
        'MAXDDFIX': date_mddfix,
        'TERM_MDD': term_mdd,
        'TERM_FIX': term_fix,
        'ANNSHARPE': annsharpe,
        'ANNCALMAR': anncalmar,
        'CUMCALMAR': cumcalmar,
        'CUMWINRATIO': cumwinratio,
        'CUMPLRATIO': cumplratio
    }
    return dic_statistics

if __name__ == '__main__':
    pass