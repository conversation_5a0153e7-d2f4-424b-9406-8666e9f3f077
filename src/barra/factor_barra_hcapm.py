# -*- coding: utf-8 -*-

from basic import get_tradedate
import numpy as np
import pandas as pd

def factor_barra_hcapm(df_dret, df_mktret, ls_tdate, wl = 504, hl = 252):
    """
    计算CAPM回归相关因子，包括LTHALPHA, HALPHA, HBETA, HSIGMA

    CNE6:
    Computed as the slope coefficient from a time-series regression of stock excess returns 
    against the cap-weighted excess returns of the estimation universe over a trailing window 
    of 504 trading days, with a 252-day half-life.

    The returns are aggregated over four-day windows to reduce the effect of non-synchronicity 
    and auto-correlation.

    CNE5:
    Computed as the slope coefficient in a time-series regression of excess stock return,
    against the cap-weighted excess return of the estimation universe. The regression coefficients 
    are estimated over the trailing 252 trading days of returns with a half-life of 63 trading days.
    
    Parameters
    ----------
        df_dret : DataFrame - 日频股票收益数据
        df_mktret : DataFrame - 全市场收益率
        wl : int - 计算周期
        hl : int - 半衰期期
        ls_tdate : List [str] - 交易日序列

    Returns
    -------

    """
    # 统一时间窗口
    ls_twindow = get_tradedate(enddate= ls_tdate[-1], n = len(ls_tdate) + wl + 4)
    df_ret = df_dret.pivot(index = 'TRADEDATE', columns = 'STOCK_CODE', values = 'RETURN').loc[ls_twindow].fillna(0).copy()
    df_mret = df_mktret.loc[ls_twindow][['RETURN']].fillna(0).copy()

    # MSCI: 计算4日累计收益，减少序列相关性和异方差性
    # The returns are aggregated over four-day windows to reduce the effect of non-synchronicity and auto-correlation.
    df_cumret = np.exp(np.log(df_ret + 1).rolling(4, min_periods = 1).sum().dropna(how = 'all')) - 1
    df_mcumret = np.exp(np.log(df_mret + 1).rolling(4, min_periods = 1).sum().dropna(how = 'all')) - 1
    ls_tindex = df_cumret.index.tolist()

    a = 0.5 ** (1 / hl)
    eweight = [(1 - a) * (a ** i) for i in np.arange(wl, 0, -1) - 1]
    # eweight[0] = eweight[0] / (1 - a)
    eweight /= sum(eweight)
    W = np.diag(eweight)

    # 矩阵法计算加权回归
    for i in range(0, df_mcumret.shape[0] - wl + 1):
        Y = df_cumret.loc[ls_tindex[i: i + wl]].values
        X = np.hstack([np.ones([wl, 1]), df_mcumret.loc[ls_tindex[i: i + wl]].values])
        RX = np.linalg.inv(np.dot(X.T, W).dot(X))

        wls_reg = RX.dot(np.dot(X.T, W).dot(Y))
        resid = (Y - X.dot(wls_reg))
        resid2 = (resid - resid.mean(axis = 0)) ** 2

        # 非加权se
        # if i != 0:
        #     wls_alpha = np.vstack((wls_alpha, wls_reg[0]))
        #     wls_beta = np.vstack((wls_beta, wls_reg[1]))
        #     wls_se = np.vstack((wls_se, (Y - X.dot(wls_reg)).std(axis = 0)))
        # else:
        #     wls_alpha = wls_reg[0]
        #     wls_beta = wls_reg[1]
        #     wls_se = (Y - X.dot(wls_reg)).std(axis = 0)
        
        # 加权se
        if i != 0:
            wls_alpha = np.vstack((wls_alpha, wls_reg[0]))
            wls_beta = np.vstack((wls_beta, wls_reg[1]))
            wls_se = np.vstack((wls_se, np.dot(eweight, resid2)))
        else:
            wls_alpha = wls_reg[0]
            wls_beta = wls_reg[1]
            wls_se = np.dot(eweight, resid2)

    df_halpha_raw = pd.DataFrame(wls_alpha, columns = df_cumret.columns, index = ls_tindex[wl - 1: ])
    df_hbeta_raw = pd.DataFrame(wls_beta, columns = df_cumret.columns, index = ls_tindex[wl - 1: ])
    df_hse_raw = pd.DataFrame(wls_se, columns = df_cumret.columns, index = ls_tindex[wl - 1: ])

    df_halpha_raw.index.rename('TRADEDATE', inplace = True)
    df_hbeta_raw.index.rename('TRADEDATE', inplace = True)
    df_hse_raw.index.rename('TRADEDATE', inplace = True)

    ls_output = []
    df_halpha = df_halpha_raw.loc[ls_tdate].stack().reset_index().copy()
    df_halpha.columns = ['TRADEDATE', 'STOCK_CODE', 'VALUE']
    df_halpha['FACTOR'] = 'HALPHA'
    ls_output.append(df_halpha)
    
    df_hbeta = df_hbeta_raw.loc[ls_tdate].stack().reset_index().copy()
    df_hbeta.columns = ['TRADEDATE', 'STOCK_CODE', 'VALUE']
    df_hbeta['FACTOR'] = 'HBETA'
    ls_output.append(df_hbeta)

    df_hse = df_hse_raw.loc[ls_tdate].stack().reset_index().copy()
    df_hse.columns = ['TRADEDATE', 'STOCK_CODE', 'VALUE']
    df_hse['FACTOR'] = 'HSE'
    ls_output.append(df_hse)

    df_output = pd.concat(ls_output, ignore_index = True)

    return df_output

if __name__ == '__main__':
    pass