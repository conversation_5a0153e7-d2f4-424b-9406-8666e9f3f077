# -*- coding: utf-8 -*-
# 计算期权相关希腊字母

import numpy as np
from scipy.stats import norm

def put_call_parity_sfuture(call_price, put_price, strike_price, op_time, rf):
    """
    基于每组同行权价格的CALL和PUT计算合成期货价格

    Parameters
    ----------
    call_price : float - 看涨期权CALL价格
    put_price : float - 看跌期权PUT价格
    strike_price : float - 行权价格
    op_time : float - 合约剩余时间(年)
    rf : float - 无风险利率

    Returns
    -------
    float
    """
    sf_k = np.exp(rf * op_time) * (call_price - put_price)
    sf_price = sf_k + strike_price

    return sf_price

def black_scholes_price(stock_price, strike_price, op_time, rf, divid, sigma, flag):
    """
    BS定价公式

    Parameters
    ----------
    stock_price : float - 标的当前价格
    strike_price : float - 行权价格
    op_time : float - 合约剩余时间(年)
    rf : float - 无风险利率
    divid : float - 分红
    sigma : float - 标的资产价格波动率
    flag : str - 期权方向, PUT或者CALL

    Returns
    -------
    float
    """
    if sigma == 0:
        d1 = np.log(stock_price / strike_price) * np.inf
    else:
        d1 = (np.log(stock_price / strike_price) + (rf - divid + 0.5 * pow(sigma, 2)) * op_time) / (sigma * np.sqrt(op_time))
    d2 = d1 - sigma * np.sqrt(op_time)

    if flag == 'CALL':
        price = np.exp(-rf * op_time) * (stock_price * norm.cdf(d1) - strike_price * norm.cdf(d2))
    elif flag == 'PUT':
        price = np.exp(-rf * op_time) * (strike_price * norm.cdf(-d2) - stock_price * norm.cdf(-d1))

    return price

def black_scholes_implied_volatility(option_price, stock_price, strike_price, op_time, rf, divid, flag):
    """
    高斯法计算隐含波动率

    Parameters
    ----------
    option_price : float - 期权当前价格
    stock_price : float - 标的当前价格
    strike_price : float - 行权价格
    op_time : float - 合约剩余时间(年)
    rf : float - 无风险利率
    divid : float - 分红
    flag : str - 期权方向, PUT或者CALL

    Returns
    -------
    float
    """
   
    low = 0
    high = 5
    target = 0.00001

    imp = (high + low) / 2
    price = black_scholes_price(stock_price, strike_price, op_time, rf, divid, imp, flag)

    if (flag == 'PUT') & (option_price <= max(strike_price - stock_price, 0)):
        imp = 0
    elif (flag == 'CALL') & (option_price <= max(stock_price - strike_price, 0)):
        imp = 0
    else:
        while high - low > target:
            if price > option_price:
                high = (high + low) / 2
                imp = (high + low) / 2
                price = black_scholes_price(stock_price, strike_price, op_time, rf, divid, imp, flag)
            else:
                low = (high + low) / 2
                imp = (high + low) / 2
                price = black_scholes_price(stock_price, strike_price, op_time, rf, divid, imp, flag)
    return imp

def black_scholes_delta(stock_price, strike_price, op_time, rf, divid, sigma, flag):
    """
    基于BS公式计算期权Delta

    Parameters
    ----------
    stock_price : float - 标的当前价格
    strike_price : float - 行权价格
    op_time : float - 合约剩余时间(年)
    rf : float - 无风险利率
    divid : float - 分红
    sigma : float - 标的资产价格波动率
    flag : str - 期权方向, PUT或者CALL

    Returns
    -------
    float
    """
    if sigma == 0:
        d1 = np.log(stock_price / strike_price) * np.inf
    else:
        d1 = (np.log(stock_price / strike_price) + (rf - divid + 0.5 * pow(sigma, 2)) * op_time) / (sigma * np.sqrt(op_time))
    
    if flag == 'CALL':
        delta = norm.cdf(d1)
    elif flag == 'PUT':
        delta = -norm.cdf(-d1)

    delta *=  np.exp(-divid * op_time)
    
    return delta

def black_scholes_vega(stock_price, strike_price, op_time, rf, divid, sigma):
    """
    基于BS公式计算期权Vega

    Parameters
    ----------
    stock_price : float - 标的当前价格
    strike_price : float - 行权价格
    op_time : float - 合约剩余时间(年)
    rf : float - 无风险利率
    divid : float - 分红
    sigma : float - 标的资产价格波动率

    Returns
    -------
    float
    """
    if sigma == 0:
        vega = 0
    else:
        d1 = (np.log(stock_price / strike_price) + (rf - divid + 0.5 * pow(sigma, 2)) * op_time) / (sigma * np.sqrt(op_time))
        vega = stock_price * np.exp(-divid * op_time) * np.sqrt(op_time) * norm.pdf(d1)

    return vega

def black_scholes_vanna(stock_price, strike_price, op_time, rf, divid, sigma):
    """
    基于BS公式计算期权Vanna

    Parameters
    ----------
    stock_price : float - 标的当前价格
    strike_price : float - 行权价格
    op_time : float - 合约剩余时间(年)
    rf : float - 无风险利率
    divid : float - 分红
    sigma : float - 标的资产价格波动率

    Returns
    -------
    float
    """
    if sigma == 0:
        vanna = np.nan
    else:
        d1 = (np.log(stock_price / strike_price) + (rf - divid + 0.5 * pow(sigma, 2)) * op_time) / (sigma * np.sqrt(op_time))
        d2 = d1 - sigma * np.sqrt(op_time)
        vanna = np.exp(-divid * op_time) * np.sqrt(op_time) * norm.pdf(d1) * d2 / sigma

    return vanna

def black_scholes_gamma(stock_price, strike_price, op_time, rf, divid, sigma):
    """
    基于BS公式计算期权Vanna

    Parameters
    ----------
    stock_price : float - 标的当前价格
    strike_price : float - 行权价格
    op_time : float - 合约剩余时间(年)
    rf : float - 无风险利率
    divid : float - 分红
    sigma : float - 标的资产价格波动率

    Returns
    -------
    float
    """
    if sigma == 0:
        gamma = np.nan
    else:
        d1 = (np.log(stock_price / strike_price) + (rf - divid + 0.5 * pow(sigma, 2)) * op_time) / (sigma * np.sqrt(op_time))
        gamma = np.exp(-divid * op_time) * norm.pdf(d1) / (stock_price * sigma * np.sqrt(op_time))

    return gamma