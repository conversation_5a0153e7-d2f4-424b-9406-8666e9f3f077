# -*- coding: utf-8 -*-

def factor_barra_dtop(df_dind, ls_tdate):
    """
    计算DTOP因子

    Computed by dividing the trailing 12-month dividend 
    per share by the price at the last month end.

    Parameters
    ----------
        df_dind : DataFrame - 日频股票数据
        ls_tdate : List [str] - 交易日序列

    Returns
    -------
    DataFrame
    
    """
    df_output = df_dind[df_dind['TRADEDATE'].isin(ls_tdate)][['TRADEDATE', 'STOCK_CODE', 'DV_TTM']].copy()
    df_output['DV_TTM'] = df_output['DV_TTM'].fillna(0)
    df_output.columns = ['TRADEDATE', 'STOCK_CODE', 'VALUE']

    df_output['FACTOR'] = 'DTOP'

    return df_output

if __name__ == '__main__':
    pass