#!/usr/bin/env python
# -*- encoding:utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))
from wework.wework_msg import *

from datetime import datetime, timedelta
import asyncio
import json
import websockets


def get_fridays():
    today = datetime.now()
    this_friday = today + timedelta((4 - today.weekday()) % 7)
    next_friday = this_friday + timedelta(weeks = 1)
    return this_friday.strftime('%y%m%d'), next_friday.strftime('%y%m%d')

def get_args():
    this_friday, next_friday = get_fridays()
    return [
        {"channel": "tickers", "instType": "FUTURES", "instId": f"{inst}-{date}"}
        for inst in ['BTC-USDT']
        for date in [this_friday, next_friday]
    ] + [{"channel": "tickers", "instType": "SPOT", "instId": "BTC-USDT"}]


dic_data = {}
async def subscribe_okx_tickers():
    n_try = 0
    uri = "wss://wspap.okx.com:8443/ws/v5/public"
    sub_args = get_args()
    while True:
        # if len(dic_data) == len(sub_args):
        #     break
        try:
            async with websockets.connect(uri) as ws:
                params = {
                    "op": "subscribe",
                    "args": sub_args
                }

                # 向WebSocket服务器发送订阅请求
                await ws.send(json.dumps(params))
                print('>>> 发起订阅...')

                while True:
                    # if len(dic_data) == len(sub_args):
                    #     break
                    try:
                        res = await asyncio.wait_for(ws.recv(), timeout = 15)
                    except (asyncio.TimeoutError, websockets.exceptions.ConnectionClosed) as e:
                        try:
                            await ws.send('ping')
                            res = await ws.recv()
                            # print(res) # pong
                            continue
                        except Exception as e:
                            print("链接超时, 尝试重连...")
                            break
                    
                    res = eval(res)
                    if 'event' in res:
                        if res['event'] =='subscribe':
                            print(f"<<< {res['arg']['instId']} 订阅成功")
                            continue
                    elif 'data' in res:
                        dic_data[res['arg']['instId']] = res['data'][0]
                        for k, v in dic_data[res['arg']['instId']].items():
                            if v.isdigit():
                                dic_data[res['arg']['instId']][k] = int(v)
                                continue
                            try:
                                dic_data[res['arg']['instId']][k] = float(v)
                            except:
                                continue
                    else:
                        print(res)
                        
        except Exception as e:
            if n_try > 5:
                print(f"<<< 订阅失败")
                break
            else:
                print(f"订阅中断, 尝试重连...{n_try}")
                n_try += 1
                continue
dic_basis = {}
def cal_basis():
    sub_args = get_args()
    ls_instID = [i['instId'] for i in sub_args if i['instType'] == 'FUTURES']
    if 'BTC-USDT' not in dic_data:
        return
    else:
        for instID in ls_instID:
            if instID not in dic_data:
                continue
            else:
                try:
                    dic_inst = {}
                    dic_inst['timepoint'] = datetime.fromtimestamp(max(dic_data['BTC-USDT']['ts'], dic_data[instID]['ts']) / 1000).strftime('[%H:%M:%S]')
                    dic_inst['nleft'] = int((datetime.strptime(instID[-6:], '%y%m%d') - datetime.now()).days) + 2
                    dic_inst['last'] = dic_data[instID]['last']
                    dic_inst['spot'] = dic_data['BTC-USDT']['last']
                    dic_inst['basis'] = (dic_data[instID]['last'] - dic_data['BTC-USDT']['last']) / dic_data['BTC-USDT']['last'] / dic_inst['nleft']
                    dic_inst['normarbi_pnl'] = dic_data[instID]['bidPx'] - dic_data['BTC-USDT']['askPx']
                    dic_inst['invearbi_pnl'] = dic_data['BTC-USDT']['bidPx'] - dic_data[instID]['askPx']
                    dic_inst['normarbi_fee'] = dic_data[instID]['bidPx'] * 0.0005 + dic_data['BTC-USDT']['askPx'] * 0.001
                    dic_inst['invearbi_fee'] = dic_data['BTC-USDT']['bidPx'] * 0.001 + dic_data[instID]['askPx'] * 0.0005
                    dic_inst['normarbi'] = dic_inst['normarbi_pnl'] / dic_data['BTC-USDT']['askPx'] / dic_inst['nleft']
                    dic_inst['invearbi'] = dic_inst['invearbi_pnl'] / dic_data[instID]['askPx'] / dic_inst['nleft']

                    dic_basis[instID] = dic_inst
                except Exception as e:
                    dic_basis[instID] = {}
                    continue
                
    
async def get_report():
    while True:
        try:
            await asyncio.sleep(3)
            cal_basis()
            ls_report = []
            if len(dic_basis) < 1:
                print('<<< 数据为空...')
                continue
            else:
                for instID in dic_basis.keys():
                    if len(dic_basis[instID]) < 1:
                        ls_report.append(f'[WARNING] {instID} NoQualifiedBasis')
                        continue
                    
                    if dic_basis[instID]['basis'] > 0:
                        arbistatus = '升水'
                    else:
                        arbistatus = '贴水'
                    
                    str_inst = dic_basis[instID]["timepoint"] + f'\n{instID} {dic_basis[instID]["nleft"]} DaysLeft\n'
                    str_inst += f'最新成交 {dic_basis[instID]["last"]:,.2f} \n最新现货 {dic_basis[instID]["spot"]:,.2f} \n基差率 {arbistatus} {abs(dic_basis[instID]["basis"]) * 360:.2%}\n\n'
                    str_inst += f'正套空间 {dic_basis[instID]["normarbi_pnl"]:,.2f} \n交易费用 {dic_basis[instID]["normarbi_fee"]:,.2f} \n基差率 {dic_basis[instID]["normarbi"] * 360:.2%}\n\n'
                    str_inst += f'反套空间 {dic_basis[instID]["invearbi_pnl"]:,.2f} \n交易费用 {dic_basis[instID]["invearbi_fee"]:,.2f} \n基差率 {dic_basis[instID]["invearbi"] * 360:.2%}'
                    
                    sendmsg_text(content = str_inst, userid = 'Vulpes')
                    # sendmsg_text(content = str_inst, userid = 'GuLi')
                    # if dic_basis[instID]["timepoint"][1:6] in ['09:00', '11:00', '13:00', '15:00']:
                    #     sendmsg_text(content = str_inst, userid = 'Vulpes')
                    ls_report.append(str_inst)

                str_report = '============\n' + '\n\n'.join(ls_report) + '\n============'
                print(str_report)
                # break
            await asyncio.sleep(30)
        except Exception as e:
            print(f'<<< 报告出错: {e}, 重新计算...')


async def main():
    task1 = asyncio.create_task(subscribe_okx_tickers())
    task2 = asyncio.create_task(get_report())
    await asyncio.gather(task1, task2)

if __name__ == '__main__':
    asyncio.run(main())