# -*- coding: utf-8 -*-
# 计算期指时序动量信号

import numpy as np

def signal_ffut_tmom(df_mktdata, value, n_momentum, trg_value):
    '''
    计算期指时序动量信号

    Parameters
    ----------
    df_mktdata : DataFrame - 日频行情数据
    value : str - 收益率测算目标
    n_momentum : int - 动量测算时间[日]
    trg_value : float - 动量触发价格

    Returns
    -------
    DataFrame
    '''
    df_momret = (df_mktdata.pivot(index = 'TRADEDATE', columns = 'STDCODE', values = value) + 1).apply(np.log).rolling(n_momentum).sum().apply(np.exp) - 1
    df_signal = (df_momret > trg_value) * 1 - 1 * (-df_momret > trg_value)

    df_filter = df_mktdata.pivot(index = 'TRADEDATE', columns = 'STDCODE', values = value) * 0 + 1
    df_signal *= df_filter
    
    return df_signal

if __name__ == '__main__':
    pass