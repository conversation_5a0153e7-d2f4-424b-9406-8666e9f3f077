# -*- coding: utf-8 -*-
# 根据持仓权重计算因子收益率和持仓明细

from basic import get_tradedate
import pandas as pd
import numpy as np

def cal_cta_factorret(df_mktdata, df_holdpct, reset, ret = 'CLSRET', startdate = '20150101'):
    '''
    根据持仓权重计算因子收益率和持仓明细

    Parameters
    ----------
    df_mktdata : DataFrame - 日频行情数据
    df_holdpct : DataFrame - 因子持仓权重
    reset : int - 策略换仓周期
    ret : str - 策略测试收益率, 默认CLSRET
    startdate : str - 策略开始日期, YYYYMMDD

    Returns
    -------
    DataFrame
    '''
    df_trademkt = df_mktdata.query('MTYPE == 1')[['TRADEDATE', 'STDCODE', 'FUT_CODE', ret]].copy()
    ls_tdate = get_tradedate(startdate = startdate, enddate = df_trademkt['TRADEDATE'].max())
    ls_sdate = [ls_tdate[i] for i in range(0, len(ls_tdate), reset)]
    df_sdate = pd.DataFrame({'TRADEDATE': ls_tdate}).merge(pd.DataFrame({'TRADEDATE': ls_sdate, 'RESETDATE': ls_sdate}), on = 'TRADEDATE', how = 'left').ffill()

    # 计算持仓绝对值权重，需要递延1天作为权重
    df_hold = pd.DataFrame(index = ls_tdate).join(df_holdpct.shift(1).loc[ls_sdate].fillna(0), how = 'left').ffill().replace(0, None).stack().to_frame().reset_index()
    df_hold.columns = ['TRADEDATE', 'STDCODE', 'WEIGHT']
    df_hold['WEIGHT'] = df_hold['WEIGHT'].abs()

    # 计算持仓多空方向
    df_holdsign = df_holdpct.shift(1).loc[ls_sdate].fillna(0).apply(np.sign).replace(0, 1)
    df_sign = pd.DataFrame(index = ls_tdate).join(df_holdsign).ffill().stack().to_frame().reset_index()
    df_sign.columns = ['TRADEDATE', 'STDCODE', 'SIGN']
    df_hold = df_hold.merge(df_sign, on = ['TRADEDATE', 'STDCODE'], how = 'left')

    # 计算持仓合约在每个持有周期内的累计收益率
    df_holdret = df_trademkt.merge(df_hold, on = ['TRADEDATE', 'STDCODE'], how = 'right').merge(df_sdate, on = 'TRADEDATE', how = 'left').fillna(0)
    df_holdret['CUMRET'] = df_holdret[ret] * df_holdret['SIGN'] + 1
    df_holdret['CUMRET'] = df_holdret.sort_values('TRADEDATE').groupby(['STDCODE', 'RESETDATE'])[['CUMRET']].cumprod()
    df_holdret['WCUMRET'] = df_holdret['WEIGHT'] * df_holdret['CUMRET']

    # 计算各持仓合约的每日组合收益贡献
    df_holdweight = df_holdret.pivot(index = 'TRADEDATE', columns = 'STDCODE', values = 'CUMRET').shift(1)
    df_holdweight.loc[df_holdweight.index.isin(ls_sdate)] = df_holdret.pivot(index = 'TRADEDATE', columns = 'STDCODE', values = 'CUMRET').loc[df_holdweight.index.isin(ls_sdate)] * 0 + 1
    df_holdret = df_holdret.merge(df_holdweight.div(df_holdweight.sum(axis = 1), axis = 0).stack().to_frame('HOLD_WEIGHT').reset_index(), on = ['TRADEDATE', 'STDCODE'])
    df_holdret['HOLD_RETURN'] = df_holdret['HOLD_WEIGHT'] * df_holdret[ret] * df_holdret['SIGN']
    df_holdret['HOLD_SIGNWEIGHT'] = df_holdret['HOLD_WEIGHT'] * df_holdret['SIGN']

    # 根据持仓权重计算组合收益率
    df_factor_ret = df_holdret.groupby('TRADEDATE')[['HOLD_RETURN']].sum().reset_index().rename({'HOLD_RETURN': 'RETURN'}, axis = 1)
    df_factor_ret = pd.DataFrame({'TRADEDATE': ls_tdate}).merge(df_factor_ret, on = 'TRADEDATE', how = 'left').fillna(0)
    df_factor_hold = df_holdret[['FUT_CODE', 'STDCODE', 'TRADEDATE', 'RESETDATE', 'HOLD_SIGNWEIGHT', 'HOLD_RETURN']].rename({'FUT_CODE': 'HOLD_CODE', 'HOLD_SIGNWEIGHT': 'HOLD_WEIGHT'}, axis = 1).copy()

    return df_factor_ret, df_factor_hold

if __name__ == '__main__':
    pass