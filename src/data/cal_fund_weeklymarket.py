# -*- coding: utf-8 -*-
# 清洗私募基金净值对齐周度序列
import pandas as pd
from basic import get_tradedate

def cal_fund_weeklymarket(df_fundnav, conn_vulpes):
    """
    清洗私募基金净值对齐周度序列

    Parameters
    ----------
    df_fundnav : DataFrame - RAWNAV数据
    conn_vulpes: vulpes数据库链接

    Returns
    -------
    DataFrame

    """
    ls_tdate_w = get_tradedate(frq = 'W')

    df_right = df_fundnav.pivot(index = 'TRADEDATE', columns = 'FUND_NAME', values = 'FUND_NAV').dropna(axis = 1, how = 'all')
    df_left = pd.DataFrame({'TRADEDATE': pd.date_range(start = df_right.index[0], end = df_right.index[-1]).strftime('%Y%m%d')})
    df_fundnav_us = df_left.merge(df_right, on = 'TRADEDATE', how = 'left').set_index('TRADEDATE')
    df_fundnav_us = df_fundnav_us.ffill() * (df_fundnav_us.bfill() * 0 + 1)
    df_fundnavw_us = df_fundnav_us[df_fundnav_us.index.isin(ls_tdate_w)].dropna(how = 'all')
    
    # 剔除发行初期连续净值为1的情况
    for i in range(10):
        df_fundnavw_us[(df_fundnavw_us == df_fundnavw_us.shift(-1)) & (df_fundnavw_us == 1) & (df_fundnavw_us.shift(1) * 0 != 0)] = None

    df_fundnavw = df_fundnavw_us.stack().to_frame().reset_index()
    df_fundretw = df_fundnavw_us.pct_change(fill_method = None).stack().to_frame().reset_index()
    df_fundnavw.columns = ['TRADEDATE', 'FUND_NAME', 'FUND_NAV']
    df_fundretw.columns = ['TRADEDATE', 'FUND_NAME', 'FUND_RETURN']
    df_fundnavw = df_fundnavw.merge(df_fundretw, on = ['TRADEDATE', 'FUND_NAME'], how = 'left')
    df_fundnavw = df_fundnavw.merge(df_fundnav.sort_values('TRADEDATE').groupby('FUND_NAME').tail(1).drop(['TRADEDATE', 'FUND_NAV'], axis = 1), on = 'FUND_NAME', how = 'left')

    # 计算基准净值序列
    df_benchmark = pd.read_sql('SELECT TRADEDATE, INDEX_CODE AS BENCHMARK_CODE, CLOSE AS BENCHMARK_NAV FROM INDEX_DAILYMARKET', conn_vulpes)
    df_fundnavw = df_fundnavw.merge(df_benchmark, on = ['TRADEDATE', 'BENCHMARK_CODE'], how = 'left')
    df_right = df_fundnavw.sort_values('TRADEDATE').groupby('FUND_NAME').head(1)[['FUND_NAME', 'BENCHMARK_NAV']].rename({'BENCHMARK_NAV': 'BENCHMARK_START'}, axis = 1)
    df_fundnavw = df_fundnavw.merge(df_right, on = 'FUND_NAME', how = 'left')
    df_fundnavw['BENCHMARK_NAV'] /= df_fundnavw['BENCHMARK_START']
    df_fundnavw['BENCHMARK_NAV'].fillna(1, inplace = True)
    df_fundnavw.drop(['BENCHMARK_START'], axis = 1, inplace = True)
    
    # 计算基准收益率
    df_benchmark_us = df_fundnavw.pivot(index = 'TRADEDATE', columns = 'FUND_NAME', values = 'BENCHMARK_NAV').pct_change(fill_method = None)
    df_benchmark_ret = df_benchmark_us.stack().to_frame().reset_index()
    df_benchmark_ret.columns = ['TRADEDATE', 'FUND_NAME', 'BENCHMARK_RETURN']
    df_fundnavw = df_fundnavw.merge(df_benchmark_ret, on = ['TRADEDATE', 'FUND_NAME'], how = 'left')

    df_fundnavw['EXCESSIVE_RETURN'] = (df_fundnavw['FUND_RETURN'] + 1) / (df_fundnavw['BENCHMARK_RETURN'] + 1) - 1
    df_fundnavw['EXCESSIVE_NAV'] = df_fundnavw['FUND_NAV'] / df_fundnavw['BENCHMARK_NAV']

    return df_fundnavw

if __name__ == '__main__':
    pass