# -*- coding: utf-8 -*-

from basic import get_tradedate
import numpy as np
import pandas as pd

def factor_barra_mtrstr(df_dret, ls_tdate):
    """
    计算CNE5的RSTR因子

    The non-lagged Relative Strength is first computed as the exponentially-weighted 
    sum of the log excess returns of the stock relative to the risk free return over 
    a trailing 504-day window, with a 126-day half-life. The final RSTR descriptor is 
    then lagged by 21 days.

    Parameters
    ----------
        df_dret : DataFrame - 日频股票收益数据
        ls_tdate : List [str] - 交易日序列

    Returns
    -------

    """
    ls_twindow = get_tradedate(enddate = ls_tdate[-1], n = len(ls_tdate) + 504 + 11 + 11)
    df_ret = df_dret.pivot(index = 'TRADEDATE', columns = 'STOCK_CODE', values = 'RETURN').loc[ls_twindow].copy()
    df_RS = np.log(df_ret.loc[ls_twindow] + 1).copy()
    
    # 计算指数加权权重序列
    wl = 504
    hl = 126
    a = 0.5 ** (1 / hl)
    eweight = [(1 - a) * (a ** i) for i in np.arange(wl, 0, -1) - 1]
    # eweight[0] = eweight[0] / (1 - a)
    eweight /= sum(eweight)
    
    # 利用对角阵生成加权乘数矩阵，矩阵算法较快但因为fillna为0，无法排除NaN数据
    x = 0
    for i in range(wl):
        x += np.eye(df_RS.shape[0] - wl + 1, df_RS.shape[0], k = i) * eweight[i]
    x = np.vstack((np.zeros([wl - 1, df_RS.shape[0]]), x))
    df_MTRSTR = pd.DataFrame(np.dot(x, df_RS.fillna(0)))
    df_MTRSTR.columns = df_RS.columns
    df_MTRSTR.index = df_RS.index
    
    # MSCI-CNE5: lagged by 21 days
    df_MTRSTR = df_MTRSTR.shift(21).loc[ls_tdate].copy()

    df_output = df_MTRSTR.stack().reset_index().copy()
    df_output.columns = ['TRADEDATE', 'STOCK_CODE', 'VALUE']
    df_output['FACTOR'] = 'MTRSTR'

    return df_output

if __name__ == '__main__':
    pass