# -*- coding: utf-8 -*-
# 数据库链接

from sqlalchemy import create_engine
import tushare as ts

def db_wind_GH():
    '''
    wind落地库-GH

    Returns
    -------
    DB_Engine - 数据库链接引擎
    '''
    db_username = 'linghhq'
    db_password = '888888'
    db_host = '*************'
    db_port = 8081
    db_schema = 'wind'
    db_charset = 'GBK'

    db_conn = create_engine('mssql+pymssql://{}:{}@{}:{}/{}?charset={}'.format(db_username, db_password, db_host, db_port, db_schema, db_charset))
    
    return db_conn

def db_vulpes(writer = False):
    '''
    vulpes数据库

    Returns
    -------
    DB_Engine - 数据库链接引擎
    '''
    db_username = 'dbreader'
    db_password = 'vulpes'
    db_host = '**************'
    db_port = 8123
    db_schema = 'vulpes'
    db_charset = 'UTF8'

    if writer:
        db_username = 'dbwriter'
        db_host = 'localhost'

    db_conn = create_engine('clickhouse://{}:{}@{}:{}/{}?charset={}'.format(db_username, db_password, db_host, db_port, db_schema, db_charset))

    return db_conn

# def db_smpp():
#     '''
#     smpp数据库

#     Returns
#     -------
#     DB_Engine - 数据库链接引擎
#     '''
#     db_username = 'jgjj'
#     db_password = 'Jgjj123321'
#     db_host = '*************'
#     db_port = 8383
#     db_schema = 'JGJJ'
#     db_charset = 'UTF8'

#     db_conn = create_engine('oracle+oracledb://{}:{}@{}:{}/{}'.format(db_username, db_password, db_host, db_port, db_schema, db_charset))

#     return db_conn

def api_ts():
    '''
    tushare 数据库接口

    Returns
    -------
    api接口
    '''
    token = 'f1a1438e25b3147de9e0be4b27b22b8b935da9efce9549f220f599a6'
    ts.set_token(token)
    pro = ts.pro_api()

    return pro


if __name__ == '__main__':
    pass