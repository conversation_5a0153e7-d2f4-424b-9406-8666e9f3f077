# -*- coding: utf-8 -*-

from basic import get_tradedate
import numpy as np
import pandas as pd

def factor_barra_dastd(df_dret, ls_tdate):
    """
    计算DASTD因子

    Computed as the volatility of daily excess returns over the past 252 trading days with a 42-day half-life.

    Parameters
    ----------
        df_dret : DataFrame - 日频股票收益率数据
        ls_tdate : List [str] - 交易日序列
    
    Returns
    -------
    DataFrame
        index : 交易日期
        columns : WIND股票代码
    """
    ls_twindow = get_tradedate(enddate = ls_tdate[-1], n = len(ls_tdate) + 252)
    df_ret = df_dret.pivot(index = 'TRADEDATE', columns = 'STOCK_CODE', values = 'RETURN').loc[ls_twindow].copy()

    wl = 252
    hl = 42
    a = 0.5 ** (1 / hl)
    eweight = [(1 - a) * (a ** i) for i in np.arange(wl, 0, -1) - 1]
    # eweight[0] = eweight[0] / (1 - a)
    eweight /= sum(eweight)

    df_square = (df_ret - df_ret.rolling(wl, min_periods = 1).mean()) ** 2

    # 利用对角阵生成加权乘数矩阵，矩阵算法较快但因为fillna为0，无法排除NaN数据
    x = 0
    for i in range(wl):
        x += np.eye(df_square.shape[0] - wl + 1, df_square.shape[0], k = i) * eweight[i]
    x = np.vstack((np.zeros([wl - 1, df_square.shape[0]]), x))
    df_dastd = pd.DataFrame(np.dot(x, df_square.fillna(0)))
    df_dastd.columns = df_square.columns
    df_dastd.index = df_square.index

    df_output = np.sqrt(df_dastd).loc[ls_tdate].stack().reset_index().copy()
    df_output.columns = ['TRADEDATE', 'STOCK_CODE', 'VALUE']
    df_output['FACTOR'] = 'DASTD'

    return df_output

if __name__ == '__main__':
    pass