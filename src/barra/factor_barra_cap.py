# -*- coding: utf-8 -*-

import numpy as np
import pandas as pd
from barra import mod_datawinsorize, mod_datastandardize

def factor_barra_cap(df_dind, df_stkqual, ls_tdate):
    """
    计算LNCAP， MIDCAP因子

    LNCAP: Computed as the natural logarithm of the market capitalization of the firm.

    MIDCAP: The Size factor exposure is first cubed, and then orthogonalized to Size
    on a regression-weighted basis, and finally winsorized and standardized.

    Parameters
    ----------
        df_dind : DataFrame - 日频股票数据
        df_stkqual : DataFrame - 股票标准化标签
        ls_tdate : List [str] - 交易日序列

    Returns
    -------
    DataFrame
    
    """
    df_MV = df_dind.pivot(index = 'TRADEDATE', columns = 'STOCK_CODE', values = 'MV')
    df_LMV = df_dind.pivot(index = 'TRADEDATE', columns = 'STOCK_CODE', values = 'LMV')
    
    df_lncap = np.log(df_MV.loc[ls_tdate])
    df_lnmv = mod_datawinsorize(df_lncap, df_stkqual, threshold = 3)
    df_lnmv = mod_datastandardize(df_lnmv, df_LMV, df_stkqual)
    df_lnmv_cube = df_lnmv ** 3
    ls_midcaps = []

    for i in ls_tdate:
        y = df_lnmv_cube.loc[i].dropna().astype(float)
        ls_code = y.index.tolist()
        Y = np.array(y.values).reshape(-1, 1)
        W = np.diag(np.sqrt(df_MV[ls_code].loc[i]) / np.sqrt(df_MV[ls_code].loc[i]).sum())
        X = np.vstack([np.ones(len(ls_code)), df_lnmv[ls_code].loc[i].astype(float).values]).T
        INV = np.linalg.inv((X.T).dot(W).dot(X))
        reg = INV.dot((X.T).dot(W).dot(Y))
        negresid = Y - X.dot(reg)
        ls_midcaps.append(pd.DataFrame({'TRADEDATE': i, 'STOCK_CODE': ls_code, 'VALUE': negresid.T[0]}))
        
    df_midcap = pd.concat(ls_midcaps)
    df_lncap = df_lncap.stack().reset_index().copy()
    df_lncap.columns = ['TRADEDATE', 'STOCK_CODE', 'VALUE']

    df_lncap['FACTOR'] = 'LNCAP'
    df_midcap['FACTOR'] = 'MIDCAP'

    df_output = pd.concat([df_lncap, df_midcap], ignore_index = True)
    
    return df_output

if __name__ == '__main__':
    pass