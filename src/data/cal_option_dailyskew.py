# -*- coding: utf-8 -*-
# 计算期权偏度数据和平值IV

import pandas as pd

def cal_option_dailyskew(df_optgreeks):
    '''
    计算期权偏度数据和平值IV

    Parameters
    ----------
    df_optgreeks : DataFrame - 期权希腊字母数据

    Returns
    -------
    DataFrame
    '''

    # 计算平值期权IV
    df_bot = df_optgreeks[df_optgreeks['STRIKE_PRICE'] <= df_optgreeks['TARGET_PRICE']].groupby(['TARGET_CODE', 'TRADEDATE', 'TMONTH', 'FLAG']).max()[['STRIKE_PRICE']].reset_index()
    df_top = df_optgreeks[df_optgreeks['STRIKE_PRICE'] > df_optgreeks['TARGET_PRICE']].groupby(['TARGET_CODE', 'TRADEDATE', 'TMONTH', 'FLAG']).min()[['STRIKE_PRICE']].reset_index()
    df_botop = pd.concat([df_bot, df_top]).merge(df_optgreeks[['TARGET_CODE','TRADEDATE', 'TMONTH', 'FLAG', 'STRIKE_PRICE', 'TARGET_PRICE', 'IV']], on = ['TARGET_CODE','TRADEDATE', 'TMONTH', 'FLAG', 'STRIKE_PRICE'], how = 'left')
    df_botop_sum = df_botop.groupby(['TARGET_CODE','TRADEDATE', 'TMONTH', 'STRIKE_PRICE']).mean(numeric_only = True).reset_index()
    df_botop_sum['DISTANCE'] = (df_botop_sum['STRIKE_PRICE'] - df_botop_sum['TARGET_PRICE']).abs()
    df_botop_sum = df_botop_sum.merge(df_botop_sum.groupby(['TARGET_CODE', 'TRADEDATE', 'TMONTH']).sum()[['DISTANCE']].rename({'DISTANCE': 'SUM_DISTANCE'}, axis = 1).reset_index(), on = ['TARGET_CODE', 'TRADEDATE', 'TMONTH'], how = 'left')
    df_botop_sum['WEIGHT'] = 1 - df_botop_sum['DISTANCE'] / df_botop_sum['SUM_DISTANCE']
    df_botop_sum['MIV'] = df_botop_sum['WEIGHT'] * df_botop_sum['IV']
    df_miv = df_botop_sum.groupby(['TARGET_CODE', 'TRADEDATE', 'TMONTH']).sum()[['MIV']].reset_index()

    # 选取距离delta为+-0.25最近的两档期权
    df_cbot = df_optgreeks[(df_optgreeks['DELTA'] <= 0.25) & (df_optgreeks['FLAG'] == 'CALL')].groupby(['TARGET_CODE', 'TRADEDATE', 'TMONTH']).max()[['DELTA']].reset_index()
    df_ctop = df_optgreeks[(df_optgreeks['DELTA'] > 0.25) & (df_optgreeks['FLAG'] == 'CALL')].groupby(['TARGET_CODE', 'TRADEDATE', 'TMONTH']).min()[['DELTA']].reset_index()
    df_pbot = df_optgreeks[(df_optgreeks['DELTA'] <= -0.25) & (df_optgreeks['FLAG'] == 'PUT')].groupby(['TARGET_CODE', 'TRADEDATE', 'TMONTH']).max()[['DELTA']].reset_index()
    df_ptop = df_optgreeks[(df_optgreeks['DELTA'] > -0.25) & (df_optgreeks['FLAG'] == 'PUT')].groupby(['TARGET_CODE', 'TRADEDATE', 'TMONTH']).min()[['DELTA']].reset_index()

    # 计算delta到+-0.25的距离
    df_cbotop = pd.concat([df_cbot, df_ctop])
    df_cbotop['DISTANCE'] = (df_cbotop['DELTA'] - 0.25).abs()
    df_pbotop = pd.concat([df_pbot, df_ptop])
    df_pbotop['DISTANCE'] = (df_pbotop['DELTA'] + 0.25).abs()

    # 数据合并补齐
    df_cpbotop = pd.concat([df_cbotop, df_pbotop]).merge(df_optgreeks[['TARGET_CODE', 'TRADEDATE', 'TMONTH', 'FLAG', 'STRIKE_PRICE', 'DELTA', 'TARGET_PRICE', 'IV']], on = ['TARGET_CODE', 'TRADEDATE', 'TMONTH', 'DELTA'], how = 'left')
    # 相同delta和flag的期权超过1个时选取行权价格与标的价格最近的期权
    df_cpbotop['CLOSEST'] = (df_cpbotop['STRIKE_PRICE'] - df_cpbotop['TARGET_PRICE']).abs()
    df_cpbotop = df_cpbotop.sort_values('CLOSEST').groupby(['TARGET_CODE', 'TRADEDATE', 'TMONTH', 'DELTA', 'FLAG']).head(1).copy()
    # 分别计算CALL与PUT的上下档合成IV
    df_cpbotop = df_cpbotop.merge(df_cpbotop.groupby(['TARGET_CODE', 'TRADEDATE', 'TMONTH', 'FLAG']).sum()[['DISTANCE']].rename({'DISTANCE': 'SUM_DISTANCE'}, axis = 1).reset_index(), on = ['TARGET_CODE', 'TRADEDATE', 'TMONTH', 'FLAG'], how = 'left')
    df_cpbotop['WEIGHT'] = 1 - df_cpbotop['DISTANCE'] / df_cpbotop['SUM_DISTANCE']
    # 附近只有一个期权的时候权重修正
    df_cpbotop['WEIGHT'].replace({0: 1}, inplace = True)
    df_cpbotop['WIV'] = df_cpbotop['IV'] * df_cpbotop['WEIGHT']

    # 数据整合计算偏度
    df_skew = df_miv.set_index(['TARGET_CODE', 'TRADEDATE', 'TMONTH']).copy()
    df_skew['CIV'] = df_cpbotop.query('FLAG == "CALL"').groupby(['TARGET_CODE', 'TRADEDATE', 'TMONTH']).sum(numeric_only = True)['WIV']
    df_skew['PIV'] = df_cpbotop.query('FLAG == "PUT"').groupby(['TARGET_CODE', 'TRADEDATE', 'TMONTH']).sum(numeric_only = True)['WIV']
    df_skew['SKEW'] = (df_skew['CIV'] - df_skew['PIV']) / df_skew['MIV']
    df_skew['CSKEW'] = (df_skew['CIV'] - df_skew['MIV']) / df_skew['MIV']
    df_skew['PSKEW'] = (df_skew['PIV'] - df_skew['MIV']) / df_skew['MIV']
    df_skew['MTYPE'] = df_skew.reset_index().groupby(['TARGET_CODE', 'TRADEDATE'])['TMONTH'].rank().values
    df_skew.reset_index(inplace = True)

    return df_skew

if __name__ == '__main__':
    pass