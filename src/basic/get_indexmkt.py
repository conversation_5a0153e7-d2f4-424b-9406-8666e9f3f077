# -*- coding: utf-8 -*-
# 提取市场指数收益率

import pandas as pd
from basic import db_vulpes

def get_indexmkt(mktcode = '000985.CSI'):
    """
    提取市场指数收益率
    
    Parameters
    ----------
    mktcode : str - ['000985.CSI'] 市场指数代码

    Returns
    -------
    DataFrame
        index : TRADEDATE 交易日期 'yyyymmdd'
        columns : [PRE_CLOSE, CLOSE, RETURN]
    """
    
    conn = db_vulpes()
    sql = '''
    SELECT 
        TRADEDATE,
        PRE_CLOSE,
        CLOSE,
        RETURN
    FROM 
        INDEX_DAILYMARKET 
    WHERE 
        INDEX_CODE = '{}'
    ORDER BY TRADEDATE;
    '''.format(mktcode)
    
    df_mktret = pd.read_sql(sql, con = conn).drop_duplicates()
    df_mktret.set_index('TRADEDATE', inplace = True)
    df_mktret = df_mktret.astype(float)

    return df_mktret

if __name__ == '__main__':
    pass