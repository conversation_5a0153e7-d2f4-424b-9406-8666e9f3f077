# -*- coding: utf-8 -*-
# 计算时序Dummy动量因子方向

def signal_ffut_tdmom(df_mktdata, value, n_momentum, n_dummy, trg_value = 0):
    '''
    计算时序Dummy动量因子方向

    Parameters
    ----------
    df_mktdata : DataFrame - 日频行情数据
    value : str - 收益率测算目标
    n_momentum : int - 动量测算时间[日]
    n_dummy : int - 判断动量的Dummy累计触发次数
    trg_value : float - 动量Dummy触发价格, 默认为0

    Returns
    -------
    DataFrame
    '''
    df_posdummy = (df_mktdata.pivot(index = 'TRADEDATE', columns = 'STDCODE', values = value) > trg_value).rolling(n_momentum).sum()
    df_nagdummy = (df_mktdata.pivot(index = 'TRADEDATE', columns = 'STDCODE', values = value) < -trg_value).rolling(n_momentum).sum()

    df_signal = (df_posdummy >= n_dummy) * 1 - 1 * (df_nagdummy >= n_dummy)

    df_filter = df_mktdata.pivot(index = 'TRADEDATE', columns = 'STDCODE', values = value) * 0 + 1
    df_signal *= df_filter
    
    return df_signal

if __name__ == '__main__':
    pass