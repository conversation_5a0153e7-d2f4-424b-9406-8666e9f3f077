# -*- coding: utf-8 -*-

from barra import mod_colunify, mod_makeupann

def factor_barra_em(df_dind, df_yreport, ls_tdate):
    """
    计算EM因子

    Computed by dividing the earnings before interest and taxes (EBIT) 
    from the last fiscal year by the current enterprise value (EV).

    Parameters
    ----------
        df_dind : DataFrame - 日频股票数据
        df_yreport : DataFrame - 年度财务指标表数据
        ls_tdate : List [str] - 交易日序列

    Returns
    -------
    DataFrame
    
    """
    # 数据提取
    df_MV = df_dind.pivot(index = 'TRADEDATE', columns = 'STOCK_CODE', values = 'MV') * 10000

    # NET_LIABILITY = TOTAL_LIABILITY - CASH
    # 非银行为MC，银行为CR
    df_yreport['NL'] = df_yreport['TL'] - (df_yreport['MC'].fillna(0) + df_yreport['CR'].fillna(0))
    df_NL = df_yreport.pivot(index = 'REPORT_DATE', columns = 'STOCK_CODE', values = 'NL')
    df_EBIT = df_yreport.pivot(index = 'REPORT_DATE', columns = 'STOCK_CODE', values = 'EBIT')
    df_ANN = df_yreport.pivot(index = 'REPORT_DATE', columns = 'STOCK_CODE', values = 'DISCLOSE_DATE')

    df_dEBIT = mod_makeupann(df_EBIT, df_ANN, ls_tdate)
    df_dNL = mod_makeupann(df_NL, df_ANN, ls_tdate)

    # 规整到有效证券范围
    df_qEBIT = mod_colunify(df_dEBIT, df_MV)
    df_qNL = mod_colunify(df_dNL, df_MV)

    # 计算因子
    # EM 
    # = EBIT / (NET_LIABILITY + MARKET_VALUE) 
    # = EBIT / (TOTAL_LIABILITY - CASH + MARKET_VALUE)

    df_EM = df_qEBIT / (df_qNL + df_MV)
    df_output = df_EM.stack().reset_index()
    df_output.columns = ['TRADEDATE', 'STOCK_CODE', 'VALUE']

    df_output['FACTOR'] = 'EM'

    return df_output

if __name__ == '__main__':
    pass