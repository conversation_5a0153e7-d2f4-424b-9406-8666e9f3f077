# -*- coding: utf-8 -*-
from barra import mod_makeupann

def factor_barra_dtoa(df_yreport, ls_tdate):
    """
    计算DTOA因子

    Computed as, DTOA = TL / TA, where TL and TA are the total liabilities
    and total assets, respectively, from the last fiscal year.
    
    Parameters
    ----------
        df_yreport : DataFrame - 年度财务指标表数据
        ls_tdate : List [str] - 交易日序列

    Returns
    -------
    DataFrame
    
    """
    df_yreport['DTOA'] = df_yreport['TL'] / df_yreport['TA']
    df_DTOA = df_yreport.pivot(index = 'REPORT_DATE', columns = 'STOCK_CODE', values = 'DTOA')
    df_ANN = df_yreport.pivot(index = 'REPORT_DATE', columns = 'STOCK_CODE', values = 'DISCLOSE_DATE')

    df_output = mod_makeupann(df_DTOA, df_ANN, ls_tdate).stack().reset_index()
    df_output.columns = ['TRADEDATE', 'STOCK_CODE', 'VALUE']

    df_output['FACTOR'] = 'DTOA'

    return df_output

if __name__ == '__main__':
    pass