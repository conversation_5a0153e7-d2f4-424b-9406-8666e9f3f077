# -*- coding: utf-8 -*-

from basic import get_tradedate
import numpy as np

def factor_barra_cmra(df_dret, ls_tdate):
    """
    计算CMRA因子

    Computed as the gap between the highest and lowest points of the 
    cumulative log excess return in the past 12 months.

    Parameters
    ----------
        df_dret : DataFrame - 日频股票收益率数据
        ls_tdate : List [str] - 交易日序列

    Returns
    -------
    DataFrame
    
    """
    # 计算月累计对数收益
    ls_twindow = get_tradedate(enddate = ls_tdate[-1], n = len(ls_tdate) + 21 * 13)
    df_RET = df_dret.pivot(index = 'TRADEDATE', columns = 'STOCK_CODE', values = 'RETURN').loc[ls_twindow].copy()
    df_LRET = np.log(df_RET + 1).fillna(0)

    df_LRET_M = df_LRET.rolling(21).sum()
    # 初始化，本月累计
    df_max = df_LRET_M.copy()
    df_min = df_LRET_M.copy()
    
    # 比较过去12个月的月累计对数收益，取最大最小值
    for i in range(1, 12):
        df_ref = df_LRET_M.shift(21 * i)
        df_max[df_max < df_ref] = df_ref[df_max < df_ref]
        df_min[df_min > df_ref] = df_ref[df_min > df_ref]
    
    df_output = df_max - df_min
    df_output = df_output.loc[ls_tdate].stack().reset_index().copy()

    df_output.columns = ['TRADEDATE', 'STOCK_CODE', 'VALUE']
    df_output['FACTOR'] = 'CMRA'

    return df_output

if __name__ == '__main__':
    pass