# -*- coding: utf-8 -*-
# 计算截面持仓因子持仓权重

import numpy as np

def factor_cta_coig(df_mktdata, df_qualified, n_oigrowth, n_hold):
    '''
    计算截面持仓因子持仓权重

    Parameters
    ----------
    df_mktdata : DataFrame - 日频行情数据
    df_qualified : DataFrame - 可以交易品种数据
    n_oigrowth : int - 持仓变化测算时间[日]
    n_hold : int - 多/空持单边持仓合约数

    Returns
    -------
    DataFrame
    '''
    df_oig = df_mktdata.query('MTYPE == 1').pivot(index = 'TRADEDATE', columns = 'STDCODE', values = 'OI').pct_change(n_oigrowth, fill_method = None)
    
    df_signal = ((df_oig * df_qualified).rank(method = 'min', axis = 1, ascending = False) <= n_hold) * 1
    df_signal -= ((df_oig * df_qualified).rank(method = 'min', axis = 1, ascending = True) <= n_hold) * 1

    df_signal.fillna(0, inplace = True)

    se_holdnum = df_signal.apply(np.abs).sum(axis = 1)
    df_holdpct = df_signal.divide(se_holdnum, axis = 0)

    return df_holdpct

if __name__ == '__main__':
    pass