# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np

def mod_colunify(df_tounify, df_stkqual):
    """
    将源数据统一列标

    Parameters
    ----------
        df_tounify : DataFrame - 待统一数据
        df_stkqual : DataFrame - 符合条件的股票标签时序

    Returns
    -------
    DataFrame
    """
    ls_qualcol = df_stkqual.columns.to_list()

    ls_comcol = [i for i in df_tounify.columns.to_list() if i in ls_qualcol]
    ls_qualonly = [i for i in ls_qualcol if i not in ls_comcol]

    df_colunify = df_tounify[ls_comcol].copy()
    if len(ls_qualonly) > 0:
        df_colunify = pd.concat([df_colunify, pd.DataFrame(index = df_colunify.index, columns = ls_qualonly)], axis = 1)

    # df_colunify = df_colunify[ls_qualcol].astype(float)

    return df_colunify

def mod_datawinsorize(df, df_stkqual, threshold = 3):
    """
    对因子数据DataFrame进行去尾处理

    Parameters
    ----------
        df : DataFrame - 单因子数据
        df_stkqual : DataFrame - 符合条件的股票标签时序
        threshold : float - MAD去尾阈值

    Returns
    -------
    DataFrame
    """

    # 统一列标
    df_towsr = mod_colunify(df, df_stkqual)
    ls_twindow = df.index.to_list()

    # 统一表结构，去除非样本股票
    df_towsr *=  df_stkqual.loc[ls_twindow]

    # Winsorize
    # l_upper = df_towsr.quantile(1 - threshold / 100, axis = 1, interpolation = 'nearest')
    # l_lower = df_towsr.quantile(threshold / 100, axis = 1, interpolation = 'nearest')
    # df_towsr = df_towsr.clip(upper = l_upper, lower = l_lower, axis = 0)

    # Winsorize_MAD
    df_median = df_towsr.median(axis = 1)
    df_mad = df_towsr.sub(df_median, axis = 0).abs().median(axis = 1)
    k = 1.4826

    l_upper = df_median + threshold * k * df_mad
    l_lower = df_median - threshold * k * df_mad
    df_winsorized = df_towsr.clip(upper = l_upper, lower = l_lower, axis = 0)

    return df_winsorized

def mod_datastandardize(df, df_MV, df_stkqual):
    """
    对因子数据DataFrame进行标准化处理

    Parameters
    ----------
        df : DataFrame - 单因子数据
        df_MV : DataFrame - 日股票市值数据
        df_stkqual : DataFrame - 符合条件的股票标签时序

    Returns
    -------
    DataFrame
    """

    # 统一列标
    df_tostd = mod_colunify(df, df_stkqual)
    ls_twindow = df.index.to_list()
    df_weight = mod_colunify(df_MV, df_stkqual).loc[ls_twindow]

    # 统一表结构，去除非样本股票
    df_tostd *=  df_stkqual.loc[ls_twindow]
    df_weight *=  df_stkqual.loc[ls_twindow]

    # Standardize
    df_mean = df_tostd.mean(axis = 1)
    df_wmean = (df_tostd * df_weight).sum(axis = 1) / df_weight.sum(axis = 1)
    df_std = df_tostd.std(axis = 1)
    df_std[df_std == 0] = np.NAN

    df_standardized = df_tostd.sub(df_wmean, axis = 0).div(df_std, axis = 0)

    return df_standardized

def mod_makeupann(df_tomakeup, df_ANN, ls_tdate):
    """
    将年报数据补充至交易日时序数据

    Parameters
    ----------
    df_tomakeup : DataFrame - 待补充年报数据
    df_ANN : DataFrame - 年报实际披露时间
    ls_tdate : List [str] - 交易日序列

    Returns
    -------
    DataFrame
        index : TRADEDATE 交易日期 'yyyymmdd'
        columns : WIND股票代码
    """    
    df_data = mod_colunify(df_tomakeup, df_ANN)
    df_sANN = df_ANN.loc[df_data.index.to_list()].copy()

    df_sdata = df_data.stack().to_frame().reset_index().dropna().copy()
    df_sdata.columns = ['REPORT_DATE', 'STOCK_CODE', 'VALUE']
    df_sANN = df_sANN.stack().to_frame().reset_index().dropna().copy()
    df_sANN.columns = ['REPORT_DATE', 'STOCK_CODE', 'TRADEDATE']

    df_rmdata = df_sdata.merge(df_sANN, on = ['STOCK_CODE', 'REPORT_DATE'], how = 'left').dropna().sort_values(['REPORT_DATE']).groupby(['TRADEDATE', 'STOCK_CODE'])[['VALUE']].last().reset_index()
    df_rmdata = df_rmdata.pivot(index = 'TRADEDATE', columns = 'STOCK_CODE', values = 'VALUE')
    
    df_output = pd.DataFrame(index = pd.date_range('1990-01-01', ls_tdate[-1]).strftime('%Y%m%d')).join(df_rmdata, how = 'left').ffill()
    df_output = df_output.loc[ls_tdate]

    return df_output

def mod_makeupind(df_tomakeup, df_industry, df_stkqual):
    """
    将截面空缺数据补充为行业中位数

    Parameters
    ----------
    df_tomakeup : DataFrame - 待补充截面数据
    df_industry : DataFrame - 一级行业时序标签
    df_stkqual : DataFrame - 符合条件的股票时序标签

    Returns
    -------
    DataFrame
    """    
    ls_twindow = df_tomakeup['TRADEDATE'].unique().tolist()

    df_qual = df_stkqual.loc[ls_twindow].stack().to_frame().reset_index().dropna()
    df_qual.columns = ['TRADEDATE', 'STOCK_CODE', 'QUAL']

    df_ind = df_industry.loc[ls_twindow].stack().to_frame().reset_index().dropna()
    df_ind.columns = ['TRADEDATE', 'STOCK_CODE', 'INDUSTRY']

    # 不可去除na, 否则会导致无行业标签的股票代码丢失, 代码以stkqual为基准
    df_qualind = df_qual.query('QUAL == 1').merge(df_ind, on = ['TRADEDATE', 'STOCK_CODE'], how = 'left')
    df_qualind = df_qualind.merge(df_tomakeup[['TRADEDATE', 'FACTOR_ID']].drop_duplicates(), on = ['TRADEDATE'], how = 'left')

    df_merge = df_qualind.merge(df_tomakeup, on = ['TRADEDATE', 'STOCK_CODE', 'FACTOR_ID'], how = 'left')
    df_indmedian = df_merge.groupby(['TRADEDATE', 'INDUSTRY', 'FACTOR_ID'])[['LOAD']].median().reset_index().rename({'LOAD': 'MEDIAN'}, axis = 1)
    df_merge = df_merge.merge(df_indmedian, on = ['TRADEDATE', 'INDUSTRY', 'FACTOR_ID'], how = 'left')
    df_merge['ISRAW'] = 1
    df_merge.loc[df_merge['LOAD'].isna(), 'ISRAW'] = 0
    df_merge.loc[df_merge['LOAD'].isna(), 'LOAD'] = df_merge.loc[df_merge['LOAD'].isna(), 'MEDIAN']
    df_output = df_merge[['TRADEDATE', 'STOCK_CODE', 'FACTOR_ID', 'LOAD', 'ISRAW']]

    return df_output

if __name__ == '__main__':
    pass