# -*- coding: utf-8 -*-
# 计算私募基金策略指数周度序列
import pandas as pd

def cal_fund_weeklyindex(df_funddata):
    """
    计算私募基金策略指数周度序列

    Parameters
    ----------
    df_funddata : DataFrame - 清洗后的基金净值对齐周度序列

    Returns
    -------
    DataFrame

    """
    df_meanret = df_funddata.groupby(['STRATEGY', 'TRADEDATE'])[['BENCHMARK_RETURN', 'FUND_RETURN']].mean().reset_index()
    df_indexret = df_meanret.pivot(index = 'TRADEDATE', columns = 'STRATEGY', values = 'FUND_RETURN')
    df_bmret = df_meanret.pivot(index = 'TRADEDATE', columns = 'STRATEGY', values = 'BENCHMARK_RETURN')
    df_exceret = (df_indexret + 1) / (df_bmret + 1) - 1

    df_indexret.loc[df_indexret.index[0], :] = 0
    df_bmret.loc[df_bmret.index[0], :] = 0
    df_exceret.loc[df_exceret.index[0], :] = 0

    df_indexnav = (df_indexret + 1).cumprod().stack().to_frame(name = 'FUND_NAV')
    df_bmnav = (df_bmret + 1).cumprod().stack().to_frame(name = 'BENCHMARK_NAV')
    df_excenav = (df_exceret + 1).cumprod().stack().to_frame(name = 'EXCESSIVE_NAV')
    df_indexret.loc[df_indexret.index[0], :] = None
    df_bmret.loc[df_bmret.index[0], :] = None
    df_exceret.loc[df_exceret.index[0], :] = None

    df_indexdata = df_indexnav.join(df_bmnav, how = 'outer').join(df_excenav, how = 'outer')\
        .join(df_indexret.stack().to_frame(name = 'FUND_RETURN'), how = 'left')\
        .join(df_bmret.stack().to_frame(name = 'BENCHMARK_RETURN'), how = 'left')\
        .join(df_exceret.stack().to_frame(name = 'EXCESSIVE_RETURN'), how = 'left').reset_index()

    df_indexdata['FUND_NAME'] = df_indexdata['STRATEGY'] + '_策略指数'
    df_indexdata['COMPANY_NAME'] = '策略指数'
    df_indexdata['COMPANY_SIZE'] = '不适用'

    df_indexdata = df_indexdata.merge(df_funddata[['STRATEGY', 'BENCHMARK_CODE']].drop_duplicates(), on = 'STRATEGY', how = 'left')

    return df_indexdata

if __name__ == '__main__':
    pass