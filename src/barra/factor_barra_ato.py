# -*- coding: utf-8 -*-
from barra import mod_makeupann

def factor_barra_ato(df_yreport, ls_tdate):
    """
    计算ATO因子

    Computed as, ATO = Sales / TA, where Sales is the trailing 12-month sales,
    and TA is the most recently reported total assets.
    
    Parameters
    ----------
        df_yreport : DataFrame - 年度财务指标表数据
        ls_tdate : List [str] - 交易日序列

    Returns
    -------
    DataFrame
    
    """
    df_yreport['ATO'] = df_yreport['TOR'] / df_yreport['TA']
    df_ATO = df_yreport.pivot(index = 'REPORT_DATE', columns = 'STOCK_CODE', values = 'ATO')
    df_ANN = df_yreport.pivot(index = 'REPORT_DATE', columns = 'STOCK_CODE', values = 'DISCLOSE_DATE')

    df_output = mod_makeupann(df_ATO, df_ANN, ls_tdate).stack().reset_index()
    df_output.columns = ['TRADEDATE', 'STOCK_CODE', 'VALUE']

    df_output['FACTOR'] = 'ATO'

    return df_output

if __name__ == '__main__':
    pass