#!/usr/bin/env python
# -*- encoding:utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))

from src.portfolio import PortfolioData

from wework.wework_msg import *

def send_pfdata(userid: str = ''):
    '''
    更新基金数据
    '''
    try:
        pfd = PortfolioData()
        pfd.run()
    except Exception as e:
        sendmsg_text(content = f'{e}', userid = userid)

    return

if __name__ == '__main__':
    send_pfdata(userid = 'Vulpes')