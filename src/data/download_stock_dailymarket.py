# -*- coding: utf-8 -*-
# 下载股票日频行情数据

import pandas as pd
from basic import get_tradedate

def download_stock_dailymarket(startdate, enddate, conn_ts):
    """
    下载股票日频行情数据

    Parameters
    ----------
    startdate : str - 'yyyymmdd' 时间窗口起点
    enddate : str - 'yyyymmdd' 时间窗口终点
    conn_ts : tushare数据接口

    Returns
    -------
    DataFrame
    """
    ls_tdate = get_tradedate(startdate, enddate)
    if len(ls_tdate) == 0:
        return None
    
    ls_stkdate = []
    ls_stkadj = []
    for tdate in ls_tdate:
        try:
            ls_stkdate.append(conn_ts.daily(trade_date = tdate).copy())
            ls_stkadj.append(conn_ts.adj_factor(trade_date = tdate).copy())
        except:
            raise Exception('[Tushare]: Stock market data download failed.')
    
    ls_stkdata = [i for i in ls_stkdate if i.shape[0] > 0]
    ls_stkadj = [i for i in ls_stkadj if i.shape[0] > 0]

    if len(ls_stkdata) > 0:
        df_stkdate = pd.concat(ls_stkdata, ignore_index = True).drop_duplicates()
        df_stkadj = pd.concat(ls_stkadj, ignore_index = True).drop_duplicates()
        df_stkdate = df_stkdate.merge(df_stkadj, on = ['trade_date', 'ts_code'], how = 'left')
        
        df_stkdate.columns = [i.upper() for i in df_stkdate.columns]
        df_stkdate.rename({'TS_CODE': 'STOCK_CODE', 'TRADE_DATE': 'TRADEDATE', 'PCT_CHG': 'RETURN'}, axis = 1, inplace = True)
        df_stkdate['RETURN'] /= 100
    else:
        df_stkdate = pd.DataFrame()

    return df_stkdate

if __name__ == '__main__':
    pass