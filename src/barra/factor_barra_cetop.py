# -*- coding: utf-8 -*-

from barra import mod_makeupann

def factor_barra_cetop(df_dind, df_yreport, ls_tdate):
    """
    计算CETOP因子

    Computed by dividing the trailing 12-month cash earnings by 
    the current market capitalization.

    Parameters
    ----------
        df_dind : DataFrame - 日频股票数据
        ls_tdate : List [str] - 交易日序列

    Returns
    -------
    DataFrame
    
    """
    df_mv = df_dind[df_dind['TRADEDATE'].isin(ls_tdate)][['TRADEDATE', 'STOCK_CODE', 'MV']].copy()
    df_NCE = df_yreport.pivot(index = 'REPORT_DATE', columns = 'STOCK_CODE', values = 'NCE')
    df_ANN = df_yreport.pivot(index = 'REPORT_DATE', columns = 'STOCK_CODE', values = 'DISCLOSE_DATE')
    df_ce = mod_makeupann(df_NCE, df_ANN, ls_tdate).stack().reset_index()
    df_ce.columns = ['TRADEDATE', 'STOCK_CODE', 'NCE']

    df_output = df_mv.merge(df_ce, on = ['TRADEDATE', 'STOCK_CODE'], how = 'inner')
    df_output['CETOP'] = df_output['NCE'] / df_output['MV']

    df_output = df_output[['TRADEDATE', 'STOCK_CODE', 'CETOP']].copy()
    df_output.columns = ['TRADEDATE', 'STOCK_CODE', 'VALUE']

    df_output['FACTOR'] = 'CETOP'

    return df_output

if __name__ == '__main__':
    pass