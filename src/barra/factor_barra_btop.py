# -*- coding: utf-8 -*-

def factor_barra_btop(df_dind, ls_tdate):
    """
    计算BTOP因子

    Computed by dividing the last reported book value of common equity 
    by the current market capitalization.

    Parameters
    ----------
        df_dind : DataFrame - 日频股票数据
        ls_tdate : List [str] - 交易日序列

    Returns
    -------
    DataFrame
    
    """
    df_output = df_dind[df_dind['TRADEDATE'].isin(ls_tdate)][['TRADEDATE', 'STOCK_CODE', 'PB']].copy()
    df_output['PB'] = 1 / df_output['PB']
    df_output.columns = ['TRADEDATE', 'STOCK_CODE', 'VALUE']

    df_output['FACTOR'] = 'BTOP'

    return df_output

if __name__ == '__main__':
    pass