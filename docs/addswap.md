# 腾讯云新增swap交换空间

腾讯云debian系统在默认情况下没有分配swap空间，容易出现暴内存的情况，可以通过以下方法分配部分磁盘空间作为swap。

Linux下可以创建两种类型的交换空间，一种是swap分区，一种是swap文件。前者适合有空闲的分区可以使用，后者适合于没有空的硬盘分区，硬盘的空间都已经分配完毕，这里我们只能采用后者。按照swap为物理内存1~2倍原则，本例将swap文件设置为4GB。

新建用来交换的4G swap路径：/mnt/swap（路径和交换文件名称可以自定义）

1. 创建用于交换分区的文件，命令需要运行一段时间，dd创建的文件总大小等于bs * count，本例是4096k * 1024000 = 4G

```
dd if=/dev/zero of=/mnt/swap bs=4M count=1024 

# dd：可以创建指定大小(4G)的文件
# if：指定输入设备
# /dev/zero：无限0资源
# of：指定输出设备
# bs：每个块的大小
# count：块的数量
```

2. 设置交换分区文件：mkswap /mnt/swap
3. 启用交换分区文件：swapon /mnt/swap
4. 添加可读可写权限

```
chown root:root /mnt/swap
chmod 0600 /mnt/swap
```

5. 开机自动挂载swap分区

```
vim /etc/fstab
```

在最后一行插入：/mnt/swap swap swap defaults 0 0


## PS

当不需要交换文件时，可以使用以下步骤将其删除：

禁用交换文件：swapoff /mnt/swap

删除交换文件：rm -f /mnt/swap

编辑vim /etc/fstab文件，删除以下内容，在系统下次引导时就不会启用交换文件了

/mnt/swap swap swap defaults 0 0
