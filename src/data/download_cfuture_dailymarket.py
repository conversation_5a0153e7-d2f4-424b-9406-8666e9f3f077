# -*- coding: utf-8 -*-
# 下载商品期货日频数据

# 郑商所合约切换
# 甲醇 ME -> MA
# 动力煤 TZ -> ZC
# 早籼稻 ER -> RI
# 强麦 WS -> WH
# 菜油 RO -> OI
# 硬麦 WT 退市

import pandas as pd
import time

def download_cfuture_dailymarket(startdate, enddate, conn_ts):
    """
    下载商品期货日频数据

    Parameters
    ----------
    startdate : str - 'yyyymmdd' 时间窗口起点
    enddate : str - 'yyyymmdd' 时间窗口终点
    conn_ts : tushare数据接口

    Returns
    -------
    DataFrame
    """
    # 下载日行情
    ls_fmarket = ['SHFE', 'DCE', 'CZCE', 'INE', 'GFEX']
    ls_data = []
    ls_info = []

    for fmarket in ls_fmarket:
        df_info = conn_ts.fut_basic(exchange = fmarket, fut_type = '1', fields = 'ts_code, exchange, fut_code, list_date, delist_date')
        ls_info.append(df_info.copy())
        ls_fcode = df_info['ts_code'].tolist()

        ls_subdata = []
        initdate = max(df_info['list_date'].min(), startdate)

        if initdate > enddate:
            continue

        stopdate = enddate
        # 每个交易所有3次断点机会
        max_sleep = 3
        num_sleep = 0
        while initdate < stopdate:
            try:
                df_data = conn_ts.fut_daily(exchange = fmarket, start_date = initdate, end_date = stopdate).drop(columns = ['change1', 'change2'])
                stopdate = df_data['trade_date'].min()
                ls_subdata.append(df_data[(df_data['trade_date'] > stopdate) & (df_data['ts_code'].isin(ls_fcode))].copy())
            except:
                if num_sleep < max_sleep:
                    time.sleep(1)
                    num_sleep += 1
                    print('[Tushare]: Futures market data download tried {}.'.format(num_sleep))
                    continue
                else:
                    raise Exception('[Tushare]: Futures market data download failed.')

        if initdate == stopdate:
            try:
                df_data = conn_ts.fut_daily(exchange = fmarket, trade_date = initdate).drop(columns = ['change1', 'change2'])
                ls_subdata.append(df_data[df_data['ts_code'].isin(ls_fcode)].drop_duplicates().copy())
            except:
                raise Exception('[Tushare]: Futures market data download failed.')
            
        ls_subdata = [i for i in ls_subdata if i.shape[0] > 0]
        if len(ls_subdata) > 0:
            ls_data.append(pd.concat(ls_subdata, ignore_index = True).drop_duplicates())
    
    ls_data = [i for i in ls_data if i.shape[0] > 0]
    ls_info = [i for i in ls_info if i.shape[0] > 0]
    if len(ls_data) > 0:
        df_cfutdata = pd.concat(ls_data, ignore_index = True).drop_duplicates()
        df_cfutinfo = pd.concat(ls_info, ignore_index = True).drop_duplicates()

        df_cfutmkt = df_cfutdata.merge(df_cfutinfo, on = 'ts_code', how = 'inner')
        df_cfutmkt.columns = [i.upper() for i in df_cfutmkt.columns]
        df_cfutmkt.rename({'TS_CODE': 'FUT_CODE',
                        'FUT_CODE': 'STDCODE',
                        'TRADE_DATE': 'TRADEDATE',
                        'LIST_DATE': 'LISTDATE', 
                        'DELIST_DATE': 'DLISTDATE'}, axis = 1, inplace = True)
        if df_cfutmkt.shape[0] > 0:
            df_cfutmkt['FUT_CODE'] = df_cfutmkt['FUT_CODE'].str.split('.', expand = True)[0]
        else:
            df_cfutmkt =  pd.DataFrame()

    else:
        df_cfutmkt =  pd.DataFrame()

    return df_cfutmkt

if __name__ == '__main__':
    pass