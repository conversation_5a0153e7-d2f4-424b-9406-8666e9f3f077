# -*- coding: utf-8 -*-
# 提取Barra因子收益率数据

import pandas as pd
from basic import db_vulpes

def get_barraret(startdate: str, enddate: str):
    '''
    提取Barra因子收益率数据

    Parameters
    ----------
    startdate : str - 'yyyymmdd' 时间窗口起点
    enddate : str - 'yyyymmdd' 时间窗口终点

    Returns
    -------
    DataFrame
    '''
    conn = db_vulpes()
    sql = '''
    SELECT
        TRADEDATE,
        FACTOR_ID,
        RETURN
    FROM 
        BARRA_DAILYRETURN
    WHERE
        MODEL = 'CNE6' AND
        TRADEDATE >= '{}' AND 
        TRADEDATE <= '{}'
    '''.format(startdate, enddate)
    df_retdata = pd.read_sql(sql, con = conn)


    sql = '''
    SELECT DISTINCT 
        INDEX_CODE,
        INDEX_NAME
    FROM 
        INDEX_SWMEMBER
    '''
    df_infodata = pd.read_sql(sql, con = conn)
    dic_name = dict(zip(df_infodata['INDEX_CODE'], df_infodata['INDEX_NAME']))
    ls_style = ['BETA', 'SIZE', 'BTOP', 'MIDCAP', 'EARNING', 'GROWTH', 'LEVERAGE', 
       'MOMENTUM', 'VOLATILITY', 'LIQUIDITY', 'DIVIDEND', 'EARNINGVAR', 'INVENSTMENT', 
       'PROFITABLITY', 'STREVERSAL', 'COUNTRY']
    ls_col = list(dic_name.values()) + ls_style
    df_barraret = df_retdata.pivot(index = 'TRADEDATE', columns = 'FACTOR_ID', values = 'RETURN').rename(dic_name, axis = 1)[ls_col]
    
    return df_barraret

if __name__ == '__main__':
    pass