# -*- coding: utf-8 -*-
# 下载交易日序列

import pandas as pd

def download_tradedate(conn_ts):
    """
    下载交易日序列

    Parameters
    ----------
    conn_ts : tushare数据接口

    Returns
    -------
    DataFrame
    """
    try:
        df_date = conn_ts.trade_cal(exchange = 'SSE').sort_values('cal_date', ascending = True)[['cal_date', 'pretrade_date', 'is_open']].copy()
    except:
        raise Exception('[Tushare] tradedate data download failed.')
    
    df_date.columns = ['CAL_DATE', 'PRE_TDATE', 'FRQ_D']

    df_date['YDATE'] = df_date['CAL_DATE'].str[0:4]
    df_date['MDATE'] = df_date['CAL_DATE'].str[0:6]
    df_date['SDATE'] = ((df_date['CAL_DATE'].str[4:6].astype(int) + 2) // 3) + df_date['CAL_DATE'].str[0:4].astype(int) * 100
    df_date['WDATE'] = df_date[pd.to_datetime(df_date['CAL_DATE']).dt.weekday == 6]['CAL_DATE']
    df_date['WDATE'] = df_date['WDATE'].bfill()
    for frq in ['W', 'M', 'S', 'Y']:
        df_date['FRQ_{}'.format(frq)] = 0
        df_date.loc[df_date['CAL_DATE'].isin(df_date.query('FRQ_D == 1').groupby('{}DATE'.format(frq)).last()['CAL_DATE']), 'FRQ_{}'.format(frq)] = 1
        df_tdate = df_date.query('FRQ_D == 1').groupby('{}DATE'.format(frq)).last()['CAL_DATE'].to_frame('{}_TDATE'.format(frq)).reset_index().copy()
        df_date = df_date.merge(df_tdate, on = '{}DATE'.format(frq), how = 'left')

    df_dnum = df_date[df_date['FRQ_D'] == 1][['CAL_DATE']]
    df_dnum['TDNUM'] = range(1, len(df_dnum) + 1)
    df_date = df_date.merge(df_dnum, on = 'CAL_DATE', how = 'left')
    df_date['TDNUM'].ffill(inplace = True)

    df_wnum = df_date[df_date['FRQ_W'] == 1][['W_TDATE']]
    df_wnum['TWNUM'] = range(1, len(df_wnum) + 1)
    df_date = df_date.merge(df_wnum, on = 'W_TDATE', how = 'left')
    df_date['TWNUM'].ffill(inplace = True)

    df_output = df_date[['CAL_DATE', 'PRE_TDATE', 'FRQ_D', 'FRQ_W', 'FRQ_M', 'FRQ_S', 'FRQ_Y', 'W_TDATE', 'M_TDATE', 'S_TDATE', 'Y_TDATE', 'TDNUM', 'TWNUM']].copy()
    return df_output

if __name__ == '__main__':
    pass