# -*- coding: utf-8 -*-
# 下载转债基本信息
import pandas as pd
import time

def download_cbond_info(conn_ts):
    """
    下载转债基本信息

    Parameters
    ----------
    conn_ts : tushare数据接口

    Returns
    -------
    DataFrame
    """
    max_sleep = 3
    num_sleep = 0

    maxset = 1500
    offset = 0

    ls_cbondinfo = []
    while True:
        try:
            df_data = conn_ts.cb_basic(offset = str(offset), fields = ['ts_code', 'bond_short_name', 'stk_code', 'maturity', 'maturity_date','issue_size', 'list_date', 'issue_rating']).dropna()
        except:
            if num_sleep < max_sleep:
                time.sleep(1)
                num_sleep += 1
                print('[Tushare]: Cbond info data download tried {}.'.format(num_sleep))
                continue
            else:
                raise Exception('[Tushare]: Cbond info data download failed.')
            
        ls_cbondinfo.append(df_data.copy())
        if df_data.shape[0] >= maxset:
            offset += maxset
        else:
            break
    
    ls_cbondinfo = [i for i in ls_cbondinfo if i.shape[0] > 0]
    if len(ls_cbondinfo) > 0:
        df_cbondinfo = pd.concat(ls_cbondinfo, ignore_index = True).drop_duplicates()
        df_cbondinfo['maturity_date'] = df_cbondinfo['maturity_date'].str.replace('-', '')
        df_cbondinfo['list_date'] = df_cbondinfo['list_date'].str.replace('-', '')
        df_cbondinfo.columns = [i.upper() for i in df_cbondinfo.columns]
        df_cbondinfo.rename({'TS_CODE': 'CBOND_CODE',
                        'BOND_SHORT_NAME': 'CBOND_NAME',
                        'STK_CODE': 'STOCK_CODE',
                        'LIST_DATE': 'LISTDATE'}, axis = 1, inplace = True)
    else:
        df_cbondinfo = pd.DataFrame()

    return df_cbondinfo

if __name__ == '__main__':
    pass