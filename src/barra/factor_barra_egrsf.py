# -*- coding: utf-8 -*-

def factor_barra_egrsf(df_dcons, ls_tdate):
    """
    计算EGRSF因子

    Short-term (1 year) earnings growth forecasted by analysts.

    Parameters
    ----------
        df_dcons : DataFrame - 日频股票分析师预期数据
        ls_tdate : List [str] - 交易日序列

    Returns
    -------
    DataFrame

    """
    df_output = df_dcons[df_dcons['TRADEDATE'].isin(ls_tdate)][['TRADEDATE', 'STOCK_CODE', 'EST_EGS']].copy()
    df_output.columns = ['TRADEDATE', 'STOCK_CODE', 'VALUE']

    df_output['FACTOR'] = 'EGRSF'

    return df_output

if __name__ == '__main__':
    pass