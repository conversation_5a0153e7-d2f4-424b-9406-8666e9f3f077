# -*- coding: utf-8 -*-
# ETF金融期权标的ETF日频价格数据

import pandas as pd

def download_foption_etfmarket(startdate, enddate, conn_ts):
    """
    ETF金融期权标的ETF日频价格数据

    Parameters
    ----------
    startdate : str - 'yyyymmdd' 时间窗口起点
    enddate : str - 'yyyymmdd' 时间窗口终点
    conn_ts : tushare数据接口

    Returns
    -------
    DataFrame
    """
    dic_exchange = {'SSE': '上交所', 'SZSE': '深交所'}
    
    # 下载日行情
    ls_data = []
    for exc in dic_exchange.keys():
        # 通过期权获取ETF代码和名称
        try:
            df_optinfo = conn_ts.opt_basic(exchange = exc, fields = ['name', 'opt_code'])
        except:
            raise Exception('[Tushare]: Option info access failed.')
        df_optinfo['name'] = df_optinfo['name'].str.split('期权', expand = True)[0]
        df_optinfo['opt_code'] = df_optinfo['opt_code'].str.split('OP', expand = True)[1]
        dic_codename = df_optinfo.drop_duplicates().set_index('opt_code').to_dict('dict')['name']

        ls_subdata = []
        for etfcode in dic_codename:
            # ETF起始日
            if startdate == '':
                try:
                    initdate = conn_ts.fund_basic(ts_code = etfcode, fields = ['list_date'])['list_date'][0]
                except:
                    raise Exception('[Tushare]: ETF info access failed.')
            else:
                initdate = startdate
            
            stopdate = enddate
            while initdate < stopdate:
                try:
                    df_data = conn_ts.fund_daily(ts_code = etfcode, start_date = initdate, end_date = stopdate).drop(columns = ['change'])
                    df_data['fund_name'] = dic_codename[etfcode]
                    df_data['exchange'] = dic_exchange[exc]
                    stopdate = df_data['trade_date'].min()
                    ls_subdata.append(df_data[df_data['trade_date'] > stopdate].drop_duplicates().copy())
                except:
                    raise Exception('[Tushare]: ETF market data download failed.')

            if initdate == stopdate:
                try:
                    df_data = conn_ts.fund_daily(ts_code = etfcode, trade_date = initdate).drop(columns = ['change'])
                    df_data['fund_name'] = dic_codename[etfcode]
                    df_data['exchange'] = dic_exchange[exc]
                    ls_subdata.append(df_data.drop_duplicates().copy())
                except:
                    raise Exception('[Tushare]: ETF market data download failed.')
        ls_subdata = [i for i in ls_subdata if i.shape[0] > 0]
        if len(ls_subdata) > 0:
            ls_data.append(pd.concat(ls_subdata, ignore_index = True))

    ls_data = [i for i in ls_data if i.shape[0] > 0]
    if len(ls_data) > 0:
        df_etfmarket = pd.concat(ls_data, ignore_index = True)
        df_etfmarket.columns = [i.upper() for i in df_etfmarket.columns]
        df_etfmarket.rename({'TS_CODE': 'FUND_CODE',
                        'PCT_CHG': 'RETURN',
                        'TRADE_DATE': 'TRADEDATE'}, axis = 1, inplace = True)
        df_etfmarket['RETURN'] /= 100
    else:
        df_etfmarket = pd.DataFrame()

    return df_etfmarket

if __name__ == '__main__':
    pass