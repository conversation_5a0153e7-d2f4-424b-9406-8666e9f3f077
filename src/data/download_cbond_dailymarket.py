# -*- coding: utf-8 -*-
# 下载转债日频数据

import pandas as pd
import time

def download_cbond_dailymarket(startdate, enddate, conn_ts):
    """
    下载转债日频数据

    Parameters
    ----------
    startdate : str - 'yyyymmdd' 时间窗口起点
    enddate : str - 'yyyymmdd' 时间窗口终点
    conn_ts : tushare数据接口

    Returns
    -------
    DataFrame
    """
    if startdate == '':
        initdate = '19930210'
    else:
        initdate = startdate

    stopdate = enddate
    ls_cbddata = []

    max_sleep = 3
    num_sleep = 0
    while initdate < stopdate:
        try:
            df_data = conn_ts.cb_daily(start_date = initdate, end_date = stopdate, fields = 'ts_code, trade_date, pre_close, open, high, low, close, pct_chg, vol, amount, bond_value, bond_over_rate, cb_value, cb_over_rate')
            stopdate = df_data['trade_date'].min()
            ls_cbddata.append(df_data[df_data['trade_date'] > stopdate].copy())
        except:
            if num_sleep < max_sleep:
                time.sleep(1)
                num_sleep += 1
                print('[Tushare]: Cbond market data download tried {}.'.format(num_sleep))
                continue
            else:
                raise Exception('[Tushare]: Cbond market data download failed.')

    if initdate == stopdate:
        try:
            df_data = conn_ts.cb_daily(trade_date = stopdate, fields = 'ts_code, trade_date, pre_close, open, high, low, close, pct_chg, vol, amount, bond_value, bond_over_rate, cb_value, cb_over_rate')
            ls_cbddata.append(df_data.copy())
        except:
            raise Exception('[Tushare]: Cbond market data download failed.')

    ls_cbddata = [i for i in ls_cbddata if i.shape[0] > 0]
    if len(ls_cbddata) > 0:
        df_cbondmkt = pd.concat(ls_cbddata, ignore_index = True).drop_duplicates()
        df_cbondmkt.columns = [i.upper() for i in df_cbondmkt.columns]
        df_cbondmkt.rename({'TS_CODE': 'CBOND_CODE',
                        'TRADE_DATE': 'TRADEDATE',
                        'PCT_CHG': 'RETURN',
                        'BOND_VALUE': 'BD_VALUE',
                        'BOND_OVER_RATE': 'BD_PRATE',
                        'CB_OVER_RATE': 'CB_PRATE'}, axis = 1, inplace = True)
        df_cbondmkt['RETURN'] /= 100
    else:
        df_cbondmkt = pd.DataFrame()
    
    return df_cbondmkt

if __name__ == '__main__':
    pass