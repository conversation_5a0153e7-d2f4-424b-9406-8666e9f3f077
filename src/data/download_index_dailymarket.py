# -*- coding: utf-8 -*-
# 下载指数日频数据

import pandas as pd

def download_index_dailymarket(startdate, enddate, conn_ts):
    """
    下载指数日频数据

    Parameters
    ----------
    startdate : str - 'yyyymmdd' 时间窗口起点
    enddate : str - 'yyyymmdd' 时间窗口终点
    conn_ts : tushare数据接口

    Returns
    -------
    DataFrame
    """
    ls_indexcode = tuple(['000016.SH', '000300.SH', '000905.SH', '000906.SH', '000852.SH', '932000.CSI', '000985.CSI', '000510.CSI'])
    ls_indexdata = []

    for code in ls_indexcode:
        try:
            df = conn_ts.index_daily(ts_code = code, start_date = startdate, end_date = enddate)
            ls_indexdata.append(df.copy())
        except:
            raise Exception('[Tushare]: Index market data download failed.')

    ls_indexdata = [i for i in ls_indexdata if i.shape[0] > 0]
    if len(ls_indexdata) > 0:
        df_indexmkt = pd.concat(ls_indexdata, ignore_index = True).drop_duplicates().drop(columns = ['change'])
        df_indexmkt.columns = [i.upper() for i in df_indexmkt.columns]
        df_indexmkt.rename({'TS_CODE': 'INDEX_CODE', 'TRADE_DATE': 'TRADEDATE', 'PCT_CHG': 'RETURN'}, axis = 1, inplace = True)
        df_indexmkt['RETURN'] /= 100
    else:
        df_indexmkt = pd.DataFrame()

    return df_indexmkt

if __name__ == '__main__':
    pass