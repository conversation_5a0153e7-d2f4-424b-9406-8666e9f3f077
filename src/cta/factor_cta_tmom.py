# -*- coding: utf-8 -*-
# 计算时序动量因子持仓权重

import numpy as np

def factor_cta_tmom(df_mktdata, value, df_qualified, n_momentum, trg_value):
    '''
    计算时序动量因子持仓权重

    Parameters
    ----------
    df_mktdata : DataFrame - 日频行情数据
    value : str - 收益率测算目标
    df_qualified : DataFrame - 可以交易品种数据
    n_momentum : int - 动量测算时间[日]
    trg_value : float - 动量触发价格

    Returns
    -------
    DataFrame
    '''
    df_momret = (df_mktdata.query('MTYPE == 1').pivot(index = 'TRADEDATE', columns = 'STDCODE', values = value) + 1).apply(np.log).rolling(n_momentum).sum()
    df_signal = ((df_momret * df_qualified) > trg_value) * 1
    df_signal -= ((-df_momret * df_qualified) > trg_value) * 1
    df_signal.fillna(0, inplace = True)

    se_holdnum = df_signal.apply(np.abs).sum(axis = 1)
    df_holdpct = df_signal.divide(se_holdnum, axis = 0)
    
    return df_holdpct

if __name__ == '__main__':
    pass