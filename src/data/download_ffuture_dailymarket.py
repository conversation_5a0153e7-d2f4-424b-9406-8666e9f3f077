# -*- coding: utf-8 -*-
# 下载金融期货日频数据

import pandas as pd
import numpy as np
import time
from basic import get_tradedate

def download_ffuture_dailymarket(startdate, enddate, conn_ts):
    """
    下载金融期货日频数据

    Parameters
    ----------
    startdate : str - 'yyyymmdd' 时间窗口起点
    enddate : str - 'yyyymmdd' 时间窗口终点
    conn_ts : tushare数据接口

    Returns
    -------
    DataFrame
    """
    # 下载日行情
    if startdate == '':
        initdate = '20100416'
    else:
        initdate = startdate

    stopdate = enddate
    ls_ffutdata = []

    max_sleep = 3
    num_sleep = 0
    while initdate < stopdate:
        try:
            df_data = conn_ts.fut_daily(exchange = 'CFFEX', start_date = initdate, end_date = stopdate).drop(columns = ['change1', 'change2'])
            stopdate = df_data['trade_date'].min()
            ls_ffutdata.append(df_data[df_data['trade_date'] > stopdate].copy())
        except:
            if num_sleep < max_sleep:
                time.sleep(1)
                num_sleep += 1
                print('[Tushare]: Futures market data download tried {}.'.format(num_sleep))
                continue
            else:
                raise Exception('[Tushare]: Futures market data download failed.')

    if initdate == stopdate:
        try:
            df_data = conn_ts.fut_daily(exchange = 'CFFEX', trade_date = stopdate).drop(columns = ['change1', 'change2'])
            ls_ffutdata.append(df_data.drop_duplicates().copy())
        except:
            raise Exception('[Tushare]: Futures market data download failed.')

    ls_ffutdata = [i for i in ls_ffutdata if i.shape[0] > 0]
    if len(ls_ffutdata) > 0:
        df_ffutdate = pd.concat(ls_ffutdata, ignore_index = True).drop_duplicates()
        df_ffutdate.columns = [i.upper() for i in df_ffutdate.columns]
        df_ffutdate.rename({'TS_CODE': 'FUT_CODE',
                        'TRADE_DATE': 'TRADEDATE'}, axis = 1, inplace = True)
    else:
        df_ffutdate = pd.DataFrame(columns = ['FUT_CODE', 'TRADEDATE'])

    try:
        df_info = conn_ts.fut_basic(exchange = 'CFFEX', fut_type = '1', fields = 'ts_code, fut_code, list_date, delist_date')
    except:
        raise Exception('[Tushare] Financial Futures info data download failed.')
    df_info.rename({'ts_code': 'FUT_CODE',
                    'fut_code': 'STDCODE', 
                    'list_date': 'LISTDATE', 
                    'delist_date': 'DLISTDATE'}, axis = 1, inplace = True)

    # 定义合约类型
    ls_trange = get_tradedate(startdate = min(df_info['LISTDATE']), enddate = enddate)
    df_in = pd.DataFrame(index = ls_trange).join(df_info.pivot(index = 'LISTDATE', columns = 'FUT_CODE', values = 'FUT_CODE'), how = 'left').ffill()
    df_out = pd.DataFrame(index = ls_trange).join(df_info[df_info['DLISTDATE'] <= enddate].pivot(index = 'DLISTDATE', columns = 'FUT_CODE', values = 'FUT_CODE'), how = 'left')
    df_in[df_out.bfill().isna()] = np.nan
    df_fill = df_in.stack().reset_index()[['level_0', 'level_1']]
    df_fill.columns = ['TRADEDATE', 'FUT_CODE']
    df_ffutinfo = df_fill.merge(df_info, how = 'left', on = ['FUT_CODE'])
    df_ffutinfo['MTYPE'] = df_ffutinfo.groupby(['TRADEDATE', 'STDCODE'])[['DLISTDATE']].rank()

    # 数据整合
    df_ffutmkt = df_ffutdate.merge(df_ffutinfo, on = ['FUT_CODE', 'TRADEDATE'], how = 'inner')
    if df_ffutmkt.shape[0] > 0:
        df_ffutmkt['FUT_CODE'] = df_ffutmkt['FUT_CODE'].str.split('.', expand = True)[0]

    return df_ffutmkt

if __name__ == '__main__':
    pass