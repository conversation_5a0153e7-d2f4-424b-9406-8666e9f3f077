# -*- coding: utf-8 -*-

from openpyxl import load_workbook
import warnings
warnings.filterwarnings('ignore')

import pandas as pd
import numpy as np
import requests
import json
import pyexcel
# pyexcel pyexcel-xls pyexcel-xlsx

# 读取PDF
import pdfplumber

import imaplib
imaplib.Commands['ID'] = ('AUTH')
import zipfile
# 需要源代码decode("cp437") 改为 GBK
from fnmatch import fnmatch
from email import message_from_bytes
from email.header import decode_header
import re
import os
import time

# import dataframe_image as dfi
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import matplotlib.ticker as ticker
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

from basic import db_vulpes, get_tradedate
from portfolio.config import *

class PortfolioData():
    '''
    云端数据收集
    '''

    def __init__(self):
        '''
        构造函数
        '''
        # 邮箱信息
        self.username = KMAX['USERNAME']
        self.password = KMAX['PASSWORD']
        self.imap_url = KMAX['IMAP_URL']
        self.inbox = KMAX['INBOX']
        
        # 业绩基准数据库
        self.conn = db_vulpes(writer = True)
        
        # 本地文件路径
        self.path = os.path.dirname(os.path.realpath(__file__))
        self.path_rawfolder = os.path.join(self.path, 'kmax_mails')
        if not os.path.exists(self.path_rawfolder):
            os.makedirs(self.path_rawfolder)
        
        # 格式信息
        self.df_formation = pd.read_csv(os.path.join(self.path, 'KMAX_FORMATION.csv'))
        self.df_strategy = pd.read_csv(os.path.join(self.path, 'KMAX_STRATEGY.csv'))

        self.df_rawnav = pd.DataFrame()

    def decode_info(self, s, init = 0, mailaddr = False):
        '''
        解码函数
        '''
        try:
            if mailaddr:
                r_mail = r'[a-z0-9\.\-+_]+@[a-z0-9\.\-+_]+\.[a-z]+'
                value = None
                for info in decode_header(s):
                    if info[1]:
                        ls_match = re.findall(r_mail, info[0].decode(info[1]))
                    else:
                        try:
                            ls_match = re.findall(r_mail, info[0].decode())
                        except:
                            ls_match = re.findall(r_mail, info[0])
                    
                    if len(ls_match) > 0:
                        return ls_match[0]
            else:
                value, charset = decode_header(s)[init]
                if charset:
                    value = value.decode(charset)
        except:
            return None
        
        return value

    def get_body(self, msg):
        '''
        获取邮件内容
        '''
        if msg.is_multipart():
            return self.get_body(msg.get_payload(0))
        else:
            return msg.get_payload(None, decode = True).decode()
    
    def download_extention(self, lastdate: str = ''):

        try:
            search_range = 'SINCE ' + pd.to_datetime(str(lastdate)).strftime('%d-%b-%Y')
        except:
            search_range = 'ALL'

        emailserver = imaplib.IMAP4_SSL(self.imap_url)  # 邮箱服务器地址
        emailserver.login(self.username, self.password)
        if self.imap_url.split('.')[1] == '163':
            args = ("name", self.username, "version", "1.0.0", "vendor", "myclient")
            typ, dat = emailserver._simple_command('ID', '("' + '" "'.join(args) + '")')
        emailserver.select(self.inbox)
        _, message_ids = emailserver.search(None, search_range)

        for message_id in message_ids[0].split():
            # 获取邮件内容
            _, data = emailserver.fetch(message_id, '(RFC822)')
            raw_email = data[0][1]
            email_message = message_from_bytes(raw_email)
            
            ls_time = email_message.get('DATE').split(',')[-1].split(' ')
            while '' in ls_time: ls_time.remove('') 
            date = pd.to_datetime(' '.join(ls_time[0: 3])).date().strftime('%Y-%m-%d')

            subject = self.decode_info(email_message.get('SUBJECT'))
            from_addr = self.decode_info(email_message.get('FROM'), mailaddr = True)
            try:
                body = self.get_body(email_message)
            except:
                body = ''
            # 特殊处理
            if 'SEM863' in body:
                date += '_SEM863_阳泽泽丰另类量化1号私募投资基金'

            if 'SE7179' in body:
                date += '_SE7179_因诺天机私募证券投资基金'

            if 'SQE472' in subject:
                date += '_SFF004_SQE472_SSH955_玄元'
            
            if 'XS864B' in subject:
                date += '_XS864B_弘源多元化CTA进取型2号私募证券投资基金B'
            
            if 'SY1781' in subject:
                date += '_SY1781_涵德盈冲量化CTA1号私募证券投资基金'
            
            if 'SY1794' in subject:
                date += '_SY1794_涵德明德中证500指数增强1号'
            
            if 'SZW306' in subject:
                date += '_SZW306_超量子量化中性1号'
            
            if 'SXJ080' in subject:
                date += '_SXJ080_因诺CTA2号'
            
            # 遍历所有附件
            for part in email_message.walk():
                if part.get_content_maintype() == 'multipart':
                    continue
                if part.get('Content-Disposition') is None:
                    continue
                if not part.get_filename():
                    continue

                # 保存附件到本地
                filename = self.decode_info(part.get_filename()).lower()
                extension = filename.split('.')[-1]

                if filename:
                    if extension in ['xlsx', 'xls', 'zip']:
                        name = '_'.join([date, filename])
                        filepath = os.path.join(self.path_rawfolder, name)
                        with open(filepath, 'wb') as f:
                            f.write(part.get_payload(decode = True))

                        if extension == 'zip':
                            zf = zipfile.ZipFile(filepath)
                            zf.extractall(path = self.path_rawfolder)
                            zf.close()
                    # else:
                    #     print('[DOWNLOAD SKIP] {} FROM {}'.format(subject, from_addr))
        # 关闭连接
        emailserver.logout()

    def cal_rawnav(self):
        """
        提取原始基金净值
        """
        ls_filename = os.listdir(self.path_rawfolder)
        for name in ls_filename:
            filemt = time.localtime(os.stat(os.path.join(self.path_rawfolder, name)).st_mtime)
            if time.strftime('%Y%m%d', filemt) < time.strftime('%Y%m%d', time.localtime()):
                continue
            if name.split('.')[-1] == 'xls':
                pyexcel.save_book_as(file_name = os.path.join(self.path_rawfolder, name), dest_file_name = os.path.join(self.path_rawfolder, name + 'x'))

        ls_fundcode = self.df_formation['FUND_CODE'].tolist()
        ls_all = []
        for fcode in ls_fundcode:
            fform = self.df_formation.query("FUND_CODE == '{}'".format(fcode))['FUND_FILE'].values[0]
            ls_match = []
            for fname in ls_filename:
                if fnmatch(fname, fform):
                    ls_match.append(fname)
            if len(ls_match) > 0:
                fval_name = self.df_formation.query("FUND_CODE == '{}'".format(fcode))['FUND_NAME'].values[0]
                fref_code = self.df_formation.query("FUND_CODE == '{}'".format(fcode))['CODE'].values[0]
                fref_date = self.df_formation.query("FUND_CODE == '{}'".format(fcode))['DATE'].values[0]
                fref_nav = self.df_formation.query("FUND_CODE == '{}'".format(fcode))['NAV'].values[0]
                fref_cumnav = self.df_formation.query("FUND_CODE == '{}'".format(fcode))['CUMNAV'].values[0]
                ls_tmp = []
                for mfname in ls_match:
                    fval_fetchdate = pd.to_datetime(mfname.split('_')[0]).strftime('%Y%m%d')
                    wb = load_workbook(os.path.join(self.path_rawfolder, mfname))
                    ws = wb.active

                    fval_code = str(ws[fref_code].value)
                    fval_date = pd.to_datetime(str(ws[fref_date].value).replace('年', '').replace('月', '').replace('日', '')).strftime('%Y%m%d')
                    fval_nav = ws[fref_nav].value
                    fval_cumnav = np.nan if pd.isna(ws[fref_cumnav].value) else ws[fref_cumnav].value
                    
                    if fcode in fval_code:
                        df_tmp = pd.DataFrame({
                            'FUND_CODE': str(fcode),
                            'FUND_NAME': str(fval_name),
                            'TRADEDATE' : fval_date,
                            'FETCHDATE' : fval_fetchdate,
                            'FUND_UNITNAV': round(float(fval_nav.replace(',', '') if type(fval_nav) is str else fval_nav), 4),
                            'FUND_CUMNAV': round(float(fval_cumnav.replace(',', '') if type(fval_cumnav) is str else fval_cumnav), 4),
                            }, index = [0])
                        ls_tmp.append(df_tmp.copy())
                    else:
                        print(f'[ERROE] CODE {fcode} NOT MATCH CODE {fval_code} IN {mfname}.')
                    wb.close()
                
                if len(ls_tmp) > 0:
                    ls_all.append(pd.concat(ls_tmp, ignore_index = True).drop_duplicates().sort_values('TRADEDATE'))

        if len(ls_all) > 0:
            self.df_rawnav = pd.concat(ls_all, ignore_index = True).drop_duplicates().sort_values(['FUND_CODE', 'TRADEDATE'])\
                .merge(self.df_strategy.drop(columns = 'FUND_NAME'), on = 'FUND_CODE', how = 'inner')

    def cal_weeklymarket(self, df_rawnav):
        """
        计算基金周度数据
        """
        ls_tdate_w = get_tradedate(frq = 'W')
        ls_fundcode = df_rawnav['FUND_CODE'].unique().tolist()
        ls_fundnav = []
        for fcode in ls_fundcode:
            df_fundnav = df_rawnav[df_rawnav['FUND_CODE'] == fcode].sort_values('TRADEDATE').copy()
            df_fundnav['FUND_CUMDIV'] = df_fundnav['FUND_CUMNAV'] - df_fundnav['FUND_UNITNAV']
            df_fundnav['FUND_DIV'] = df_fundnav['FUND_CUMDIV'].diff().fillna(0).round(4)
            df_fundnav['FUND_RETURN'] = ((df_fundnav['FUND_CUMNAV'] - df_fundnav['FUND_CUMDIV'].shift(1)) / df_fundnav['FUND_UNITNAV'].shift(1) - 1).fillna(0)
            df_fundnav['FUND_CUMRETURN'] = (df_fundnav['FUND_RETURN'] + 1).cumprod() - 1
            df_fundnav['FUND_NAV'] = ((df_fundnav['FUND_CUMRETURN'] + 1) * df_fundnav['FUND_UNITNAV'].tolist()[0]).round(4)
            ls_fundnav.append(df_fundnav.copy())

        df_fundnav = pd.concat(ls_fundnav, ignore_index = True).drop(['FUND_CUMDIV', 'FUND_DIV', 'FUND_RETURN', 'FUND_CUMRETURN'], axis = 1).drop_duplicates()
        df_right = df_fundnav.pivot(index = 'TRADEDATE', columns = 'FUND_CODE', values = 'FUND_NAV').dropna(axis = 1, how = 'all')
        df_left = pd.DataFrame({'TRADEDATE': pd.date_range(start = df_right.index[0], end = df_right.index[-1]).strftime('%Y%m%d')})
        df_fundnav_us = df_left.merge(df_right, on = 'TRADEDATE', how = 'left').set_index('TRADEDATE')
        df_fundnav_us = df_fundnav_us.ffill() * (df_fundnav_us.bfill() * 0 + 1)
        df_fundnavw_us = df_fundnav_us[df_fundnav_us.index.isin(ls_tdate_w)].dropna(how = 'all')
        
        # 剔除发行初期连续净值为1的情况
        for i in range(10):
            df_fundnavw_us[(df_fundnavw_us == df_fundnavw_us.shift(-1)) & (df_fundnavw_us == 1) & (df_fundnavw_us.shift(1) * 0 != 0)] = None

        df_fundnavw = df_fundnavw_us.stack().to_frame().reset_index()
        df_fundretw = df_fundnavw_us.pct_change(fill_method = None).stack().to_frame().reset_index()
        df_fundnavw.columns = ['TRADEDATE', 'FUND_CODE', 'FUND_NAV']
        df_fundretw.columns = ['TRADEDATE', 'FUND_CODE', 'FUND_RETURN']
        df_fundnavw = df_fundnavw.merge(df_fundretw, on = ['TRADEDATE', 'FUND_CODE'], how = 'left')
        df_fundnavw = df_fundnavw.merge(self.df_strategy, on = 'FUND_CODE', how = 'inner').drop(columns = ['SOURCE'])

        df_fundnavw = df_fundnavw.sort_values(['FUND_CODE', 'TRADEDATE']).reset_index(drop = True)
        df_fundnavw['FUND_ADJNAV'] = df_fundnavw['FUND_RETURN'].fillna(0) + 1
        df_fundnavw['FUND_ADJNAV'] = df_fundnavw.groupby('FUND_CODE')['FUND_ADJNAV'].cumprod()

        # 计算基准净值序列
        df_benchmark = pd.read_sql('SELECT TRADEDATE, INDEX_CODE AS BENCHMARK_CODE, CLOSE AS BENCHMARK_NAV FROM INDEX_DAILYMARKET', self.conn)
        df_fundnavw = df_fundnavw.merge(df_benchmark, on = ['TRADEDATE', 'BENCHMARK_CODE'], how = 'left')
        df_right = df_fundnavw.sort_values('TRADEDATE').groupby('FUND_NAME').head(1)[['FUND_NAME', 'BENCHMARK_NAV']].rename({'BENCHMARK_NAV': 'BENCHMARK_START'}, axis = 1)
        df_fundnavw = df_fundnavw.merge(df_right, on = 'FUND_NAME', how = 'left')
        df_fundnavw['BENCHMARK_NAV'] /= df_fundnavw['BENCHMARK_START']
        df_fundnavw['BENCHMARK_NAV'].fillna(1, inplace = True)
        df_fundnavw.drop(['BENCHMARK_START'], axis = 1, inplace = True)

        # 计算基准收益率
        df_benchmark_us = df_fundnavw.pivot(index = 'TRADEDATE', columns = 'FUND_CODE', values = 'BENCHMARK_NAV').pct_change(fill_method = None)
        df_benchmark_ret = df_benchmark_us.stack().to_frame().reset_index()
        df_benchmark_ret.columns = ['TRADEDATE', 'FUND_CODE', 'BENCHMARK_RETURN']
        df_fundnavw = df_fundnavw.merge(df_benchmark_ret, on = ['TRADEDATE', 'FUND_CODE'], how = 'left')

        df_fundnavw['EXCESSIVE_RETURN'] = (df_fundnavw['FUND_RETURN'] + 1) / (df_fundnavw['BENCHMARK_RETURN'] + 1) - 1
        df_fundnavw['EXCESSIVE_NAV'] = df_fundnavw['FUND_ADJNAV'] / df_fundnavw['BENCHMARK_NAV']
    
        return df_fundnavw

    def clean_rawfolder(self):
        '''
        清理附件文件夹
        '''
        for root, dirs, files in os.walk(self.path_rawfolder):
            for name in files:
                os.remove(os.path.join(root, name))
    
    def run(self, reset = False):
        '''
        运行完整刷新流程
        '''
        dic_tables = {
        '300': 'tbly3qDPb0s6vLRT',
        '500': 'tblb85VKUsPzBooo',
        '1000': 'tbl3o0wLwa8qoahU',}
        # 'CTA': 'tblrTV6YOV05ExRe'
        # try:
        #     ls_data = []
        #     fsapi = FeishuData()
        #     for k in dic_tables.keys():
        #         df_tmp = fsapi.fetch(dic_tables[k])
        #         ls_data.append(df_tmp)
        #     df_fsdata = pd.concat(ls_data)[['产品名称', '日期', '累计单位净值', '单位净值']]
        #     df_fsdata.columns = ['FUND_NAME', 'TRADEDATE', 'FUND_CUMNAV', 'FUND_UNITNAV']
        #     df_fsdata['TRADEDATE'] = df_fsdata['TRADEDATE'].dt.strftime('%Y%m%d')
        #     df_fsdata['FETCHDATE'] = df_fsdata['TRADEDATE']
        #     df_fsdata[['FUND_CUMNAV', 'FUND_UNITNAV']] = df_fsdata[['FUND_CUMNAV', 'FUND_UNITNAV']].astype(float)
        #     df_fsdata['FUND_UNITNAV'].fillna(df_fsdata['FUND_CUMNAV'], inplace = True)
        #     df_fsdata['FUND_NAME'] = df_fsdata['FUND_NAME'].str.replace('私募证券投资基金', '')
        #     df_fsdata['FUND_NAME'] = df_fsdata['FUND_NAME'].str.replace('私募投资基金', '')
        #     df_fsdata = df_fsdata.merge(self.df_strategy.query('SOURCE == "FEISHU"'), on = 'FUND_NAME', how = 'inner')
        # except:
        #     raise SystemError('[PORTFOLIO DATA] Fetch data from FEISHU failed.')
        df_fsdata = pd.DataFrame()
        if reset:
            # 历史数据重置
            df_lastnav = pd.read_csv(os.path.join(self.path, 'KMAX_HIST.csv'))
            df_lastnav['TRADEDATE'] = df_lastnav['TRADEDATE'].astype(str)
            df_lastnav['FETCHDATE'] = df_lastnav['FETCHDATE'].astype(str)
            df_lastnav = df_lastnav.merge(self.df_strategy, on = ['FUND_CODE', 'FUND_NAME'], how = 'inner')
            lastdate = '20240809'
        else:
            try:
                df_lastnav = pd.read_sql('SELECT * FROM KMAX_RAWNAV', con = self.conn)
                if df_lastnav.empty:
                    lastdate = ''
                else:
                    lastdate = df_lastnav['FETCHDATE'].max()
            except:
                raise SystemError('[PORTFOLIO DATA] Read table KMAX_RAWNAV failed.')
        
        try:
            self.download_extention(lastdate = lastdate)
            self.cal_rawnav()
        except:
            raise SystemError('[PORTFOLIO DATA] Download rawnav failed.')

        try:
            df_rawnav = pd.concat([self.df_rawnav, df_fsdata, df_lastnav], ignore_index = True)
            df_rawnav['TRADEDATE'] = df_rawnav['TRADEDATE'].astype(str)
            df_rawnav['FETCHDATE'] = df_rawnav['FETCHDATE'].astype(str)
            df_rawnav = df_rawnav.sort_values(['FUND_CODE', 'TRADEDATE', 'FETCHDATE']).groupby(['FUND_CODE', 'TRADEDATE']).tail(1).drop_duplicates()
            df_fundnavw = self.cal_weeklymarket(df_rawnav)
        except:
            raise SystemError('[PORTFOLIO DATA] Calculate weekly market failed.')

        try:
            df_rawnav.to_sql('KMAX_RAWNAV', con = self.conn, index = False, if_exists = 'append', chunksize = 10000)
            df_fundnavw.to_sql('KMAX_WEEKLYMARKET', con = self.conn, index = False, if_exists = 'append', chunksize = 10000)
            raw_conn = self.conn.raw_connection()
            cursor = raw_conn.cursor()
            cursor.execute('OPTIMIZE TABLE PORTFOLIO_RAWNAV FINAL DEDUPLICATE')
            cursor.execute('OPTIMIZE TABLE KMAX_WEEKLYMARKET FINAL DEDUPLICATE')
            raw_conn.commit()
        except:
            raise SystemError('[PORTFOLIO DATA] Upload data to KMAX_RAWNAV and KMAX_WEEKLYMARKET failed.')

        self.clean_rawfolder()
        print('[PORTFOLIO DATA] Data update finished.')
        return

class FeishuData:
    def __init__(self):
        usr = 'cli_a623060258fd100d'
        pwd = '2us49TOHtDNEQxYhnAh3EhikfaP2WPUO'
        self.app_token = 'L6DvbBlW3aRt88sHiCOcOilSnIc'
        self.payload = json.dumps({'app_id': usr, 'app_secret': pwd})
        # config
        headers = {'Content-Type': 'application/json'}
        token_url = 'https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal'
        token_res = requests.request('POST', token_url, headers = headers, data = self.payload)
        self.token = json.loads(token_res.text)['tenant_access_token']
    @staticmethod
    def date_decode(timestamp):
        dt = pd.to_datetime(timestamp, unit='ms') + pd.Timedelta('8H')
        return dt
    def fetch(self, table_id):
        pages = []
        page_token = ''
        page_size = 500
        headers = {'Authorization': 'Bearer ' + str(self.token)}
        while True:
            url = f'https://open.feishu.cn/open-apis/bitable/v1/apps/{self.app_token}/' \
                  f'tables/{table_id}/records??page_size={page_size}&page_token={page_token}'
            res = requests.request('GET', url, headers = headers, data = self.payload)
            res = json.loads(res.text)
            df_list = []
            for itm in res['data']['items']:
                df_list.append(itm['fields'])
            pages.append(pd.DataFrame(df_list))
            # check has more
            has_more = res['data']['has_more']
            if not has_more:
                break
            page_token = res['data']['page_token']
        res_df = pd.concat(pages)
        res_df['日期'] = FeishuData.date_decode(res_df['日期'])
        return res_df

if __name__ == '__main__':
    pass