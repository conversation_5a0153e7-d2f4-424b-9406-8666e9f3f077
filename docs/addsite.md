# 不同二级域名绑定不同APP

在获取顶级域名后，希望通过不同的二级域名xxx.abc.def实现访问搭建在同一个服务器上的不同app。默认情况下，服务器上搭建了不同的app可以通过IP+端口实现访问，而且需要通过服务器的安全设置打开对应的端口权限，从操作便利性以及安全性考虑都不是很好的解决方案。借助宝塔以各二级域名分别建站并借助反向代理可以较好的实现这个需求。

1. 搭建宝塔：在服务器部署宝塔，并安装网站模块下的Nginx

   ![step_1](image/addsite/step_1.png)
2. 域名解析：在域名管理网站(如阿里云/腾讯云)平台，添加对应二级域名的解析，均解析到我们对应的服务器，本案例因为只有一个服务器，故解析至同一IP

   ![step_2](image/addsite/step_2.png)
3. 以二级域名建立站点：在宝塔平台的网站模块下，点击"添加站点"，并输入需要使用的二级域名，不需要额外修改端口，使用默认80即可，例如superset.vulpes.site。完成后可以在浏览器进行测试，输入刚刚新建的站点URL应该可以看到建站成功的默认index.html

   ![step_3](image/addsite/step_3.png)
4. 添加反向代理：回到宝塔页面，新建的站点右侧打开设置对话框，找到反向代理，并"添加反向代理"。代理名称随意，目标URL就是我们希望此二级域名可以解析到的目标位置，例如我们的APP搭建在本地服务器的90端口，那么我们的目标URL就是http://127.0.0.1:90，而发送域名保持默认的$host即可。也就是说本二级域名将被直接代理至本地的90端口。同时我们也不需要在服务的安全设置上打开90端口，仅通过二级域名的80端口就可以访问目标APP

   ![step_4](image/addsite/step_4.png)
