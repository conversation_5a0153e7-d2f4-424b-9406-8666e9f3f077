# -*- coding: utf-8 -*-
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))

from src.basic import *
from src.barra import Barra

from wework.wework_msg import *

import pandas as pd
import argparse
from datetime import date, datetime
from tqdm import tqdm

def db_barra_update(uptodate, conn_to):
    """
    Barra数据更新

    Parameters
    ----------
    uptodate : str - 'yyyymmdd' 数据库更新截至时点
    conn_to : dbconnector - 目标数据库链接

    Returns
    -------
    """
    enddate = get_tradedate(enddate = uptodate)[-1]

    # Set timerange
    try:
        startdate = pd.read_sql('SELECT MAX(TRADEDATE) AS TRADEDATE FROM BARRA_DAILYRETURN', con = conn_to)['TRADEDATE'].values[0]
    except:
        sendmsg_text(content = '[BARRA]: Table [BARRA_DAILYRETURN] connect failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
        raise SystemError('[BARRA]: Table connect failed.')
    
    if not startdate:
        startdate = '20141231'

    if enddate > startdate:
        ls_trange = get_tradedate(startdate = startdate, enddate = enddate)
        df_mktret = get_indexmkt()
        df_stkqual = get_stkqual()
        df_stkind = get_stkindustry()


        for i in tqdm(range(len(ls_trange) // 20 + 1), desc = '[BARRA]'):
            if i * 20 == len(ls_trange):
                continue
            
            # Need one more day to calculate factor return in the loop
            if i > 0: 
                range_start = ls_trange[i * 20 - 1]
            else:
                range_start = ls_trange[i * 20]
            
            if i == len(ls_trange) // 20:
                range_end = ls_trange[-1]
            else:
                range_end = ls_trange[(i + 1) * 20 - 1]

            try:
                cal_barra = Barra(startdate = range_start, enddate = range_end, conn_to = conn_to, df_mktret = df_mktret, df_stkqual = df_stkqual, df_stkind = df_stkind)
            except:
                sendmsg_text(content = '[BARRA]: Initialization and data download failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
                raise SystemError('[BARRA]: Initialization and data download failed.')
        
            try:
                df_factorload_L1, df_factorload_L2 = cal_barra.cal_factorload()

                if range_start != '20141231':
                    df_factorload_L1 = df_factorload_L1[df_factorload_L1['TRADEDATE'] > range_start]
                df_factorload_L1.to_sql(name = 'BARRA_FACTORLOAD_L1', con = conn_to, index = False, if_exists = 'append', chunksize = 10000)
                del df_factorload_L1

                if range_start != '20141231':
                    df_factorload_L2 = df_factorload_L2[df_factorload_L2['TRADEDATE'] > range_start]
                df_factorload_L2.to_sql(name = 'BARRA_FACTORLOAD_L2', con = conn_to, index = False, if_exists = 'append', chunksize = 10000)
                del df_factorload_L2
            except:
                sendmsg_text(content = '[BARRA]: Factor load calculation failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
                raise SystemError('[BARRA]: Factor load calculation failed.')
            
            try:
                df_factor_return = cal_barra.cal_factorreturn(model = 'CNE5')
                df_factor_return = df_factor_return[df_factor_return['TRADEDATE'] > range_start]
                df_factor_return.to_sql(name = 'BARRA_DAILYRETURN', con = conn_to, index = False, if_exists = 'append', chunksize = 10000)
            except:
                sendmsg_text(content = '[BARRA]: CNE5 Factor return calculation failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
                raise SystemError('[BARRA]: CNE5 Factor return calculation failed.')
            
            try:
                df_factor_return = cal_barra.cal_factorreturn(model = 'CNE6')
                df_factor_return = df_factor_return[df_factor_return['TRADEDATE'] > range_start]
                df_factor_return.to_sql(name = 'BARRA_DAILYRETURN', con = conn_to, index = False, if_exists = 'append', chunksize = 10000)
            except:
                sendmsg_text(content = '[BARRA]: CNE6 Factor return calculation failed.\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
                raise SystemError('[BARRA]: CNE6 Factor return calculation failed.')
            
            # Delete the object to release memory
            del cal_barra

        if df_factor_return.shape[0] > 0:
            newdate = df_factor_return['TRADEDATE'].max()
            print('[BARRA]: Update to {}.'.format(newdate))
        else:
            print('[BARRA]: No data for calculation, check source.')
    else:
        print('[BARRA]: No data to update.')

    return

if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('-day', type = int, default = 1, help = 'Update to N days before today.')
    args = parser.parse_args()
    uptodate = (date.today() - pd.Timedelta('{}D'.format(args.day))).strftime('%Y%m%d')
    conn_vulpes = db_vulpes(writer = True)

    try:
        db_barra_update(uptodate, conn_vulpes)
        print('[BARRA]: Update Finished.')
        # sendmsg_text(content = '【通知】Barra数据更新成功\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
    except:
        sendmsg_text(content = '【警告】Barra数据更新失败\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
        raise SystemError('[BARRA]: Update Failed.')