#!/usr/bin/env python
# -*- encoding:utf-8 -*-
 
import requests
import json
import datetime
import io
import os

def get_access_token() -> str:
    '''
    获取access_token凭证
    '''
    # 企业ID
    corp_id = 'ww703b0c00ff6ce26e'
    # 应用密钥（Secret）
    corp_secret = 'QjkRwn3Ziov4kuWK4gTNcI-K7uIZuOT_9Mxh1yJA2Z8'
    file_path = '/root/zerda/wework/'
    try:
        with open(file_path + 'access_token', 'r') as f:
            ls_info = f.read().split(';')
            time_expire = datetime.datetime.strptime(ls_info[1], '%Y%m%d%H%M%S')
            if time_expire > datetime.datetime.now():
                return ls_info[0]
    except:
        pass

    url = 'https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={}&corpsecret={}'.format(corp_id, corp_secret)
    response = requests.get(url)
    result = response.json()
    if result['errcode'] == 0:
        with open(file_path + 'access_token', 'w') as f:
            f.write(';'.join([result['access_token'], (datetime.datetime.now() + datetime.timedelta(seconds = result['expires_in'])).strftime('%Y%m%d%H%M%S')]))
        return result['access_token']
    else:
        print(result)


def get_media_id(file_path: str, file_name: str, file_type: str = 'file') -> str:
    '''
    上传临时素材, 获取media_id, 该media_id仅三天内有效
    
    Parameters
    ----------
    file_path : 上传文件的绝对地址
    file_name : 上传文件的显示名称, 含后缀
    file_type : 上传文件类型, 分别有图片(image), 语音(voice), 视频(video), 普通文件(file)
    '''
    # 请求头
    access_token = get_access_token()
    file_data = open(file_path, 'rb').read()
    url = 'https://qyapi.weixin.qq.com/cgi-bin/media/upload?access_token={}&type={}'.format(access_token, file_type)
    headers = {
        'Content-Type': 'multipart/form-data; boundary=---------------------------' + os.urandom(16).hex(),
    }

    # 请求体
    body = io.BytesIO()
    boundary = headers['Content-Type'].split('boundary=')[1]

    # 表单数据部分
    body.write(bytes('--' + boundary + '\r\n', 'utf-8'))
    body.write(bytes('Content-Disposition: form-data; name="media";filename="' + file_name + '";filelength=' + str(len(file_data)) + '\r\n', 'utf-8'))
    body.write(b'\r\n')

   # 文件数据部分
    body.write(file_data)

    # 文件结束标志
    body.write(bytes('\r\n--' + boundary + '--\r\n', 'utf-8'))

    # 发送请求
    response = requests.post(url, headers = headers, data = body.getvalue())
    result = response.json()
    if result['errcode'] == 0:
        return result['media_id']
    else:
        print(result)

def sendmsg_text(content: str, userid: str = '', partyid: str = '', agentid: int = 1000002):
    '''
    发送文本消息到小组

    Parameters
    ----------
    content : 消息内容
    userid : 个人ID
    partyid : 小组ID
    agentid : 应用的agentid
    '''
    token = get_access_token()
    url = 'https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token={}'.format(token)

    json_data = json.dumps({
        "touser" : userid,
        "toparty" : partyid,
        "totag" : "",
        "msgtype" : "text",
        "agentid" : agentid,
        "text" : {
            "content" : content
        },
        "safe":0,
        "enable_id_trans": 0,
        "enable_duplicate_check": 0,
        "duplicate_check_interval": 1800})

    resp = requests.post(url, data = json_data)
    result = resp.json()

    if result['errcode'] == 0:
        print('消息推送成功')
    else:
        print(result)
    return result['errmsg']

def sendmsg_file(media_id: str, userid: str = '', partyid: str = '', file_type: str = 'file', agentid: int = 1000002):
    '''
    发送文件消息到小组

    Parameters
    ----------
    media_id : 消息内容
    userid : 个人ID
    partyid : 小组ID
    filetype : 文件的类型, file, image, voice
    agentid : 应用的agentid
    '''
    token = get_access_token()
    url = 'https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token={}'.format(token)

    json_data = json.dumps({
        "touser" : userid,
        "toparty" : partyid,
        "totag" : "",
        "msgtype" : file_type,
        "agentid" : agentid,
        file_type : {
            "media_id" : media_id
        },
        "safe":0,
        "enable_id_trans": 0,
        "enable_duplicate_check": 0,
        "duplicate_check_interval": 1800})

    resp = requests.post(url, data = json_data)
    result = resp.json()

    if result['errcode'] == 0:
        print('文件推送成功')
    else:
        print(result)
    return result['errmsg']

def sendmsg_markdown(content: str, userid: str = '', partyid: str = '', agentid: int = 1000002):
    '''
    发送MD消息到小组

    Parameters
    ----------
    content : 消息内容
    userid : 个人ID
    partyid : 小组ID
    agentid : 应用的agentid
    '''
    token = get_access_token()
    url = 'https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token={}'.format(token)

    json_data = json.dumps({
        "touser" : userid,
        "toparty" : partyid,
        "totag" : "",
        "msgtype" : "markdown",
        "agentid" : agentid,
        "markdown" : {
            "content" : content
        },
        "safe":0,
        "enable_id_trans": 0,
        "enable_duplicate_check": 0,
        "duplicate_check_interval": 1800})

    resp = requests.post(url, data = json_data)
    result = resp.json()

    if result['errcode'] == 0:
        print('消息推送成功')
    else:
        print(result)
    return result['errmsg']

if __name__ == '__main__':
    pass