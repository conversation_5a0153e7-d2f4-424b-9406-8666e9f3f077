# -*- coding: utf-8 -*-
# 下载私募基金净值序列
import pandas as pd

def download_fundnav(startdate, df_funds, conn_smpp):
    """
    下载私募基金净值序列

    Parameters
    ----------
    startdate : str - 'yyyymmdd' 时间起点
    df_funds : DataFrame - 产品名称, 产品策略
    conn_smpp : smpp数据库连接

    Returns
    -------
    DataFrame

    """
    ls_funds = df_funds['FUND_NAME'].tolist()

    try:
        ls_subnav = []
        for i in range(0, len(ls_funds) // 800 + 1):
            ls_sub = ls_funds[i * 800: (i + 1) * 800] + ['']
            sql = '''
            SELECT 
                A.price_date AS TRADEDATE,
                B.fund_short_name AS FUND_NAME,
                A.cumulative_nav AS FUND_NAV,
                C.company_short_name AS COMPANY_NAME,
                C.company_asset_size AS COMPANY_SIZE
            FROM PVN_NAV A
            LEFT JOIN PVN_FUND_INFO B
            ON A.fund_id = B.fund_id
            LEFT JOIN PVN_COMPANY_INFO C
            ON B.issuer_id = C.company_id
            WHERE 
                B.fund_short_name IN {} AND
                A.updatetime >= to_date('{}', 'YYYYMMDD')
            '''.format(tuple(ls_sub), startdate)
            ls_subnav.append(pd.read_sql(sql, con = conn_smpp).drop_duplicates().dropna().copy())
        df_nav = pd.concat(ls_subnav, ignore_index = True).drop_duplicates().dropna()
    except:
        raise Exception('[DOWNLOAD FAILED]')

    if df_nav.shape[0] < 1:
        ls_unavailable = ls_funds
        print('[NO DATA FUND IN SMPP DB]')
        df_nav_all = pd.DataFrame(columns = ['TRADEDATE', 'FUND_NAME', 'FUND_NAV', 'COMPANY_NAME', 'COMPANY_SIZE', 'STRATEGY', 'BENCHMARK_CODE'])
    else:
        df_nav.columns = ['TRADEDATE', 'FUND_NAME', 'FUND_NAV', 'COMPANY_NAME', 'COMPANY_SIZE']
        df_nav['TRADEDATE'] = pd.to_datetime(df_nav['TRADEDATE']).dt.strftime('%Y%m%d')
        ls_get = df_nav['FUND_NAME'].unique().tolist()
        ls_unavailable = [i for i in ls_funds if i not in ls_get]
        df_nav_all = df_nav.merge(df_funds, on = 'FUND_NAME', how = 'left')
        df_nav_all['COMPANY_SIZE'].replace({
            1: '0-5亿',
            2: '5-10亿',
            3: '10-20亿',
            4: '20-50亿',
            5: '50-100亿',
            6: '100亿以上'}, inplace = True)
        df_nav_all['BENCHMARK_CODE'] = ''
        df_nav_all.loc[df_nav_all['STRATEGY'] == '300指增', 'BENCHMARK_CODE'] = '000300.SH'
        df_nav_all.loc[df_nav_all['STRATEGY'] == '500指增', 'BENCHMARK_CODE'] = '000905.SH'
        df_nav_all.loc[df_nav_all['STRATEGY'] == '1000指增', 'BENCHMARK_CODE'] = '000852.SH'
        df_nav_all.loc[df_nav_all['STRATEGY'] == '量化选股', 'BENCHMARK_CODE'] = '000906.SH'

    # if len(ls_unavailable) > 0:
    #     print('[UNAVAILABLE FUND]: ', ls_unavailable)

    return df_nav_all

if __name__ == '__main__':
    pass