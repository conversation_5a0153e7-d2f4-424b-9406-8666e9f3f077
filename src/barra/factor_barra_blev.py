# -*- coding: utf-8 -*-
from barra import mod_makeupann

def factor_barra_blev(df_yreport, ls_tdate):
    """
    计算BLEV因子

    Computed as, BLEV = (BE + PE + LD) / BE, where BE, PE, and LD are the book value of 
    common equity, preferred equity, and long-term debt, respectively, from the last fiscal year.
    
    Parameters
    ----------
        df_yreport : DataFrame - 年度财务指标表数据
        ls_tdate : List [str] - 交易日序列

    Returns
    -------
    DataFrame
    
    """
    df_yreport['BLEV'] = (df_yreport['TE'] + df_yreport['TLL'].fillna(0)) / (df_yreport['TE'] - df_yreport['PS'].fillna(0))
    df_BLEV = df_yreport.pivot(index = 'REPORT_DATE', columns = 'STOCK_CODE', values = 'BLEV')
    df_ANN = df_yreport.pivot(index = 'REPORT_DATE', columns = 'STOCK_CODE', values = 'DISCLOSE_DATE')

    df_output = mod_makeupann(df_BLEV, df_ANN, ls_tdate).stack().reset_index()
    df_output.columns = ['TRADEDATE', 'STOCK_CODE', 'VALUE']

    df_output['FACTOR'] = 'BLEV'

    return df_output

if __name__ == '__main__':
    pass