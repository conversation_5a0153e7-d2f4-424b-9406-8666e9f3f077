# -*- coding: utf-8 -*-
# 读取清洗股票型基金四级估值表

import pandas as pd
import numpy as np

def cal_stkfundrawhold(filepath: str, paneldate: str = ''):
    """
    读取清洗股票型基金四级估值表

    Parameters
    ----------
    filepath : str - 估值表文件路径
    paneldate : str - 估值表日期

    Returns
    -------
    DataFrame
    """
    try:
        df_file = pd.read_excel(filepath).dropna(how = 'all', axis = 1).dropna(how = 'all', axis = 0)
        ls_cells = [i.replace(' ', '') for i in df_file.columns.astype(str).tolist() + df_file.fillna('').astype(str).values.reshape(-1).tolist()]
        ls_cells = [i.replace('：', ':') for i in ls_cells]
        df_file.set_index(df_file.columns[ls_cells.index('科目代码') % df_file.shape[1]], inplace = True)
    except:
        raise Exception('[ERROR]: 无法读取估值表文件, 请检查文件格式')

    if paneldate == '':
        try:
            if '日期:' in ls_cells:
                paneldate = pd.to_datetime(ls_cells[ls_cells.index('日期:') + 1]).strftime('%Y%m%d')
            elif '估值日期:' in ls_cells:
                paneldate = pd.to_datetime(ls_cells[ls_cells.index('估值日期:') + 1]).strftime('%Y%m%d')
            else:
                paneldate = pd.to_datetime([i.split(':')[-1] for i in ls_cells if '日期' in i][0]).strftime('%Y%m%d')
        except:
            raise Exception('[WARNING]: 无法获取估值表日期, 请手动录入')
    
    try:
        ls_cells = [i.replace(' ', '') for i in df_file.reset_index().columns.astype(str).tolist() + df_file.reset_index().fillna('').astype(str).values.reshape(-1).tolist()]
        ls_columns = ls_cells[ls_cells.index('科目代码') + 1: ls_cells.index('科目代码') + df_file.shape[1] + 1]
        ls_columns = [i.replace(' ', '').split('-')[0] for i in ls_columns]
        ls_columns = [i.replace('(', '').replace(')', '') for i in ls_columns]
        df_file.columns = ls_columns
        df_file.index.name = '代码'

        ls_towipe = ['.', ' ', 'SH', 'SZ', 'BJ', 'CFX']
        for tw in ls_towipe:
            df_file.index = df_file.index.str.replace(tw, '')
        
        df_rawhold_stk = df_file.loc[(df_file.index.str[0:4].isin(['1102', '2101'])) & (~df_file['停牌信息'].isna())].copy()
        df_rawhold_ffut = df_file.loc[(df_file.index.str[0:4].isin(['3102'])) & (df_file.index.str.contains('|'.join(['IH', 'IF', 'IC', 'IM']))) & (~df_file['停牌信息'].isna())].copy()
        
        df_rawhold = pd.concat([df_rawhold_stk, df_rawhold_ffut])
        df_rawhold = df_rawhold.T.drop_duplicates().T

        if '行情' in df_file.columns:
            df_rawhold = df_rawhold[['科目名称', '数量', '单位成本', '成本', '行情', '市值', '市值占比', '停牌信息']].reset_index()
        else:
            df_rawhold = df_rawhold[['科目名称', '数量', '单位成本', '成本', '市价', '市值', '市值占净值%', '停牌信息']].reset_index()
            df_rawhold['市值占净值%'] = df_rawhold['市值占净值%'].astype(float)
            df_rawhold['市值占净值%'] /= 100

        df_rawhold = df_rawhold.dropna().T.drop_duplicates().T
        df_rawhold.columns = ['代码', '名称', '数量', '单位成本', '成本', '单位市价', '市值', '市值占比', '停牌信息']
        df_rawhold['名称'] = df_rawhold['名称'].str.replace(' ', '')
        df_rawhold['名称'] = df_rawhold['名称'].str.split('.', expand = True).values[:, -1]

        for col in ['数量', '单位成本', '成本', '单位市价', '市值', '市值占比']:
            df_rawhold[col] = df_rawhold[col].astype(str).str.replace(',', '')
            df_rawhold[col] = df_rawhold[col].astype(float)

        df_rawhold['资产类型'] = ''
        df_rawhold.loc[df_rawhold['代码'].str[0:4].isin(['1102', '2101']), '资产类型'] = '股票'
        df_rawhold.loc[df_rawhold['代码'].str[0:4].isin(['3102']), '资产类型'] = '股指期货'
        
        df_rawhold['日期'] = paneldate
        df_rawhold['多空方向'] = np.sign(df_rawhold['市值'])
        df_rawhold.loc[df_rawhold['代码'].str[0:4] == '2101', '多空方向'] = -1
        df_rawhold['代码'] = df_rawhold['代码'].str[-6:]
        df_rawhold.loc[df_rawhold['资产类型'] == '股指期货', '名称'] = df_rawhold.loc[df_rawhold['资产类型'] == '股指期货', '代码']
        df_rawhold['名称'] = [i[-1] for i in df_rawhold['名称'].str.split('-')]
        df_rawhold['名称'] = df_rawhold['名称'].str.replace(' ', '')
        df_rawhold['停牌信息'] = df_rawhold['停牌信息'].str.replace('【', '')
        df_rawhold['停牌信息'] = df_rawhold['停牌信息'].str.replace('】', '')
        df_rawhold.loc[df_rawhold['代码'].str[0].isin(['6']), '代码'] += '.SH'
        df_rawhold.loc[df_rawhold['代码'].str[0].isin(['0', '3']), '代码'] += '.SZ'
        df_rawhold.loc[df_rawhold['代码'].str[0].isin(['4', '8', '9']), '代码'] += '.BJ'
        df_rawhold['单位涨幅'] = df_rawhold['单位市价'] / df_rawhold['单位成本'] - 1

        df_rawhold_stk = df_rawhold[df_rawhold['资产类型'] == '股票'].copy()
        df_rawhold_stk['持股权重'] = df_rawhold_stk['多空方向'] * df_rawhold_stk['市值占比'] / df_rawhold_stk[df_rawhold_stk['多空方向'] > 0]['市值占比'].sum()
        df_rawhold_stk['持仓盈亏'] = (df_rawhold_stk['市值'] - df_rawhold_stk['成本']) * df_rawhold_stk['多空方向']

        df_rawhold_ffut = df_rawhold[df_rawhold['资产类型'] == '股指期货'].copy()
        df_rawhold_ffut['持仓盈亏'] = df_rawhold_ffut['市值'] - df_rawhold_ffut['成本']
        if df_rawhold_stk[df_rawhold_stk['多空方向'] > 0].shape[0] > 0:
            df_rawhold_ffut['持股权重'] = df_rawhold_ffut['市值占比'] / df_rawhold_stk[df_rawhold_stk['多空方向'] > 0]['市值占比'].sum()
        else:
            df_rawhold_ffut['持股权重'] = 0
        
        df_rawhold = pd.concat([df_rawhold_stk, df_rawhold_ffut]).reset_index(drop = True)

    except:
        raise Exception('[ERROR]: 无法解析估值表文件, 请检查文件格式')
    
    if df_rawhold.shape[0] == 0:
        raise Exception('[ERROR]: 估值表文件中未找到有效持仓信息')
    
    return df_rawhold

if __name__ == '__main__':
    pass
