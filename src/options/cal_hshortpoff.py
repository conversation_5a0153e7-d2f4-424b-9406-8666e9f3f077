# -*- coding: utf-8 -*-
# 计算卖权到期payoff

import pandas as pd
import numpy as np
from basic.db_conn import db_vulpes
from basic import get_tradedate

def cal_hshortpoff(code: str, enddate: str, n_unit: int = 5, wl: int = 750, hl: int = 250):
    '''
    计算卖权到期payoff

    Parameters
    ----------
    code : int - 标的资产代码
    enddate : 测算截至日期
    n_unit : list - 测算触发实值的档位数量
    wl : int - 历史数据回顾区间
    hl : int - 指数加权半衰期
    
    Returns
    -------
    DataFrame
    '''

    conn = db_vulpes()
    sql = '''
        SELECT 
        TRADEDATE, EXCHANGE, FLAG, STRIKE_PRICE, SETTLE, TMONTH, DLISTDATE
        FROM FOPTION_DAILYMARKET
        WHERE 
        TARGET_CODE = '{}' AND 
        TRADEDATE = (SELECT TRADEDATE 
        FROM FOPTION_DAILYMARKET
        WHERE TARGET_CODE = '{}' 
        AND TRADEDATE <= '{}'
        ORDER BY TRADEDATE DESC
        LIMIT 1)
    '''.format(code, code, enddate)
    df_strick = pd.read_sql(sql, con = conn).drop_duplicates()
    exchange = df_strick['EXCHANGE'].values[0]
    df_date = df_strick.sort_values('TMONTH')[['TMONTH', 'DLISTDATE']].drop_duplicates()
    ls_tdate = get_tradedate()
    df_date['DAYLEFT'] = [ls_tdate.index(i) - ls_tdate.index(df_strick['TRADEDATE'][0]) for i in df_date['DLISTDATE']]

    if exchange == 'CFFEX':
        sql = '''
        SELECT
            INDEX_CODE AS CODE,
            TRADEDATE,
            CLOSE,
            RETURN
        FROM INDEX_DAILYMARKET
        WHERE INDEX_CODE = '{}' AND
        TRADEDATE <= '{}'
        ORDER BY TRADEDATE ASC
        '''.format(code, enddate)
        df_mkt = pd.read_sql(sql, con = conn)
    else:
        sql = '''
        SELECT
            FUND_CODE AS CODE,
            TRADEDATE,
            CLOSE,
            RETURN
        FROM FOPTION_ETFMARKET
        WHERE FUND_CODE = '{}' AND
        TRADEDATE <= '{}'
        ORDER BY TRADEDATE ASC
        '''.format(code, enddate)
        df_mkt = pd.read_sql(sql, con = conn)

    price = df_mkt['CLOSE'].values[-1]
    df_tick_c = df_strick[(df_strick['STRIKE_PRICE'] >= price) & (df_strick['FLAG'] == 'CALL')].pivot(index = 'STRIKE_PRICE', columns = 'TMONTH', values = 'SETTLE').sort_index()
    df_tick_p = df_strick[(df_strick['STRIKE_PRICE'] <= price) & (df_strick['FLAG'] == 'PUT')].pivot(index = 'STRIKE_PRICE', columns = 'TMONTH', values = 'SETTLE').sort_index(ascending = False)

    if exchange != 'CFFEX':
        df_tick_c.dropna(inplace = True)
        df_tick_p.dropna(inplace = True)

    ls_dit_c = df_tick_c.index.tolist()[0: n_unit]
    ls_dit_p = df_tick_p.index.tolist()[0: n_unit]
    ls_pct_c = [i / price - 1 for i in ls_dit_c]
    ls_pct_p = [1 - i / price for i in ls_dit_p]

    a = 0.5 ** (1 / hl)
    eweight = [(1 - a) * (a ** i) for i in np.arange(wl, 0, -1) - 1]
    eweight /= sum(eweight)

    df_settle = df_strick[['FLAG', 'STRIKE_PRICE', 'SETTLE', 'TMONTH']].copy()
    df_settle.columns = ['合约类型', '触发价格', '期权价格', '合约月份']
    df_settle = df_settle.pivot(index = ['合约类型', '触发价格'], columns = '合约月份', values = '期权价格').reset_index()

    ls_output = []
    for i in range(df_date.shape[0]):
        n_left = df_date.iloc[i]['DAYLEFT'] + 1
        tmonth = df_date.iloc[i]['TMONTH']

        df_absvol = (np.exp(np.log(df_mkt[['RETURN']] + 1).rolling(n_left).sum()) - 1).abs().tail(wl)

        ls_trg_c = [((df_absvol['RETURN'] >= i).T.multiply(eweight).T).sum() for i in ls_pct_c]
        ls_trg_p = [((df_absvol['RETURN'] >= i).T.multiply(eweight).T).sum() for i in ls_pct_p]
        ls_epf_c = [-(((df_absvol['RETURN'] - i) * price).clip(lower = 0).T.multiply(eweight).T).sum() for i in ls_pct_c]
        ls_epf_p = [-(((df_absvol['RETURN'] - i) * price).clip(lower = 0).T.multiply(eweight).T).sum() for i in ls_pct_p]

        df_call = pd.DataFrame({'触发档位': range(1, len(ls_dit_c) + 1), '合约类型': 'CALL', '触发价格': ls_dit_c, '触发概率': ls_trg_c, '期望损失': ls_epf_c}).sort_values('触发档位', ascending = False)
        df_put = pd.DataFrame({'触发档位': range(1, len(ls_dit_p) + 1), '合约类型': 'PUT', '触发价格': ls_dit_p, '触发概率': ls_trg_p, '期望损失': ls_epf_p}).sort_values('触发档位', ascending = True)
        df_mid = pd.DataFrame({'触发档位': [0], '合约类型': 'PRICE', '触发价格': [price], '触发概率': np.nan, '期望损失': np.nan})
        df_output = pd.concat([df_call, df_mid, df_put], ignore_index = True)

        df_output = df_output.merge(df_settle[['合约类型', '触发价格', tmonth]], on = ['合约类型', '触发价格'], how = 'left').rename({tmonth: '期权费'}, axis = 1)
        df_output['总损益'] = df_output['期权费'].multiply(1 - df_output['触发概率'], axis = 0).add(df_output['期望损失'], axis = 0)
        df_output.set_index('触发档位', inplace = True)
        df_output.columns.name = tmonth
        ls_output.append(df_output.copy())

    return ls_output

if __name__ == '__main__':
    pass