# -*- coding: utf-8 -*-
# 下载宽基指数成分股权重

import pandas as pd
import time
from basic import get_tradedate

def download_index_consweight(startdate, enddate, conn_ts):
    """
    下载宽基指数成分股权重

    Parameters
    ----------
    startdate : str - 'yyyymmdd' 时间窗口起点
    enddate : str - 'yyyymmdd' 时间窗口终点
    conn_ts : tushare数据接口

    Returns
    -------
    DataFrame
    """
    if startdate == '':
        initdate = '20050101'
    else:
        initdate = startdate

    ls_mdate = get_tradedate(initdate, enddate, frq = 'M')
    ls_indexcode = ['000016.SH', '399300.SZ', '000905.SH', '000906.SH', '000852.SH', '932000.CSI', '000985.CSI', '000510.CSI']
    ls_weight = []

    for mdate in ls_mdate:
        for code in ls_indexcode:
            try:
                ls_weight.append(conn_ts.index_weight(index_code = code, trade_date = mdate).copy())
                time.sleep(0.12)
            except:
                raise Exception('[Tushare]: Index member weight data download failed.')
    ls_weight = [i for i in ls_weight if i.shape[0] > 0]

    if len(ls_weight) > 0:
        df_weight = pd.concat(ls_weight, ignore_index = True).drop_duplicates()
        df_weight.columns = [i.upper() for i in df_weight.columns]
        df_weight.rename({'CON_CODE': 'STOCK_CODE',
                        'TRADE_DATE': 'TRADEDATE'}, axis = 1, inplace = True)

        df_weight['INDEX_CODE'] = df_weight['INDEX_CODE'].str.replace('399300.SZ', '000300.SH', regex = False)
        df_weight['WEIGHT'] /= 100
    else:
        df_weight = pd.DataFrame(columns = ['INDEX_CODE', 'STOCK_CODE', 'TRADEDATE', 'WEIGHT'])
        
    return df_weight

if __name__ == '__main__':
    pass