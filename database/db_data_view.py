# -*- coding: utf-8 -*-
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))

from src.basic import *
from src.data import *
from wework.wework_msg import *

from datetime import datetime

def create_view(conn_to):
    raw_conn = conn_to.raw_connection()
    cursor = raw_conn.cursor()

    try:
        sql = '''
        CREATE VIEW IF NOT EXISTS VIEW_STOCKPERFORMANCE AS(
        SELECT 
            B.TRADEDATE,
            A.*,
            CASE WHEN B.RETURN IS NULL THEN 0 ELSE B.RETURN END AS RETURN
        FROM (
            SELECT
                STOCK_CODE,
                SUM(CASE WHEN INDEX_CODE = '000300.SH' THEN 1 ELSE 0 END) A300,
                SUM(CASE WHEN INDEX_CODE = '000905.SH' THEN 1 ELSE 0 END) A500,
                SUM(CASE WHEN INDEX_CODE = '000852.SH' THEN 1 ELSE 0 END) A1000,
                SUM(CASE WHEN INDEX_CODE = '932000.CSI' THEN 1 ELSE 0 END) A2000,
                SUM(CASE WHEN INDEX_CODE = '000985.CSI' THEN 1 ELSE 0 END) ACHN
            FROM INDEX_CONSWEIGHT 
            WHERE 
                TRADEDATE = (SELECT MAX(TRADEDATE) FROM INDEX_CONSWEIGHT) AND
                STOCK_CODE NOT LIKE '%.BJ'
            GROUP BY STOCK_CODE
            ) A
        LEFT JOIN (
            SELECT 
                STOCK_CODE, TRADEDATE, `RETURN` 
            FROM STOCK_DAILYMARKET
            WHERE 
                TRADEDATE = (SELECT MAX(TRADEDATE) FROM STOCK_DAILYMARKET)
            ) B
        ON 
            A.STOCK_CODE = B.STOCK_CODE
        WHERE 
            A.ACHN > 0
        );
        '''
        cursor.execute(sql)
        print('[VIEW_STOCKPNL]: View created.')
    except:
        print('[VIEW_STOCKPNL]: Create failed.')

    raw_conn.commit()

    return

if __name__ == '__main__':
    
    conn_vulpes = db_vulpes(writer = True)
    
    try:
        create_view(conn_to = conn_vulpes)
        print('[VIEW]: Update Finished.')
        # sendmsg_text(content = '【通知】视图数据更新成功\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
    except:
        sendmsg_text(content = '【警告】视图数据更新失败\n[{}]'.format(datetime.now().strftime('%Y-%m-%d %H:%M:%S')), userid = 'Vulpes')
        raise SystemError('[VIEW]: Update Failed.')
