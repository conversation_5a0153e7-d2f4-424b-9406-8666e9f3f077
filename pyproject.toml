[project]
name = "zerda"
version = "0.1.0"
description = "zerda project"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "clickhouse-sqlalchemy>=0.3.2",
    "crypto>=1.4.1",
    "cvxpy>=1.6.4",
    "dataframe-image>=0.2.7",
    "flask>=3.1.0",
    "matplotlib>=3.10.1",
    "notebook>=7.3.3",
    "numpy>=2.2.4",
    "openpyxl>=3.1.5",
    "pandas>=2.2.3",
    "pdfplumber>=0.11.6",
    "pycryptodome>=3.22.0",
    "pyexcel>=0.7.2",
    "pyexcel-xls>=0.7.1",
    "pyexcel-xlsx>=0.6.1",
    "sqlalchemy>=2.0.39",
    "streamlit>=1.44.1",
    "streamlit-authenticator>=0.4.2",
    "tqdm>=4.67.1",
    "tushare>=1.4.19",
    "websocket>=0.2.1",
    "websockets>=15.0.1",
]

[[tool.uv.index]]
url = "https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple"
default = true
