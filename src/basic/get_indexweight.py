# -*- coding: utf-8 -*-
# 提取市场指数成分股权重

import pandas as pd
from basic import db_vulpes
from basic import get_tradedate

def get_indexweight(datetime: str, mktcode: str = '000985.CSI'):
    """
    提取市场指数成分股权重
    
    Parameters
    ----------
    datetime : str - 'yyyymmdd' 查询日期, 数据库仅能提供每个月底数据
    mktcode : str - ['000985.CSI'] 市场指数代码

    Returns
    -------
    DataFrame
        index : TRADEDATE 交易日期 'yyyymmdd'
        columns : [TRADEDATE, INDEX_CODE, STOCK_CODE, WEIGHT]
    """
    
    conn = db_vulpes()
    timepoint = get_tradedate(enddate = datetime, frq = 'M')[-1]

    sql = '''
    SELECT 
        TRADEDATE,
        INDEX_CODE,
        STOCK_CODE,
        WEIGHT
    FROM 
        INDEX_CONSWEIGHT 
    WHERE 
        INDEX_CODE = '{}' AND
        TRADEDATE = '{}'
    ORDER BY WEIGHT DESC;
    '''.format(mktcode, timepoint)
    
    df_weight = pd.read_sql(sql, con = conn).drop_duplicates()

    return df_weight

if __name__ == '__main__':
    pass