# -*- coding: utf-8 -*-
# 下载申万一级行业成分股调入调出记录

import pandas as pd

def download_index_swmember(conn_ts):
    """
    下载申万一级行业成分股调入调出记录

    Parameters
    ----------
    conn_ts : tushare数据接口

    Returns
    -------
    DataFrame
    """
    df_info = conn_ts.index_classify(level = 'L1', src = 'SW2021', fields = ['index_code', 'industry_name'])
    ls_indexcode = df_info['index_code'].tolist()

    ls_data = []
    for code in ls_indexcode:
        try:
            df = conn_ts.index_member(index_code = code)
            ls_data.append(df.copy())
        except:
            raise Exception('[Tushare]: Industry member data download failed.')
    
    ls_data = [i for i in ls_data if i.shape[0] > 0]
    if len(ls_data) == 0:
        df_indexmember = pd.concat(ls_data, ignore_index = True).drop_duplicates().drop(columns = ['is_new']).merge(df_info, on = 'index_code', how = 'left')
        df_indexmember.columns = [i.upper() for i in df_indexmember.columns]
        df_indexmember.rename({'CON_CODE': 'STOCK_CODE', 'INDUSTRY_NAME': 'INDEX_NAME'}, axis = 1, inplace = True)
    else:
        df_indexmember = pd.DataFrame()
        
    return df_indexmember

if __name__ == '__main__':
    pass