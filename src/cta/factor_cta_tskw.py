# -*- coding: utf-8 -*-
# 计算时序偏度因子持仓权重

import numpy as np  

def factor_cta_tskw(df_mktdata, value, df_qualified, n_skewness, trg_value):
    '''
    计算时序偏度因子持仓权重

    Parameters
    ----------
    df_mktdata : DataFrame - 日频行情数据
    value : str - 收益率测算目标
    df_qualified : DataFrame - 可以交易品种数据
    n_skewness : int - 偏度测算时间[日]
    trg_value : float - 偏度判定阈值

    Returns
    -------
    DataFrame
    '''

    df_setret = df_mktdata.query('MTYPE == 1').pivot(index = 'TRADEDATE', columns = 'STDCODE', values = value)
    df_skew = -(df_setret.rolling(n_skewness).skew())

    df_signal = ((df_skew * df_qualified) > trg_value) * 1
    df_signal -= ((-df_skew * df_qualified) > trg_value) * 1
    df_signal.fillna(0, inplace = True)

    se_holdnum = df_signal.apply(np.abs).sum(axis = 1)
    df_holdpct = df_signal.divide(se_holdnum, axis = 0)

    return df_holdpct

if __name__ == '__main__':
    pass