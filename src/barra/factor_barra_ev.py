# -*- coding: utf-8 -*-

import pandas as pd
from barra import mod_makeupann

def factor_barra_ev(df_yreport, ls_tdate):
    """
    计算Earnings Variability类因子，含VSAL, VERN, VFLO

    Computed by dividing the standard deviation of the annual 
    sales/earnings/cashflows of the last five fiscal years by the average annual sales/earnings/cashflows.

    Parameters
    ----------
        df_yreport : DataFrame - 年度财务指标表数据
        ls_tdate : List [str] - 交易日序列

    Returns
    -------
    DataFrame
    
    """
    df_TOR = df_yreport.pivot(index = 'REPORT_DATE', columns = 'STOCK_CODE', values = 'TOR')
    df_NI = df_yreport.pivot(index = 'REPORT_DATE', columns = 'STOCK_CODE', values = 'NI')
    df_NCE = df_yreport.pivot(index = 'REPORT_DATE', columns = 'STOCK_CODE', values = 'NCE')
    df_ANN = df_yreport.pivot(index = 'REPORT_DATE', columns = 'STOCK_CODE', values = 'DISCLOSE_DATE')

    df_VSAL = df_TOR.rolling(5, min_periods = 1).std() / df_TOR.rolling(5, min_periods = 1).mean()
    df_VERN = df_NI.rolling(5, min_periods = 1).std() / df_NI.rolling(5, min_periods = 1).mean()
    df_VFLO = df_NCE.rolling(5, min_periods = 1).std() / df_NCE.rolling(5, min_periods = 1).mean()

    df_dVSAL = mod_makeupann(df_VSAL, df_ANN, ls_tdate).stack().reset_index()
    df_dVERN = mod_makeupann(df_VERN, df_ANN, ls_tdate).stack().reset_index()
    df_dVFLO = mod_makeupann(df_VFLO, df_ANN, ls_tdate).stack().reset_index()

    df_dVSAL.columns = ['TRADEDATE', 'STOCK_CODE', 'VALUE']
    df_dVERN.columns = ['TRADEDATE', 'STOCK_CODE', 'VALUE']
    df_dVFLO.columns = ['TRADEDATE', 'STOCK_CODE', 'VALUE']

    df_dVSAL['FACTOR'] = 'VSAL'
    df_dVERN['FACTOR'] = 'VERN'
    df_dVFLO['FACTOR'] = 'VFLO'

    df_output = pd.concat([df_dVSAL, df_dVERN, df_dVFLO], ignore_index = True)

    return df_output

if __name__ == '__main__':
    pass