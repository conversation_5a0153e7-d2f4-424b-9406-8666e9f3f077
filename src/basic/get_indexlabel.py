# -*- coding: utf-8 -*-
# 提取市场截面宽基指数成分标签

import pandas as pd
from basic import db_vulpes
from basic import get_tradedate

def get_indexlabel(datetime: str):
    """
    提取市场截面宽基指数成分标签, 分为沪深300, 中证500, 中证1000, 中证2000
    
    Parameters
    ----------
    datetime : str - 'yyyymmdd' 查询日期, 数据库仅能提供每个月底数据

    Returns
    -------
    DataFrame
        index : TRADEDATE 交易日期 'yyyymmdd'
        columns : [TRADEDATE, STOCK_CODE, INDEX_CODE, INDEX_NAME]
    """
    
    conn = db_vulpes()
    timepoint = get_tradedate(enddate = datetime, frq = 'M')[-1]

    sql = '''
    SELECT 
        TRADEDATE,
        STOCK_CODE,
        INDEX_CODE,
        INDEX_CODE AS INDEX_NAME
    FROM 
        INDEX_CONSWEIGHT 
    WHERE 
        INDEX_CODE IN ('000300.SH', '000905.SH', '000852.SH', '932000.CSI') AND
        TRADEDATE = '{}';
    '''.format(timepoint)
    
    df_label = pd.read_sql(sql, con = conn).drop_duplicates()
    df_label['INDEX_NAME'] = df_label['INDEX_NAME'].replace({'000300.SH': '沪深300', '000905.SH': '中证500', '000852.SH': '中证1000', '932000.CSI': '中证2000'})

    return df_label

if __name__ == '__main__':
    pass