# -*- coding: utf-8 -*-
# 提取可交易商品期货品种

import pandas as pd
from basic import db_vulpes

def get_cfut_qualified():
    '''
    提取可交易商品期货品种

    <<可交易商品期货判定规则>>
    1. 该品种已经上市超过1年
    2. 该品种每日成交额连续20个交易日大于1亿元
    3. 该品种每日成交量连续20个交易日大于1000手

    Parameters
    ----------

    Returns
    -------
    DataFrame
    '''

    conn = db_vulpes()
    sql = '''
    SELECT A.*, B.LISTDATE
    FROM(
    SELECT
        TRADEDATE,
        STDCODE,
        COUNT(FUT_CODE) AS FUTNUM,
        SUM(VOL) AS VOL,
        SUM(AMOUNT) AS AMOUNT
    FROM
        CFUTURE_DAILYMARKET
    GROUP BY (TRADEDATE, STDCODE)) A
    LEFT JOIN(
    SELECT 
        STDCODE,
        MIN(LISTDATE) AS LISTDATE
    FROM 
        CFUTURE_DAILYMARKET
    GROUP BY 
        STDCODE) B
    ON A.STDCODE = B.STDCODE
    ORDER BY (TRADEDATE, STDCODE)
    '''
    df_info = pd.read_sql(sql, con = conn)
    df_info['STARTDATE'] = (df_info['LISTDATE'].astype(int) + 10000).astype(str)
    df_info['ACTIVE'] = df_info['TRADEDATE'] > df_info['STARTDATE']

    # 连续20个交易日成交量大于1000手
    df_volnum = (df_info.pivot(index = 'TRADEDATE', columns = 'STDCODE', values = 'VOL') > 1000).rolling(20).sum() == 20
    # 连续20个交易日成交额大于1亿元
    df_amtnum = (df_info.pivot(index = 'TRADEDATE', columns = 'STDCODE', values = 'AMOUNT') > 10000).rolling(20).sum() == 20
    # 品种发布至少1年
    df_active = df_info.pivot(index = 'TRADEDATE', columns = 'STDCODE', values = 'ACTIVE')

    df_qualified = ((df_volnum * df_amtnum * df_active).replace({0: None}) * 1).dropna(how = 'all')

    return df_qualified

if __name__ == '__main__':
    pass