#!/usr/bin/env python
# -*- encoding:utf-8 -*-

from gevent import pywsgi
from flask import request
from flask import Flask
from xml.dom.minidom import parseString
from threading import Thread
import time
from WXBizMsgCrypt import WXBizMsgCrypt

from wework_react import *

#对应接受消息回调模式中的token, EncodingAESKey 和 企业信息中的企业id
qy_api = [WXBizMsgCrypt('w7ffqhXbWN', 'HtUxDx3sEOhd4aAq6HUIUn8mIMI9UBpn7CmVQ7bJ3vk', 'ww703b0c00ff6ce26e'),]

app = Flask(__name__)
@app.route('/hook_path', methods = ['GET','POST']) 
def hook():
    if request.method == 'GET':
        echo_str = signature_get(request, 0)
        return(echo_str)
    elif request.method == 'POST':
        echo_str = signature_post(request, 0)
        return(echo_str)

# 开启消息接受模式时验证接口连通性
def signature_get(request, i): 
    msg_signature = request.args.get('msg_signature', '')
    timestamp = request.args.get('timestamp', '')
    nonce = request.args.get('nonce', '')
    echo_str = request.args.get('echostr', '')
    ret,sEchoStr=qy_api[i].VerifyURL(msg_signature, timestamp, nonce, echo_str)
    if (ret != 0):
        print('ERR: VerifyURL ret: ' + str(ret))
        return('FAILED')
    else:
        return(sEchoStr)

# 实际接受消息
def signature_post(request, i):
    msg_signature = request.args.get('msg_signature', '')
    timestamp = request.args.get('timestamp', '')
    nonce = request.args.get('nonce', '')
    data = request.data.decode('utf-8')
    ret,sMsg=qy_api[i].DecryptMsg(data,msg_signature, timestamp,nonce)
    if (ret != 0):
        print('ERR: DecryptMsg ret: ' + str(ret))
        return('FAILED')
    else:
        # with open ('/var/log/qywx.log', 'a+') as f: # 消息接收日志
        with open ('/root/zerda/log/vulpesecho.log', 'a+') as f: # 消息接收日志
            doc = parseString(sMsg)
            collection = doc.documentElement
            name_xml = collection.getElementsByTagName('FromUserName')
            msg_xml = collection.getElementsByTagName('Content')
            type_xml = collection.getElementsByTagName('MsgType')
            pic_xml = collection.getElementsByTagName('PicUrl')
            msg = ''
            name = ''
            msg_type = type_xml[0].childNodes[0].data
            if msg_type == 'text': #文本消息
                name = name_xml[0].childNodes[0].data        #发送者id
                msg = msg_xml[0].childNodes[0].data          #发送的消息内容
                f.write(time.strftime('[%Y-%m-%d %H:%M:%S]') + '[ch%d] %s:%s\n' % (i, name, msg))
                t = Thread(target = wework_react, args = (msg, name))
                t.start()
                
            elif msg_type == 'image': #图片消息
                name = name_xml[0].childNodes[0].data
                pic_url = pic_xml[0].childNodes[0].data
                f.write(time.strftime('[%Y-%m-%d %H:%M:%S]') + '[ch%d] %s:图片消息 %s\n' % (i, name, pic_url))

            f.close()

        return('OK')

if __name__ == '__main__':
    server = pywsgi.WSGIServer(('0.0.0.0', 8808), app)
    server.serve_forever()
    app.run()

    # 本地监听端口,可自定义
    # app.run('0.0.0.0', 8808)  