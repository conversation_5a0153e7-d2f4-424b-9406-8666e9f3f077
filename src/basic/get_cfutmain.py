# -*- coding: utf-8 -*-
# 提取商品期货主力合约

import pandas as pd
from basic import db_vulpes

def get_cfutmain(code: list = [], submain: bool = False):
    '''
    提取商品期货主力合约

    Parameters
    ----------
    code : list - 合约代码列表, 默认为空提取所有合约
    submain : Boolean [False] 是否提取次主力合约

    Returns
    -------
    DataFrame
    '''
    mtype = 1 if not submain else 2

    conn = db_vulpes()
    ls_code = pd.read_sql('SELECT DISTINCT STDCODE FROM CFUTURE_INFO', con = conn)['STDCODE'].tolist()
    if len(code) == 0:
        set_code = tuple(ls_code)
    elif type(code) is str:
        set_code = (code, )
    else:
        set_code = tuple(code)

    sql = '''
    SELECT
        FUT_CODE,
        TRADEDATE,
        STDCODE,
        UNIT
    FROM 
        CFUTURE_MAINCODE A
    LEFT JOIN
        CFUTURE_INFO B
    ON A.STDCODE = B.STDCODE
    WHERE 
        MTYPE = {} AND
        STDCODE IN {}
    ORDER BY (TRADEDATE, FUT_CODE)
    '''.format(mtype, set_code)
    df_cfutmain = pd.read_sql(sql, con = conn)
    
    return df_cfutmain

if __name__ == '__main__':
    pass