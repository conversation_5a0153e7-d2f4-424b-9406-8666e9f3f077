# -*- coding: utf-8 -*-
# 提取计算期指CTA因子所需要的行情数据

import pandas as pd
from basic import db_vulpes, get_ffut<PERSON>in

def get_ffut_mktdata(startdate = ''):
    '''
    提取计算期指CTA因子所需要的行情数据

    Parameters
    ----------
    startdate : str - 行情起始日期
    
    Returns
    -------
    DataFrame
    '''
    ls_stdcode = ['IH', 'IF', 'IC', 'IM']
    conn = db_vulpes()
    sql = '''
    SELECT 
        FUT_CODE,
        TRADEDATE,
        STDCODE,
        PRE_CLOSE,
        PRE_SETTLE,
        OPEN,
        CLOSE,
        SETTLE,
        AMOUNT,
        VOL,
        OI,
        DLISTDATE,
        MTYPE 
    FROM 
        FFUTURE_DAILYMARKET
    WHERE
        TRADEDATE >= '{}' AND
        STDCODE IN {} AND
        MTYPE < 3
    '''.format(startdate, tuple(ls_stdcode))

    df_mktdata = pd.read_sql(sql, con = conn)
    df_mktdata['RETURN'] = df_mktdata['CLOSE'] / df_mktdata['PRE_SETTLE'] - 1
    df_mktdata['SETRET'] = df_mktdata['SETTLE'] / df_mktdata['PRE_SETTLE'] - 1
    df_mktdata['CLSRET'] = df_mktdata['CLOSE'] / df_mktdata['PRE_CLOSE'] - 1
    df_mktdata['STORET'] = df_mktdata['SETTLE'] / df_mktdata['OPEN'] - 1
    df_mktdata['CTORET'] = df_mktdata['CLOSE'] / df_mktdata['OPEN'] - 1

    df_main = get_ffutmain()
    df_mktdata = df_mktdata.merge(df_main, on = ['TRADEDATE', 'STDCODE', 'FUT_CODE'], how = 'inner')

    return df_mktdata

if __name__ == '__main__':
    pass