# -*- coding: utf-8 -*-

from barra import mod_makeupann

def factor_barra_gpm(df_yreport, ls_tdate):
    """
    计算GPM因子

    Computed as, GPM = (Sales - COGS) / Sales, where Sales and COGS 
    are the sales and cost of goods sold, respectively, from the last fiscal year.
    
    Parameters
    ----------
        df_yreport : DataFrame - 年度财务指标表数据
        ls_tdate : List [str] - 交易日序列

    Returns
    -------
    DataFrame
    
    """
    df_yreport['GPM'] = (df_yreport['TOR'] - df_yreport['TOC']) / df_yreport['TOR']
    df_GPM = df_yreport.pivot(index = 'REPORT_DATE', columns = 'STOCK_CODE', values = 'GPM')
    df_ANN = df_yreport.pivot(index = 'REPORT_DATE', columns = 'STOCK_CODE', values = 'DISCLOSE_DATE')

    df_output = mod_makeupann(df_GPM, df_ANN, ls_tdate).stack().reset_index()
    df_output.columns = ['TRADEDATE', 'STOCK_CODE', 'VALUE']

    df_output['FACTOR'] = 'GPM'

    return df_output

if __name__ == '__main__':
    pass