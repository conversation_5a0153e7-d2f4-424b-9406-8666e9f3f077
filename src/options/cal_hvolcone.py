# -*- coding: utf-8 -*-
# 计算期权标的资产历史波动率锥

import pandas as pd
import numpy as np
from basic.db_conn import db_vulpes

def cal_hvolcone(enddate, ls_pct = [1, 0.9, 0.75, 0.5, 0.25, 0.1, 0], wl = 750):
    '''
    计算期权标的资产历史波动率锥

    Parameters
    ----------
    ls_pct : list - 用于计算波动率锥的百分位
    wl : int - 用于计算波动率的收益率跨度
    
    Returns
    -------
    DataFrame
    '''

    conn = db_vulpes()

    # 计算波动率锥
    sql = '''
    SELECT INDEX_CODE AS TARGET_CODE, TRADEDATE, RETURN 
    FROM INDEX_DAILYMARKET 
    WHERE INDEX_CODE IN (SELECT DISTINCT TARGET_CODE FROM FOPTION_DAILYMARKET) AND TRADEDATE <= '{}'
    UNION ALL
    SELECT FUND_CODE AS TARGET_CODE, TRADEDATE, RETURN 
    FROM FOPTION_ETFMARKET 
    WHERE FUND_CODE IN (SELECT DISTINCT TARGET_CODE FROM FOPTION_DAILYMARKET) AND TRADEDATE <= '{}'
    '''.format(enddate, enddate)
    df_tsret = pd.read_sql(sql, con = conn).pivot(index = 'TRADEDATE', columns = 'TARGET_CODE', values = 'RETURN').dropna().tail(wl)

    ls_data = []
    for t in range(5, 305, 5):
        df_quantile = (df_tsret.rolling(t).std() * np.sqrt(252)).quantile(ls_pct)
        df_data = df_quantile.stack().reset_index().rename(columns={'level_0': 'QUANTILE', 0: 'HVOL'})
        df_data['TERM'] = t
        ls_data.append(df_data)
    df_output = pd.concat(ls_data)

    return df_output

if __name__ == '__main__':
    pass