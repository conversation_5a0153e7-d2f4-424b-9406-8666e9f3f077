# -*- coding: utf-8 -*-

from openpyxl import load_workbook
from sqlalchemy import create_engine
import warnings
warnings.filterwarnings('ignore')

import pandas as pd
import numpy as np
from scipy.optimize import fsolve
import pyexcel
# pyexcel pyexcel-xls pyexcel-xlsx
import pdfplumber

import imaplib
imaplib.Commands['ID'] = ('AUTH')
import zipfile
# 需要源代码decode("cp437") 改为 GBK
from fnmatch import fnmatch
from email import message_from_bytes
from email.header import decode_header
import re
import os
import time

import dataframe_image as dfi
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

from basic import get_tradedate, db_vulpes

class Portfolio():
    def __init__(self):
        '''
        构造函数
        '''
        # 邮箱信息
        self.username = 'getfundnav'
        self.password = 'SXIKPMCGRABJXRIT'
        self.imap_url = 'imap.163.com'
        self.inbox = 'inbox'
        # 业绩基准数据库
        self.conn = db_vulpes()
        # 本地文件路径
        self.path = os.path.dirname(os.path.realpath(__file__))
        self.path_rawfolder = os.path.join(self.path, 'rawfile')
        if not os.path.exists(self.path_rawfolder):
            os.makedirs(self.path_rawfolder)
        self.path_reportfolder = os.path.join(self.path, 'reportfile')
        if not os.path.exists(self.path_reportfolder):
            os.makedirs(self.path_reportfolder)
        # 非必要文件
        self.path_record = os.path.join(self.path, 'rawfile_record.xlsx')
        self.path_rawnav = os.path.join(self.path, 'fund_rawnav.xlsx')
        # 必要文件
        self.path_format = os.path.join(self.path, 'fund_format.xlsx')
        self.path_holdaction = os.path.join(self.path, 'fund_action.xlsx')
        self.path_fundfee = os.path.join(self.path, 'fund_fee.xlsx')

    def decode_info(self, s, init = 0, mailaddr = False):
        '''
        解码函数
        '''
        try:
            if mailaddr:
                r_mail = r'[a-z0-9\.\-+_]+@[a-z0-9\.\-+_]+\.[a-z]+'
                value = None
                for info in decode_header(s):
                    if info[1]:
                        ls_match = re.findall(r_mail, info[0].decode(info[1]))
                    else:
                        try:
                            ls_match = re.findall(r_mail, info[0].decode())
                        except:
                            ls_match = re.findall(r_mail, info[0])
                    
                    if len(ls_match) > 0:
                        return ls_match[0]
            else:
                value, charset = decode_header(s)[init]
                if charset:
                    value = value.decode(charset)
        except:
            return None
        return value

    def download_rawdata(self):
        '''
        读取邮件附件保存至本地
        '''
        # path_record
        # path_rawfolder

        try:
            df_records = pd.read_excel(self.path_record)
            search_range = 'SINCE ' + pd.to_datetime(df_records['SEND_DATE'].max()).strftime('%d-%b-%Y')
        except:
            search_range = 'ALL'

        # username = 'getfundnav'
        # password = 'SXIKPMCGRABJXRIT'
        # imap_url = 'imap.163.com'
        # inbox = 'inbox'

        emailserver = imaplib.IMAP4_SSL(self.imap_url)  # 邮箱服务器地址
        emailserver.login(self.username, self.password)
        args = ("name", self.username, "version", "1.0.0", "vendor", "myclient")
        typ, dat = emailserver._simple_command('ID', '("' + '" "'.join(args) + '")')
        emailserver.select(self.inbox)
        _, message_ids = emailserver.search(None, search_range)

        ls_attchment= []
        ls_black = ['*北京信弘天禾资产管理中心*净值表*', '*正定成长CTA3号*净值表*']

        for message_id in message_ids[0].split():
            # 获取邮件内容
            _, data = emailserver.fetch(message_id, '(RFC822)')
            raw_email = data[0][1]
            email_message = message_from_bytes(raw_email)
            subject = self.decode_info(email_message.get('SUBJECT'))
            from_addr = self.decode_info(email_message.get('FROM'), mailaddr = True)  
            ls_time = email_message.get('DATE').split(',')[-1].split(' ')
            while '' in ls_time: ls_time.remove('') 
            date = pd.to_datetime(' '.join(ls_time[0: 3])).date().strftime('%Y-%m-%d')

            isblack = False
            for form_black in ls_black:
                if fnmatch(subject, form_black):
                    isblack = True

            # 遍历所有附件
            for part in email_message.walk():
                if part.get_content_maintype() == 'multipart':
                    continue
                if part.get('Content-Disposition') is None:
                    continue
                if not part.get_filename():
                    continue

                # 保存附件到本地
                filename = self.decode_info(part.get_filename())
                extension = filename.split('.')[-1].lower()

                if filename:
                    if extension in ['xlsx', 'xls', 'zip', 'pdf']:
                        if not os.path.exists(self.path_rawfolder):
                            os.makedirs(self.path_rawfolder)
                        
                        if fnmatch(filename, '*\ufffd*') | isblack:
                            rename = 1
                            name = subject + '.' + extension
                            filepath = os.path.join(self.path_rawfolder, name)
                            with open(filepath, 'wb') as f:
                                f.write(part.get_payload(decode = True))      
                        else:
                            rename = 0
                            name = filename
                            filepath = os.path.join(self.path_rawfolder, name)
                            with open(filepath, 'wb') as f:
                                f.write(part.get_payload(decode = True))

                        if extension == 'zip':
                            zf = zipfile.ZipFile(filepath)
                            zf.extractall(path = self.path_rawfolder)
                            zf.close()
                        
                        ls_attchment.append([subject, from_addr, date, name, rename])

        # 关闭连接
        emailserver.logout()
        df_records_add = pd.DataFrame(ls_attchment, columns = ['MAIL_SUBJECT', 'MAIL_FROM', 'SEND_DATE', 'FILE_NAME', 'RENAME'])

        try:
            df_records = pd.concat([pd.read_excel(self.path_record), df_records_add], ignore_index = True)
        except:
            df_records = df_records_add.copy()
            
        df_records.to_excel(self.path_record, index = False)
        return

    def get_rawnav(self):
        '''
        将本地文件数据提取整合
        '''
        # path_rawfolder
        # path_format
        # path_rawnav
        ls_filename = os.listdir(self.path_rawfolder)
        for name in ls_filename:
            filemt = time.localtime(os.stat(os.path.join(self.path_rawfolder, name)).st_mtime)
            if time.strftime('%Y%m%d', filemt) < time.strftime('%Y%m%d', time.localtime()):
                continue

            if name.split('.')[-1].lower() == 'xls':
                pyexcel.save_book_as(file_name = os.path.join(self.path_rawfolder, name), dest_file_name = os.path.join(self.path_rawfolder, name + 'x'))

            if (name.split('.')[-1].lower() == 'pdf') & (fnmatch(name.split('.')[0], '资产净值公告_SABN40_千象股票中性2号*')):
                pdf_file = pdfplumber.open(os.path.join(self.path_rawfolder, name))
                table = pdf_file.pages[0].extract_table()
                table[0][0] = table[0][0].split(',')[0]
                table[0][1] = table[0][0].split('截至')[-1]
                df_table = pd.DataFrame(table)
                df_table.to_excel(os.path.join(self.path_rawfolder, name.split('.')[0] + '.xlsx'), index = False)
                
            if (name.split('.')[-1].lower() == 'pdf') & (fnmatch(name.split('.')[0], 'SAGS00_慕途稳健3号私募证券投资基金产品规模数据*')):
                pdf_file = pdfplumber.open(os.path.join(self.path_rawfolder, name))
                table = pdf_file.pages[0].extract_table()
                df_table = pd.DataFrame(table[1], index = table[0])
                df_table.to_excel(os.path.join(self.path_rawfolder, name.split('.')[0] + '.xlsx'))

        df_mailformat = pd.read_excel(self.path_format)
        ls_fileformat = df_mailformat['附件格式'].to_list()
        ls_all = []

        ls_filename = os.listdir(self.path_rawfolder)
        for filter in ls_fileformat:
            ls_match = []
            for name in ls_filename:
                if fnmatch(name, filter):
                    ls_match.append(name)
            
            if len(ls_match) > 0:
                fval_code = df_mailformat[df_mailformat['附件格式'] == filter]['产品代码'].values[0]
                fval_name = df_mailformat[df_mailformat['附件格式'] == filter]['产品名称'].values[0]
                fval_iszh = df_mailformat[df_mailformat['附件格式'] == filter]['专户'].values[0]
                
                fref_date = df_mailformat[df_mailformat['附件格式'] == filter]['日期'].values[0]
                fref_nav = df_mailformat[df_mailformat['附件格式'] == filter]['单位净值'].values[0]
                fref_cumnav = df_mailformat[df_mailformat['附件格式'] == filter]['累计单位净值'].values[0]
                
                fref_tshare = df_mailformat[df_mailformat['附件格式'] == filter]['产品份额'].values[0]
                fref_tequity = df_mailformat[df_mailformat['附件格式'] == filter]['产品净值'].values[0]
                fref_hshare = df_mailformat[df_mailformat['附件格式'] == filter]['持有份额'].values[0]
                fref_hequity = df_mailformat[df_mailformat['附件格式'] == filter]['持有净值'].values[0]

                ls_tmp = []
                for fname in ls_match:
                    wb = load_workbook(os.path.join(self.path_rawfolder, fname))
                    ws = wb.active

                    # 特别处理泰铼
                    if (fval_name in ['泰铼联泰2号', '泰铼开泰中性策略1号']) & (ws['D1'].value == '协会备案代码'):
                        fref_date = 'A2'
                        fref_nav = 'E2'
                        fref_cumnav = 'F2'
                    # 特别处理致远22号
                    elif (fval_name == '致远22号A') & (ws['B2'].value == '致远22号私募投资基金A'):
                        fref_date = 'A2'
                        fref_nav = 'C2'
                        fref_cumnav = 'D2'
                        fref_tshare = 'E3'
                        fref_tequity = 'F3'        
                    # 特别处理平凡浩睿
                    elif (fval_name == '平凡浩睿') & (ws['A10'].value == '基金份额净值：'):
                        fref_nav = 'B10'
                        fref_cumnav = 'B11'
                    # 特别处理白鹭
                    elif (fval_name == '白鹭鼓浪屿量化多策略十号') & (ws['D6'].value == '单位净值'):
                        fref_nav = 'D7'
                        fref_cumnav = 'E7'
                    else:
                        fref_date = df_mailformat[df_mailformat['附件格式'] == filter]['日期'].values[0]
                        fref_nav = df_mailformat[df_mailformat['附件格式'] == filter]['单位净值'].values[0]
                        fref_cumnav = df_mailformat[df_mailformat['附件格式'] == filter]['累计单位净值'].values[0]
                        
                        fref_tshare = df_mailformat[df_mailformat['附件格式'] == filter]['产品份额'].values[0]
                        fref_tequity = df_mailformat[df_mailformat['附件格式'] == filter]['产品净值'].values[0]
                        fref_hshare = df_mailformat[df_mailformat['附件格式'] == filter]['持有份额'].values[0]
                        fref_hequity = df_mailformat[df_mailformat['附件格式'] == filter]['持有净值'].values[0]     

                    fval_date = str(ws[fref_date].value).replace('年', '').replace('月', '').replace('日', '')
                    fval_nav = np.nan if pd.isna(ws[fref_nav].value) else ws[fref_nav].value
                    fval_cumnav = np.nan if pd.isna(ws[fref_cumnav].value) else ws[fref_cumnav].value

                    fval_tshare = np.nan if pd.isna(fref_tshare) else ws[fref_tshare].value
                    fval_tequity = np.nan if pd.isna(fref_tequity) else ws[fref_tequity].value
                    fval_hshare = np.nan if pd.isna(fref_hshare) else ws[fref_hshare].value
                    fval_hequity = np.nan if pd.isna(fref_hequity) else ws[fref_hequity].value

                    fval_tshare = np.nan if pd.isna(fval_tshare) else fval_tshare
                    fval_tequity = np.nan if pd.isna(fval_tequity) else fval_tequity
                    fval_hshare = np.nan if pd.isna(fval_hshare) else fval_hshare
                    fval_hequity = np.nan if pd.isna(fval_hequity) else fval_hequity

                    df_tmp = pd.DataFrame({'产品代码': str(fval_code),
                                        '产品名称': str(fval_name),
                                        '专户': int(fval_iszh),
                                        '日期' : pd.to_datetime(fval_date if type(fval_date) is str else fval_date),
                                        '单位净值': float(fval_nav.replace(',', '') if type(fval_nav) is str else fval_nav),
                                        '累计单位净值': float(fval_cumnav.replace(',', '') if type(fval_cumnav) is str else fval_cumnav),
                                        '产品份额': float(fval_tshare.replace(',', '') if type(fval_tshare) is str else fval_tshare),
                                        '产品净值': float(fval_tequity.replace(',', '') if type(fval_tequity) is str else fval_tequity),
                                        '持有份额': float(fval_hshare.replace(',', '') if type(fval_hshare) is str else fval_hshare),
                                        '持有净值': float(fval_hequity.replace(',', '') if type(fval_hequity) is str else fval_hequity),
                                        }, index = [0])
                    
                    # 特别处理信弘明珠一号A
                    if fval_name == '信弘明珠一号A':
                        df_tmp = pd.read_excel(os.path.join(self.path_rawfolder, fname), header = 2, thousands = ',')
                        df_tmp.columns = ['日期', '产品代码', '产品名称', '产品净值', '产品份额', '单位净值', '累计单位净值']
                        df_tmp['日期'] = pd.to_datetime(df_tmp['日期'].str.extract(r'\D*(\d+)\D*(\d+)\D*(\d+)').values.sum(axis = 1))
                        df_tmp['产品名称'] = str(fval_name)
                        df_tmp['专户'] = int(fval_iszh)
                        df_tmp['持有份额'] = float(fval_hshare.replace(',', '') if type(fval_hshare) is str else fval_hshare)
                        df_tmp['持有净值'] = float(fval_hequity.replace(',', '') if type(fval_hequity) is str else fval_hequity)

                    ls_tmp.append(df_tmp.copy())
                    wb.close()

                ls_all.append(pd.concat(ls_tmp, ignore_index = True).drop_duplicates().sort_values('日期'))

        df_all = pd.concat(ls_all, ignore_index = True).drop_duplicates()
        df_all['日期'] = df_all['日期'].dt.date
        df_all.sort_values(['产品代码', '日期'], inplace = True)
        df_all.to_excel(self.path_rawnav, index = False)

        return 

    def cal_holdstatus(self, todate: str = '', current: bool = False, savepng: bool = False):
        '''
        基于换仓信息返回当前持仓状态
        '''
        if todate == '':
            todate = pd.Timestamp.today().strftime('%Y%m%d')

        # 净值记录
        df_rawnav = pd.read_excel(self.path_rawnav)
        df_rawnav = df_rawnav.groupby(['产品代码', '产品名称', '专户', '日期']).max().reset_index()
        df_rawnav = df_rawnav[~df_rawnav['单位净值'].isna()].copy()
        df_rawnav['日期'] = pd.to_datetime(df_rawnav['日期']).dt.date
        # 申赎记录
        df_action = pd.read_excel(self.path_holdaction)
        df_action['日期'] = pd.to_datetime(df_action['日期']).dt.date

        df_rawnav = df_rawnav[df_rawnav['日期'] <= pd.to_datetime(todate).date()].copy()
        df_action = df_action[df_action['日期'] <= pd.to_datetime(todate).date()].copy()

        ls_actcode = df_action['产品代码'].unique().tolist()
        
        # 基准与费率记录
        df_fee = pd.read_excel(self.path_fundfee)
        ls_benchmark = df_fee['业绩基准'].dropna().unique().tolist()
        sql = '''
        SELECT
            INDEX_CODE,
            TRADEDATE,
            CLOSE
        FROM
            INDEX_DAILYMARKET
        WHERE
            INDEX_CODE IN {}
        '''.format(tuple(ls_benchmark))
        df_benchmark = pd.read_sql(sql, con = self.conn)
        df_benchmark = df_benchmark.rename({'TRADEDATE': '日期', 'CLOSE': '业绩基准'}, axis = 1)
        df_benchmark['日期'] = pd.to_datetime(df_benchmark['日期']).dt.date

        ls_fundnav = []
        ls_fundirr = []

        for code in ls_actcode:
            # 净值初始化
            df_act = df_action[df_action['产品代码'] == code].sort_values('日期').reset_index(drop = True).copy()
            init_date = df_act[df_act['动作'] == '申购']['日期'].min()
            init_name = df_act.loc[df_act['日期'] == init_date, '产品名称'].tolist()[0]
            init_cumnav = df_act.loc[df_act['日期'] == init_date, '累计单位净值'].tolist()[0]
            init_share = df_act.loc[df_act['日期'] == init_date, '份额调整'].tolist()[0]
            init_money = df_act.loc[df_act['日期'] == init_date, '金额调整'].tolist()[0]
            init_nav = round(init_money / init_share, 4)
            df_fundnav = df_rawnav[(df_rawnav['产品代码'] == code) & (df_rawnav['日期'] >= init_date)].sort_values('日期').reset_index(drop = True).copy()

            if init_date not in df_fundnav['日期'].tolist():
                df_fundnav = pd.concat([df_fundnav.head(1), df_fundnav], ignore_index = True)
                df_fundnav.loc[0, '产品代码'] = code
                df_fundnav.loc[0, '产品名称'] = init_name
                df_fundnav.loc[0, '日期'] = init_date
                df_fundnav.loc[0, '单位净值'] = init_nav
                df_fundnav.loc[0, '累计单位净值'] = init_cumnav

            df_fundnav['单位累计分红'] = (df_fundnav['累计单位净值'] - df_fundnav['单位净值']).round(8)
            df_fundnav['单位分红'] = df_fundnav['单位累计分红'].diff().round(8).fillna(0)
            df_fundnav['复权单位净值'] = 0
            df_fundnav['收益率'] = ((df_fundnav['累计单位净值'] - df_fundnav['单位累计分红'].shift(1)) / df_fundnav['单位净值'].shift(1) - 1).fillna(0)
            df_fundnav['累计收益率'] = (df_fundnav['收益率'] + 1).cumprod()
            df_fundnav['最大回撤'] = df_fundnav['累计收益率'] / df_fundnav['累计收益率'].cummax() - 1
            df_fundnav['复权单位净值'] = df_fundnav['累计收益率'] * df_fundnav['单位净值'].tolist()[0]

            # 添加业绩基准
            benchmark = df_fee.loc[df_fee['产品代码'] == code]['业绩基准'].values[0]
            if pd.isna(benchmark):
                df_fundnav['业绩基准'] = 1
            else:
                df_fundnav = df_fundnav.merge(df_benchmark[df_benchmark['INDEX_CODE'] == benchmark][['日期', '业绩基准']].copy(), on = '日期', how = 'left')
                df_fundnav['业绩基准'] = df_fundnav['业绩基准'].ffill()
                df_fundnav['业绩基准'] /= df_fundnav['业绩基准'].values[0]
            df_fundnav['累计超额收益率'] = df_fundnav['累计收益率'] / df_fundnav['业绩基准']
            df_fundnav['超额收益率'] = df_fundnav['累计超额收益率'].pct_change(fill_method = None).fillna(0)
            
            # 申购点初始化
            df_fundnav['持仓份额'] = init_share
            df_fundnav['累计投入'] = init_money
            df_fundnav['持仓净值'] = df_fundnav['持仓份额'] * df_fundnav['单位净值']
            df_fundnav['动作'] = None
            df_fundnav['份额调整'] = None
            df_fundnav['实现盈亏'] = None
            df_fundnav['未实现盈亏'] = None
            df_fundnav.loc[df_fundnav['日期'] == init_date, '动作'] = '申购'
            df_fundnav.loc[df_fundnav['日期'] == init_date, '份额调整'] = init_share

            cum_share = init_share
            cum_invest = init_money

            # 潜在分红
            df_der = df_fundnav[df_fundnav['单位累计分红'].diff().round(2) > 0].copy()

            # 份额调整
            if df_act.shape[0] == 1:
                pass
            else:
                for i in range(1, df_act.shape[0]):
                    delta_share = df_act.loc[i, '份额调整']
                    delta_money = df_act.loc[i, '金额调整']
                    delta_cumnav = df_act.loc[i, '累计单位净值']
                    delta_date = df_act.loc[i, '日期']
                    delta_act = df_act.loc[i, '动作']

                    if delta_date not in df_fundnav['日期'].tolist():                
                        df_fundnav = pd.concat([df_fundnav.copy(), df_fundnav.tail(1).copy()], ignore_index = True)
                        df_fundnav.loc[df_fundnav.shape[0] - 1, '日期'] = delta_date
                        df_fundnav.loc[df_fundnav.shape[0] - 1, '累计单位净值'] = delta_cumnav
                        df_fundnav.loc[df_fundnav.shape[0] - 1, '单位净值'] = delta_cumnav - df_fundnav.loc[df_fundnav.shape[0] - 1, '单位累计分红']
                        df_fundnav.sort_values('日期', inplace = True)
                        df_fundnav.reset_index(drop = True, inplace = True)
                
                    if delta_act == '申购':
                        cum_invest += delta_money
                    elif delta_act == '赎回':
                        delta_invest = delta_share * (cum_invest / cum_share)
                        cum_invest += delta_invest
                        delta_realize = delta_invest - delta_money

                        df_fundnav.loc[df_fundnav['日期'] == delta_date, '实现盈亏'] = delta_realize
                    elif delta_act == '分红再投':
                        df_tmp = df_der[df_der['日期'] <= delta_date].tail(1).copy()
                        tmp_days = (delta_date - df_tmp['日期'].tolist()[0]).days
                        if tmp_days < 5:
                            delta_date = df_tmp['日期'].tolist()[0]
                        else:
                            print('分红再投资日期错误！')
                    elif delta_act == '现金分红':
                        df_tmp = df_der[df_der['日期'] <= delta_date].tail(1).copy()
                        tmp_days = (delta_date - df_tmp['日期'].tolist()[0]).days
                        if tmp_days < 5:
                            delta_date = df_tmp['日期'].tolist()[0]
                        else:
                            print('分红再投资日期错误！')
                        delta_realize = 0 - delta_money
                        df_fundnav.loc[df_fundnav['日期'] == delta_date, '实现盈亏'] = delta_realize

                    cum_share += delta_share
                    df_fundnav.loc[df_fundnav['日期'] == delta_date, '动作'] = delta_act
                    df_fundnav.loc[df_fundnav['日期'] == delta_date, '份额调整'] = delta_share
                    df_fundnav.loc[df_fundnav['日期'] >= delta_date, '持仓份额'] = round(cum_share, 2)
                    df_fundnav.loc[df_fundnav['日期'] >= delta_date, '累计投入'] = round(cum_invest, 2)

                    if (i == (df_act.shape[0] - 1)) & (delta_act == '赎回') & (round(cum_share, 2) == 0):
                        df_fundnav = df_fundnav[df_fundnav['日期'] <= delta_date].copy()

            df_fundnav['实现盈亏'].fillna(0, inplace = True)
            df_fundnav['持仓净值'] = df_fundnav['持仓份额'] * df_fundnav['单位净值']
            df_fundnav['累计已实现盈亏'] = df_fundnav['实现盈亏'].cumsum()
            df_fundnav['累计未实现盈亏'] = df_fundnav['持仓净值'] - df_fundnav['累计投入']
            df_fundnav['未实现盈亏'] = df_fundnav['持仓份额'] * df_fundnav['单位净值'].shift(1).fillna(0) * df_fundnav['收益率']

            # 资金成本
            capital_cost = 0.047
            df_fundnav['资金成本'] = df_fundnav['日期'].diff(1).dt.days.fillna(0) / 365 * df_fundnav['累计投入'].shift(1).fillna(0) * capital_cost 
            df_fundnav['累计资金成本'] = df_fundnav['资金成本'].cumsum()

            # 费率调整
            split_type = df_fee.loc[df_fee['产品代码'] == code]['赎回类型'].values[0]
            fee = df_fee.loc[df_fee['产品代码'] == code]['后端'].values[0]
            ls_fee = [i.split(',') for i in fee.split(';') if i != '']
            df_charge = pd.DataFrame(ls_fee, columns = ['bot', 'top', 'charge'], dtype = 'float').sort_values('bot').reset_index(drop = True)

            df_fundact = df_fundnav[['产品代码', '日期', '产品名称', '单位净值', '复权单位净值', '累计收益率', '份额调整', '持仓份额', '动作']].dropna().copy()
            df_holdsplit = pd.DataFrame(0, index = df_fundact['日期'].tolist(), columns = df_fundact['日期'].tolist())

            for i in range(df_fundact.shape[0]):
                split_date = df_fundact.iloc[i]['日期']
                split_act = df_fundact.iloc[i]['动作']
                split_share = df_fundact.iloc[i]['份额调整']
                ls_precol = [i for i in df_holdsplit.columns if i < split_date]
                if split_type == '后进先出':
                    ls_precol.reverse()

                if i == 0:
                    df_holdsplit.loc[split_date, split_date] = split_share
                    continue
                else:
                    split_predate = df_fundact.iloc[i - 1]['日期']
                    df_holdsplit.loc[split_date, ls_precol] = df_holdsplit.loc[split_predate, ls_precol].fillna(0)

                if split_act == '申购':
                    df_holdsplit.loc[split_date, split_date] = split_share

                if split_act in ['分红再投', '业报提取']:
                    df_holdsplit.loc[split_date, split_date] = split_share + df_holdsplit.loc[split_date, ls_precol].sum()
                    df_holdsplit.loc[split_date, ls_precol] = 0
                
                if split_act == '赎回':
                    if split_type != '平衡进出':
                        for d in ls_precol:
                            df_holdsplit.loc[split_date, d] = max(0, df_holdsplit.loc[split_date, d] + split_share)
                            split_share = min(0, df_holdsplit.loc[split_predate, d] + split_share)
                    else:
                        df_holdsplit.loc[split_date, ls_precol] += df_holdsplit.loc[split_date, ls_precol].div(df_holdsplit.loc[split_date, ls_precol].sum(), axis = 0) * split_share

            df_fundnav['累计未实现后端'] = 0
            
            for part_start in df_holdsplit.index:
                df_fundnav.loc[df_fundnav['日期'] >= part_start, '累计未实现后端'] = 0
                for hold_start in df_holdsplit.columns:
                    if hold_start > part_start:
                        break
                    else:
                        hold_share = df_holdsplit.loc[part_start][hold_start]
                    
                    if hold_share > 0:
                        df_fundnav_part = df_fundnav.loc[df_fundnav['日期'] >= hold_start, ['日期', '单位净值', '复权单位净值']].reset_index(drop = True).copy()
                    else:
                        continue
                    
                    df_fundnav_part['区间天数'] = (df_fundnav_part['日期'] - df_fundnav_part['日期'].values[0]).astype('timedelta64[s]').dt.days
                    df_fundnav_part['年化收益率'] = (df_fundnav_part['复权单位净值'] / df_fundnav_part['复权单位净值'].values[0] - 1) / df_fundnav_part['区间天数'] * 365
                    df_fundnav_part['区间收益率'] = df_fundnav_part['单位净值'] / df_fundnav_part['单位净值'].values[0] - 1
                    df_fundnav_part['后端'] = 0
                    
                    for i in df_charge.values:
                        df_fundnav_part[i[2]] = 0
                        df_fundnav_part.loc[df_fundnav_part['年化收益率'] >= i[0], i[2]] = df_fundnav_part.loc[df_fundnav_part['年化收益率'] >= i[0], '区间收益率'] * df_fundnav_part['单位净值'].values[0] * hold_share * i[2]
                        df_fundnav_part.loc[df_fundnav_part['年化收益率'] >= i[1], i[2]] = df_fundnav_part.loc[df_fundnav_part['年化收益率'] >= i[1], '区间天数'] * i[1] / 365 * df_fundnav_part['单位净值'].values[0] * hold_share * i[2]
                        # 区间高水位提取
                        df_fundnav_part.loc[df_fundnav_part['区间收益率'] <= 0, i[2]] = 0
                        df_fundnav_part['后端'] += df_fundnav_part[i[2]]

                    df_fundnav.loc[df_fundnav['日期'] >= part_start, '累计未实现后端'] += df_fundnav_part.loc[df_fundnav_part['日期'] >= part_start, '后端'].round(4).values
            
            df_fundnav['累计费前总盈亏'] = df_fundnav['累计已实现盈亏'] + df_fundnav['累计未实现盈亏']
            df_fundnav['累计费后总盈亏'] = df_fundnav['累计已实现盈亏'] + df_fundnav['累计未实现盈亏'] - df_fundnav['累计未实现后端']
            df_fundnav['费前总盈亏'] = df_fundnav['累计费前总盈亏'].diff()
            df_fundnav['费后总盈亏'] = df_fundnav['累计费后总盈亏'].diff()
            ls_fundnav.append(df_fundnav.copy())

            df_cf = df_fundnav[df_fundnav['动作'].isin(['申购', '赎回', '现金分红']) | (df_fundnav['日期'] == df_fundnav['日期'].max())][['日期', '动作', '单位净值', '单位分红', '持仓份额', '份额调整', '累计投入', '实现盈亏', '累计未实现后端']].drop_duplicates()
            df_cf['动作'].fillna('结算', inplace = True)
            df_cf.loc[df_cf['动作'] == '结算', '份额调整'] = -df_cf.loc[df_cf['动作'] == '结算', '持仓份额']
            df_cf['投入调整'] = df_cf['累计投入'].diff()
            df_cf['费前现金流'] = df_cf['单位净值'] * df_cf['份额调整']
            df_cf['费后现金流'] = df_cf['单位净值'] * df_cf['份额调整']

            df_cf.loc[df_cf['动作'] == '现金分红', '费前现金流'] = -df_cf.loc[df_cf['动作'] == '现金分红', '持仓份额'] * df_cf.loc[df_cf['动作'] == '现金分红', '单位分红']
            df_cf.loc[df_cf['动作'] == '现金分红', '费后现金流'] = -df_cf.loc[df_cf['动作'] == '现金分红', '实现盈亏']

            df_cf.loc[df_cf['动作'] == '赎回', '费后现金流'] = df_cf.loc[df_cf['动作'] == '赎回', '投入调整'] - df_cf.loc[df_cf['动作'] == '赎回', '实现盈亏']
            df_cf.loc[df_cf['动作'] == '结算', '费后现金流'] = df_cf.loc[df_cf['动作'] == '结算', '费后现金流'] + df_cf.loc[df_cf['动作'] == '结算', '累计未实现后端']
            df_cf['时间流'] = (pd.to_datetime(df_cf['日期']).diff().dt.days / 365).fillna(0)
            df_cf['时间流'] = df_cf['时间流'].cumsum()

            irr_bfee = irr(cfs = df_cf['费前现金流'], yrs = df_cf['时间流'], x0 = 0.1)
            irr_afee = irr(cfs = df_cf['费后现金流'], yrs = df_cf['时间流'], x0 = 0.1)

            ls_fundirr.append(pd.DataFrame({'产品代码': [code], '费前年化收益率': [irr_bfee], '费后年化收益率': [irr_afee]}))

        df_fundnav_all = pd.concat(ls_fundnav, ignore_index = True)
        df_fundirr_all = pd.concat(ls_fundirr, ignore_index = True)
        
        df_fundnav_all['持仓占比'] = 1
        df_fundnav_all['份额占比'] = df_fundnav_all['持仓份额'] / df_fundnav_all['产品份额']
        df_fundnav_all['净值占比'] = df_fundnav_all['持仓净值'] / df_fundnav_all['产品净值']
        df_fundnav_all['持仓占比'] = df_fundnav_all[['份额占比', '净值占比']].max(axis = 1).values
        
        df_fundnav_all.loc[df_fundnav_all['专户'] == 1, '持仓占比'] = 1
        df_fundnav_all['持仓占比'] = df_fundnav_all['持仓占比'].fillna(0)

        tp_5td = get_tradedate(enddate = df_fundnav_all['日期'].max().strftime('%Y%m%d'), form = 'datetime')[-5].date()
        tp_20td = get_tradedate(enddate = df_fundnav_all['日期'].max().strftime('%Y%m%d'), form = 'datetime')[-20].date()
        tp_ytd = pd.to_datetime(df_fundnav_all['日期'].max().strftime('%Y') + '0101').date()
        
        df_5td = df_fundnav_all[(df_fundnav_all['日期'] >= tp_5td)][['产品代码', '收益率']]
        df_20td = df_fundnav_all[(df_fundnav_all['日期'] >= tp_20td)][['产品代码', '收益率']]
        df_ytd = df_fundnav_all[(df_fundnav_all['日期'] >= tp_ytd)][['产品代码', '收益率', '费后总盈亏']]

        df_5td['收益率'] += 1
        df_20td['收益率'] += 1
        df_ytd['收益率'] += 1
        df_5tdret = (df_5td.groupby('产品代码').prod() - 1).rename({'收益率': '周收益率'}, axis = 1).reset_index()
        df_20tdret = (df_20td.groupby('产品代码').prod() - 1).rename({'收益率': '月收益率'}, axis = 1).reset_index()
        df_ytdret = (df_ytd[['产品代码', '收益率']].groupby('产品代码').prod() - 1).rename({'收益率': '今年收益率'}, axis = 1).reset_index()
        df_ytdafr = (df_ytd[['产品代码', '费后总盈亏']].groupby('产品代码').sum()).rename({'费后总盈亏': '今年费后收益'}, axis = 1).reset_index()

        df_start = df_fundnav_all.groupby('产品代码').head(1)[['日期', '产品代码']].rename({'日期': '起投日期'}, axis = 1)
        df_current = df_fundnav_all.groupby('产品代码').tail(1).merge(df_start, on = '产品代码', how = 'left')[[
            '日期', 
            '产品代码', 
            '产品名称', 
            '起投日期',
            '单位净值', 
            '累计单位净值', 
            '持仓份额', 
            '持仓净值', 
            '持有净值', 
            '产品份额', 
            '产品净值', 
            '累计投入',
            ]].sort_values('累计投入', ascending = False).merge(df_fundirr_all, on = '产品代码', how = 'left')
        
        df_status = df_fundnav_all.groupby('产品代码').tail(1).merge(df_start, on = '产品代码', how = 'left')[[
            '日期', 
            '产品代码',
            '专户',
            '产品名称',
            '起投日期', 
            '收益率',
            '单位净值', 
            '累计单位净值',
            '复权单位净值',
            '产品份额',
            '产品净值',
            '持仓占比',
            '持仓份额', 
            '持仓净值', 
            '累计投入',
            '累计收益率',
            '累计超额收益率',
            '最大回撤',
            '累计已实现盈亏',
            '累计未实现盈亏',
            '累计未实现后端',
            '累计费前总盈亏',
            '累计费后总盈亏',
            '累计资金成本']].sort_values('累计投入', ascending = False).merge(df_fundirr_all, on = '产品代码', how = 'left')\
        .merge(df_5tdret, on = '产品代码', how = 'left')\
        .merge(df_20tdret, on = '产品代码', how = 'left')\
        .merge(df_ytdret, on = '产品代码', how = 'left')\
        .merge(df_ytdafr, on = '产品代码', how = 'left')

        # 投资组合汇总
        dic_columns = df_status.set_index('产品代码')[['产品名称']].to_dict()['产品名称']
        df_holdinv = df_fundnav_all.pivot(index = '日期', columns = '产品代码', values = '累计投入')
        df_holdnav = df_fundnav_all.pivot(index = '日期', columns = '产品代码', values = '持仓净值')
        df_rprofit = df_fundnav_all.pivot(index = '日期', columns = '产品代码', values = '实现盈亏')
        df_uprofit = df_fundnav_all.pivot(index = '日期', columns = '产品代码', values = '未实现盈亏')
        df_bfprofit = df_fundnav_all.pivot(index = '日期', columns = '产品代码', values = '费前总盈亏')
        df_afprofit = df_fundnav_all.pivot(index = '日期', columns = '产品代码', values = '费后总盈亏')
        df_left = pd.DataFrame(index = pd.date_range(start = df_holdnav.index[0], end = df_holdnav.index[-1]))
        ls_tdate = get_tradedate(startdate = pd.to_datetime(df_holdnav.index[0]).strftime('%Y%m%d'), enddate = pd.to_datetime(df_holdnav.index[-1]).strftime('%Y%m%d'))
        # 组合净值与明细
        df_pf_holdnav = df_left.join(df_holdnav, how = 'left').ffill().loc[ls_tdate].rename(dic_columns, axis = 1)
        df_pf_holdnav['组合总净值'] = df_pf_holdnav.sum(axis = 1)
        df_pf_holdnav = df_pf_holdnav[['组合总净值'] + list(dic_columns.values())]
        # 组合投入与明细
        df_pf_holdinv = df_left.join(df_holdinv, how = 'left').ffill().loc[ls_tdate].rename(dic_columns, axis = 1)
        df_pf_holdinv['组合总投入'] = df_pf_holdinv.sum(axis = 1)
        df_pf_holdinv = df_pf_holdinv[['组合总投入'] + list(dic_columns.values())]
        # 组合实现盈亏与明细
        df_pf_rprofit = df_left.join(df_rprofit, how = 'left').join(pd.DataFrame(index = pd.to_datetime(ls_tdate), data = {'日期': pd.to_datetime(ls_tdate)}), how = 'left').rename(dic_columns, axis = 1)
        df_pf_rprofit['日期'].bfill(inplace = True)
        df_pf_rprofit = df_pf_rprofit.groupby('日期').sum()
        df_pf_rprofit['实现盈亏'] = df_pf_rprofit.sum(axis = 1)
        df_pf_rprofit['累计实现盈亏'] = df_pf_rprofit['实现盈亏'].cumsum()
        df_pf_rprofit = df_pf_rprofit[['累计实现盈亏', '实现盈亏'] + list(dic_columns.values())]
        # 组合未实现盈亏与明细
        df_pf_uprofit = df_left.join(df_uprofit, how = 'left').join(pd.DataFrame(index = pd.to_datetime(ls_tdate), data = {'日期': pd.to_datetime(ls_tdate)}), how = 'left').rename(dic_columns, axis = 1)
        df_pf_uprofit['日期'].bfill(inplace = True)
        df_pf_uprofit = df_pf_uprofit.groupby('日期').sum()
        df_pf_uprofit['未实现盈亏'] = df_pf_uprofit.sum(axis = 1)
        df_pf_uprofit['累计未实现盈亏'] = df_pf_uprofit['未实现盈亏'].cumsum()
        df_pf_uprofit = df_pf_uprofit[['累计未实现盈亏', '未实现盈亏'] + list(dic_columns.values())]
        # 组合费前总盈亏与明细
        df_pf_bfprofit = df_left.join(df_bfprofit, how = 'left').join(pd.DataFrame(index = pd.to_datetime(ls_tdate), data = {'日期': pd.to_datetime(ls_tdate)}), how = 'left').rename(dic_columns, axis = 1)
        df_pf_bfprofit['日期'].bfill(inplace = True)
        df_pf_bfprofit = df_pf_bfprofit.groupby('日期').sum()
        df_pf_bfprofit['费前总盈亏'] = df_pf_bfprofit.sum(axis = 1)
        df_pf_bfprofit['累计费前总盈亏'] = df_pf_bfprofit['费前总盈亏'].cumsum()
        df_pf_bfprofit = df_pf_bfprofit[['累计费前总盈亏', '费前总盈亏'] + list(dic_columns.values())]
        # 组合费后总盈亏与明细
        df_pf_afprofit = df_left.join(df_afprofit, how = 'left').join(pd.DataFrame(index = pd.to_datetime(ls_tdate), data = {'日期': pd.to_datetime(ls_tdate)}), how = 'left').rename(dic_columns, axis = 1)
        df_pf_afprofit['日期'].bfill(inplace = True)
        df_pf_afprofit = df_pf_afprofit.groupby('日期').sum()
        df_pf_afprofit['费后总盈亏'] = df_pf_afprofit.sum(axis = 1)
        df_pf_afprofit['累计费后总盈亏'] = df_pf_afprofit['费后总盈亏'].cumsum()
        df_pf_afprofit = df_pf_afprofit[['累计费后总盈亏', '费后总盈亏'] + list(dic_columns.values())]

        df_protfolio = pd.DataFrame(index = pd.to_datetime(ls_tdate))
        df_protfolio['组合费前单位净值'] = 1
        df_protfolio['组合费后单位净值'] = 1
        df_protfolio['组合费前收益率'] = 0
        df_protfolio['组合费后收益率'] = 0

        df_protfolio['组合总净值'] = df_pf_holdnav['组合总净值']
        df_protfolio['组合总投入'] = df_pf_holdinv['组合总投入']
        df_protfolio['实现盈亏'] = df_pf_rprofit['实现盈亏']
        df_protfolio['未实现盈亏'] = df_pf_uprofit['未实现盈亏']

        df_protfolio['费前总盈亏'] = df_pf_bfprofit['费前总盈亏']
        df_protfolio['费后总盈亏'] = df_pf_afprofit['费后总盈亏']

        df_protfolio['累计实现盈亏'] = df_pf_rprofit['累计实现盈亏']
        df_protfolio['累计未实现盈亏'] = df_pf_uprofit['累计未实现盈亏']
        df_protfolio['累计未实现后端'] = (df_pf_bfprofit['累计费前总盈亏'] - df_pf_afprofit['累计费后总盈亏'])

        df_protfolio['累计费前总盈亏'] = df_pf_bfprofit['累计费前总盈亏']
        df_protfolio['累计费后总盈亏'] = df_pf_afprofit['累计费后总盈亏']

        df_protfolio['组合费前收益率'] = (df_protfolio['费前总盈亏'] / df_protfolio['组合总净值'].shift(1)).fillna(0)
        df_protfolio['组合费前单位净值'] = (df_protfolio['组合费前收益率'] + 1).cumprod()
        df_protfolio['组合费后收益率'] = (df_protfolio['费后总盈亏'] / df_protfolio['组合总净值'].shift(1)).fillna(0)
        df_protfolio['组合费后单位净值'] = (df_protfolio['组合费后收益率'] + 1).cumprod()

        df_protfolio = df_protfolio.reset_index().rename({'index': '日期'}, axis = 1)
        df_protfolio['日期'] = pd.to_datetime(df_protfolio['日期']).dt.date

        capital_cost = 0.047
        df_protfolio['资金成本'] = df_protfolio['日期'].diff(1).dt.days.fillna(0) / 365 * df_protfolio['组合总投入'].shift(1).fillna(0) * capital_cost 
        df_protfolio['累计资金成本'] = df_protfolio['资金成本'].cumsum()
        
        # 周报
        df_cumdata = df_protfolio.set_index('日期')
        df_cumdata.index.name = None
        df_cumdata.index = pd.to_datetime(df_cumdata.index)
        df_cumnav = df_cumdata['组合费后单位净值'].to_frame(name = '投资组合').join(df_fundnav_all.pivot(index = '日期', columns = '产品代码', values = '复权单位净值').rename(dic_columns, axis = 1), how = 'left')
        df_cumret = df_cumdata['组合费后单位净值'].to_frame(name = '投资组合').join(df_fundnav_all.pivot(index = '日期', columns = '产品代码', values = '累计收益率').rename(dic_columns, axis = 1), how = 'left')
        df_cumpft = df_cumdata['累计费后总盈亏'].to_frame(name = '投资组合').join(df_fundnav_all.pivot(index = '日期', columns = '产品代码', values = '累计费后总盈亏').rename(dic_columns, axis = 1), how = 'left')

        ls_wdate = get_tradedate(frq = 'W', form = 'datetime')

        df_cumwnav = df_cumnav[df_cumnav.index.isin(ls_wdate)]
        df_cumwret = df_cumret[df_cumret.index.isin(ls_wdate)]
        df_cumwpft = df_cumpft[df_cumpft.index.isin(ls_wdate)]

        ly_point = df_cumwret[df_cumwret.index.year == df_cumwret.index.year.max() - 1].index[-1]
        tw_point = pd.to_datetime(df_cumwret.index[-1]).date()

        ls_twhold = df_action[df_action['日期'] <= tw_point][['产品代码', '份额调整']].groupby('产品代码').sum().round(2).query('份额调整 > 0').index.tolist()
        df_tw = df_fundnav_all[(df_fundnav_all['日期'] <= tw_point) & (df_fundnav_all['产品代码'].isin(ls_twhold))].groupby('产品代码').tail(1).sort_values('累计投入', ascending = False)

        df_wrep = pd.DataFrame()
        df_wrep['起投日期'] = df_cumnav.stack(dropna = True).reset_index().groupby('level_1').min()['level_0'].dt.date
        df_wrep['复权单位净值'] = df_cumwnav.iloc[-1]
        df_wrep['周涨跌幅'] = df_cumwret.iloc[-1] / df_cumwret.bfill().iloc[-2] - 1
        df_wrep['月涨跌幅'] = df_cumwret.iloc[-1] / df_cumwret.bfill().iloc[-5] - 1
        df_wrep['今年涨跌幅'] = df_cumwret.iloc[-1] / df_cumret.bfill().ffill().loc[ly_point] - 1
        df_wrep['累计涨跌幅'] = df_cumwret.iloc[-1] - 1
        df_wrep['最大周回撤'] = (df_cumwret / df_cumwret.cummax() - 1).min() * (df_cumwret.iloc[-1] * 0 + 1)
        df_wrep['周度盈亏(万)'] = (df_cumwpft.iloc[-1] - df_cumwpft.fillna(0).iloc[-2]) / 10000
        df_wrep['月度盈亏(万)'] = (df_cumwpft.iloc[-1] - df_cumwpft.fillna(0).iloc[-5]) / 10000
        df_wrep['今年盈亏(万)'] = (df_cumwpft.iloc[-1] - df_cumwpft.fillna(0).loc[ly_point]) / 10000
        df_wrep['累计盈亏(万)'] = df_cumwpft.iloc[-1] / 10000

        df_wrep = df_wrep.loc[['投资组合'] + df_tw['产品名称'].tolist()]

        df_tojoin = df_tw.set_index('产品名称')[['持仓净值', '累计投入', '日期']].copy()
        df_tojoin[['持仓净值', '累计投入']] /= 10000
        df_tojoin.columns = ['持仓净值(万)', '累计投入(万)', '净值日期']
        df_tojoin.loc['投资组合', ['持仓净值(万)', '累计投入(万)']] = df_tojoin[['持仓净值(万)', '累计投入(万)']].sum()
        df_tojoin.loc['投资组合', '净值日期'] = pd.to_datetime(df_tojoin['净值日期']).max().date()

        df_wrep = df_wrep.join(df_tojoin, how = 'left')
        df_wrep.index.name = None
        df_wrep.columns.name = df_cumwret.index[-1].date()

        # 存储
        ls_holdcode = df_action[['产品代码', '份额调整']].groupby('产品代码').sum().round(2).query('份额调整 > 0').index.tolist()

        path_report = os.path.join(self.path_reportfolder,'台账汇总_{}.xlsx'.format(str(df_current['日期'].max())))
        writer = pd.ExcelWriter(path = path_report)

        df_current = df_current[df_current['产品代码'].isin(ls_holdcode)].reset_index(drop = True).copy()
        df_status.loc[~df_status['产品代码'].isin(ls_holdcode), ['累计未实现盈亏', '累计未实现后端', '累计费前总盈亏', '累计费后总盈亏']] = 0

        if current:
            df_current.to_excel(excel_writer = writer, sheet_name = '台账汇总', index = False)
        df_status.to_excel(excel_writer = writer, sheet_name = '汇总', index = False)
        
        df_wrep.to_excel(excel_writer = writer, sheet_name = '组合周报')

        df_protfolio.to_excel(excel_writer = writer, sheet_name = '投资组合', index = False)

        for df_tosave in ls_fundnav:
            name = df_tosave['产品名称'].tolist()[-1]
            df_tosave.drop(columns = ['产品份额', '产品净值', '持有份额', '持有净值']).to_excel(excel_writer = writer, sheet_name = name, index = False)
        writer.close()

        if savepng:
            df_png = df_fundnav_all.groupby('产品代码').tail(1).merge(df_start, on = '产品代码', how = 'left')[[
            '日期',
            '产品名称',
            '产品代码',
            '收益率', 
            '超额收益率',
            '累计收益率', 
            '累计超额收益率', 
            '单位净值', 
            '持仓份额', 
            '持仓净值', 
            '累计投入',
            '累计未实现盈亏',
            '起投日期',
            '持仓占比'
            ]]
            df_png = df_png[df_png['产品代码'].isin(ls_holdcode)].copy()
            df_png.set_index('产品名称', inplace = True)
            df_png.columns.name = df_png.index.name
            df_png.index.name = None
            df_png[['累计收益率', '累计超额收益率']] -= 1
            df_png[['持仓份额', '持仓净值', '累计投入', '累计未实现盈亏']] /= 10000

            df_png.drop(columns = ['产品代码'], inplace = True)
            df_png.sort_values(by = ['累计投入'], ascending = False, inplace = True)
            df_style = df_png.style.format({
                '收益率': '{:.2%}', 
                '超额收益率': '{:.2%}', 
                '累计收益率': '{:.2%}', 
                '累计超额收益率': '{:.2%}', 
                '单位净值': '{:.4f}', 
                '持仓份额': '{:.2f}', 
                '持仓净值': '{:.2f}', 
                '累计投入': '{:.2f}',
                '累计未实现盈亏': '{:.2f}',
                '持仓占比': '{:.2%}'
            }).applymap(lambda x: 'color: green' if x < 0 else 'color: red', subset = ['收益率', '超额收益率', '累计未实现盈亏'])\
            .applymap(lambda x: 'color: orange' if x < df_png['日期'].max() else '', subset = ['日期'])\
            .applymap(lambda x: 'background-color: yellow' if abs(x) > 0.01 else '', subset = ['收益率'])\
            .applymap(lambda x: 'background-color: yellow' if (x > 0.1) & (x != 1) else '', subset = ['持仓占比'])
            dfi.export(df_style, os.path.join(self.path_reportfolder, 'report.png'), table_conversion = 'matplotlib')

            df_style = df_wrep.style.format({
                '复权单位净值': '{:.4f}', 
                '周涨跌幅': '{:.2%}', 
                '月涨跌幅': '{:.2%}', 
                '今年涨跌幅': '{:.2%}',
                '累计涨跌幅': '{:.2%}', 
                '最大周回撤': '{:.2%}', 
                '周度盈亏(万)': '{:.2f}', 
                '周度盈亏(万)': '{:.2f}', 
                '月度盈亏(万)': '{:.2f}', 
                '今年盈亏(万)': '{:.2f}', 
                '累计盈亏(万)': '{:.2f}', 
                '持仓净值(万)': '{:.2f}', 
                '累计投入(万)': '{:.2f}', }, na_rep = '-')\
            .applymap(lambda x: 'color: green' if x < 0 else 'color: red', subset = ['周涨跌幅', '月涨跌幅', '今年涨跌幅', '累计涨跌幅'])\
            .applymap(lambda x: 'color: orange' if x < df_wrep['净值日期'].max() else '', subset = ['净值日期'])
            dfi.export(df_style, os.path.join(self.path_reportfolder, 'weekreport.png'), table_conversion = 'matplotlib')

        return path_report

def npv(irr, cfs, yrs):
    # DCF模型
    return np.sum(cfs / (1. + irr) ** yrs)

def irr(cfs, yrs, x0, **kwargs):
    # DCF模型逆推IRR
    return fsolve(npv, x0 = x0, args = (cfs, yrs), **kwargs)[0]
   
if __name__ == '__main__':
    pass