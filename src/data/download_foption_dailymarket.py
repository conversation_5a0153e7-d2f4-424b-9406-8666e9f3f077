# -*- coding: utf-8 -*-
# 下载金融期权日频数据

import pandas as pd
import time

def download_foption_dailymarket(startdate, enddate, conn_ts):
    """
    下载金融期权日频数据

    Parameters
    ----------
    startdate : str - 'yyyymmdd' 时间窗口起点
    enddate : str - 'yyyymmdd' 时间窗口终点
    conn_ts : tushare数据接口

    Returns
    -------
    DataFrame
    """
    # 下载日行情
    ls_fmarket = ['SSE', 'SZSE', 'CFFEX']
    ls_data = []
    ls_info = []

    for fmarket in ls_fmarket:
        df_info = conn_ts.opt_basic(exchange = fmarket, fields='ts_code, name, opt_code, exercise_price, s_month, list_date, delist_date')
        ls_info.append(df_info.copy())
        ls_fcode = df_info['ts_code'].tolist()

        ls_subdata = []
        initdate = max(df_info['list_date'].min(), startdate)
        if initdate > enddate:
            continue

        stopdate = enddate
        # 每个交易所有3次断点机会
        max_sleep = 3
        num_sleep = 0
        while initdate < stopdate:
            try:
                df_data = conn_ts.opt_daily(start_date = initdate, end_date = stopdate, exchange = fmarket)
                stopdate = df_data['trade_date'].min()
                ls_subdata.append(df_data[(df_data['trade_date'] > stopdate) & (df_data['ts_code'].isin(ls_fcode))].copy())
            except:
                if num_sleep < max_sleep:
                    time.sleep(1)
                    num_sleep += 1
                    print('[Tushare]: Finance options market data download tried {}.'.format(num_sleep))
                    continue
                else:
                    raise Exception('[Tushare]: Finance options market data download failed.')

        if initdate == stopdate:
            try:
                df_data = conn_ts.opt_daily(trade_date = initdate, exchange = fmarket)
                ls_subdata.append(df_data[df_data['ts_code'].isin(ls_fcode)].drop_duplicates().copy())
            except:
                raise Exception('[Tushare]: Finance options market data download failed.')
        
        ls_subdata = [i for i in ls_subdata if i.shape[0] > 0]
        if len(ls_subdata) > 0:
            ls_data.append(pd.concat(ls_subdata, ignore_index = True).drop_duplicates())
    
    ls_data = [i for i in ls_data if i.shape[0] > 0]
    ls_info = [i for i in ls_info if i.shape[0] > 0]
    if len(ls_data) > 0:
        df_foptdata = pd.concat(ls_data, ignore_index = True).drop_duplicates()
        df_foptinfo = pd.concat(ls_info, ignore_index = True).drop_duplicates()

        df_foptmkt = df_foptdata.merge(df_foptinfo, on = 'ts_code', how = 'inner')
        df_foptmkt.columns = [i.upper() for i in df_foptmkt.columns]
        df_foptmkt.rename({'TS_CODE': 'OPT_CODE',
                        'OPT_CODE': 'TARGET_CODE',
                        'NAME': 'OPT_NAME',
                        'TRADE_DATE': 'TRADEDATE',
                        'EXERCISE_PRICE': 'STRIKE_PRICE',
                        'S_MONTH': 'TMONTH',
                        'LIST_DATE': 'LISTDATE', 
                        'DELIST_DATE': 'DLISTDATE'}, axis = 1, inplace = True)
        if df_foptmkt.shape[0] > 0:
            df_foptmkt['TARGET_CODE'] = df_foptmkt['TARGET_CODE'].str.split('OP', expand = True)[1]
            df_foptmkt['OP_TIME'] = ((pd.to_datetime(df_foptmkt['DLISTDATE']) - pd.to_datetime(df_foptmkt['TRADEDATE'])).dt.days + 0.5) / 365
            df_foptmkt['FLAG'] = None
            df_foptmkt.loc[df_foptmkt['OPT_NAME'].str.contains('认沽'), 'FLAG'] = 'PUT'
            df_foptmkt.loc[df_foptmkt['OPT_NAME'].str.contains('认购'), 'FLAG'] = 'CALL'
        else:
            df_foptmkt =  pd.DataFrame()
    else:
       df_foptmkt =  pd.DataFrame()

    return df_foptmkt

if __name__ == '__main__':
    pass