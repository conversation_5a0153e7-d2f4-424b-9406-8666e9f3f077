# -*- coding: utf-8 -*-

from basic import get_tradedate
import numpy as np
import pandas as pd

def factor_barra_ston(df_dturnover, ls_tdate):
    """
    计算STON因子, 换手率的计算月数 1, 3, 12

    Computed as the log of the average percentage of shares traded monthly over the last N months.

    Parameters
    ----------
        df_dturnover : DataFrame - 日频股票换手率数据
        ls_tdate : List [str] - 交易日序列

    Returns
    -------
    DataFrame
    
    """
    # 数据提取
    ls_twindow = get_tradedate(enddate = ls_tdate[-1], n = len(ls_tdate) + 13 * 21)
    df_TURN = df_dturnover.pivot(index = 'TRADEDATE', columns = 'STOCK_CODE', values = 'TURNOVER').loc[ls_twindow].copy()

    dic_n = {1: 'M', 3: 'Q', 12: 'A'}

    ls_output = []
    for n in dic_n.keys():
        df_STON = np.log(df_TURN.fillna(0).rolling(21 * n).sum().round(6).replace(0, np.NaN) / n)
        df_tn = df_STON.loc[ls_tdate].stack().reset_index().copy()
        df_tn.columns = ['TRADEDATE', 'STOCK_CODE', 'VALUE']
        df_tn['FACTOR'] = 'STO{}'.format(dic_n[n])
        ls_output.append(df_tn)
    
    df_output = pd.concat(ls_output, ignore_index = True)

    return df_output

if __name__ == '__main__':
    pass