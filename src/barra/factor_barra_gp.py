# -*- coding: utf-8 -*-

from barra import mod_makeupann

def factor_barra_gp(df_yreport, ls_tdate):
    """
    计算GP因子

    Computed as, Computed as, GP = (Sales - COGS) / TA, 
    where Sales, COGS, and TA are the sales, cost of goods sold, 
    and total assets, respectively, from the last fiscal year.
    
    Parameters
    ----------
        df_yreport : DataFrame - 年度财务指标表数据
        ls_tdate : List [str] - 交易日序列

    Returns
    -------
    DataFrame
    
    """
    df_yreport['GP'] = (df_yreport['TOR'] - df_yreport['TOC']) / df_yreport['TA']
    df_GP = df_yreport.pivot(index = 'REPORT_DATE', columns = 'STOCK_CODE', values = 'GP')
    df_ANN = df_yreport.pivot(index = 'REPORT_DATE', columns = 'STOCK_CODE', values = 'DISCLOSE_DATE')

    df_output = mod_makeupann(df_GP, df_ANN, ls_tdate).stack().reset_index()
    df_output.columns = ['TRADEDATE', 'STOCK_CODE', 'VALUE']

    df_output['FACTOR'] = 'GP'

    return df_output

if __name__ == '__main__':
    pass