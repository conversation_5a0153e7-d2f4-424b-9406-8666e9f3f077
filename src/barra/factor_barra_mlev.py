# -*- coding: utf-8 -*-

from barra import mod_makeupann, mod_colunify

def factor_barra_mlev(df_dind, df_yreport, ls_tdate):
    """
    计算MLEV因子

    Computed as, MLEV = (ME + PE + LD) / ME, where ME is the market value of common equity
    on the last trading day, and PE and LD are the preferred equity and long-term debt,
    respectively, from the last fiscal year.
    
    Parameters
    ----------
        df_dind : DataFrame - 日频股票数据
        df_yreport : DataFrame - 年度财务指标表数据
        ls_tdate : List [str] - 交易日序列

    Returns
    -------
    DataFrame
    
    """
    df_MV = df_dind.pivot(index = 'TRADEDATE', columns = 'STOCK_CODE', values = 'MV') * 10000
    
    df_yreport['TO'] = df_yreport['TLL'].fillna(0) + df_yreport['PS'].fillna(0)
    df_TO = df_yreport.pivot(index = 'REPORT_DATE', columns = 'STOCK_CODE', values = 'TO')
    df_ANN = df_yreport.pivot(index = 'REPORT_DATE', columns = 'STOCK_CODE', values = 'DISCLOSE_DATE')

    df_dTO = mod_makeupann(df_TO, df_ANN, ls_tdate)
    df_MLEV = (df_MV + mod_colunify(df_dTO, df_MV)) / df_MV

    df_output = df_MLEV.stack().reset_index()
    df_output.columns = ['TRADEDATE', 'STOCK_CODE', 'VALUE']

    df_output['FACTOR'] = 'MLEV'

    return df_output

if __name__ == '__main__':
    pass