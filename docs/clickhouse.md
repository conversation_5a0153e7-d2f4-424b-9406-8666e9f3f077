# Clickhouse 数据库设置

1. 安装clickhouse服务器端和客户端(Debian or Ubuntu)，建议参考官方指引

```
sudo apt-get install -y apt-transport-https ca-certificates dirmngr
sudo apt-key adv --keyserver hkp://keyserver.ubuntu.com:80 --recv 8919F6BD2B48D754

echo "deb https://packages.clickhouse.com/deb stable main" | sudo tee \
    /etc/apt/sources.list.d/clickhouse.list
sudo apt-get update

sudo apt-get install -y clickhouse-server clickhouse-client

sudo service clickhouse-server start
clickhouse-client # or "clickhouse-client --password" if you've set up a password.
```

2. 配置server文件实现Dbeaver和Python的外部访问

在目录/etc/clickhouse-server/下找到config.xml和user.xml

在config.xml文件内找到如下部分，解除其注释

```
<listen_host>::</listen_host>
```

3. 对于user.xml文件主要设置数据库用户信息，可以参考范例对profile和users进行设置，建议仅对本地账户开放写入权限
