# Linux环境编译CTP至Python接口

上期技术提供的CTP官方API为C++版本，为使用方便与安全考虑，可自行编译至Python接口进行开发。目前已经存在第三方编译且可以通过pip安装的跨平台包[openctp-ctp](https://github.com/openctp/openctp "Github")，可直接安装使用以完成跨平台开发需求，在生产环境下再考虑自编译等问题，详情参考[项目说明](https://gitee.com/airinc/openctp-ctp-python "Gitee镜像")。

## 前期准备

1. 使用swig工具在Linux环境下编译C++至Python，需要系统具备g++与swig环境，Debian/Ubuntu发行版可以通过apt-get安装；
   ```bash
   // 查看g++版本确认是否已经安装
   g++ -version

   // 没有则使用apt-get安装
   sudo apt-get install g++

   // 查看swig版本确认是否已经安装
   swig -version

   // 没有则使用apt-get安装
   sudo apt-get -y install swig
   ```
2. 通过上期技术[官网](http://www.sfit.com.cn/ "上期技术官网")下载最新接口文件(看穿式监管生产版本)，并解压获得相关文件
   ```bash
   // 新建项目文件夹pyctp
   mkdir ~/pyctp
   cd ~/pyctp

   // 官方下载API压缩包并解压
   wget -O traderapi.zip http://www.sfit.com.cn/DocumentDown/api_3/5_2_2/v6.7.2_traderapi_20230913.zip
   unzip traderapi.zip
   mv *_traderapi_*/*_traderapi_linux64/*_api/*_se_linux64/* ~/pyctp/
   rm -rf *_traderapi_*

   // 解压后需要获取以下必备文件
   thostmduserapi_se.so
   thosttraderapi_se.so
   ThostFtdcUserApiStruct.h
   ThostFtdcUserApiDataType.h
   ThostFtdcTraderApi.h
   ThostFtdcMdApi.h
   error.xml
   error.dtd

   // 重命名其中的.so文件
   mv -i thostmduserapi_se.so libthostmduserapi_se.so
   mv -i thosttraderapi_se.so libthosttraderapi_se.so

   ```

## 编译流程

1. 在同一文件夹下创建两个.i文件

   ```bash
   touch thostmduserapi.i
   touch thosttraderapi.i
   ```
   **thostmduserapi.i**

   ```cpp
   %module(directors="1") thostmduserapi
   %{
   #include "ThostFtdcMdApi.h"
   #include <codecvt>
   #include <locale>
   #include <vector>
   #include <string>
   using namespace std;
   #ifdef _MSC_VER
   const static locale g_loc("zh-CN");
   #else  
   const static locale g_loc("zh_CN.GB18030");
   #endif
   %}

   %feature("director") CThostFtdcMdSpi;
   %ignore THOST_FTDC_VTC_BankBankToFuture;
   %ignore THOST_FTDC_VTC_BankFutureToBank;
   %ignore THOST_FTDC_VTC_FutureBankToFuture;
   %ignore THOST_FTDC_VTC_FutureFutureToBank;
   %ignore THOST_FTDC_FTC_BankLaunchBankToBroker;
   %ignore THOST_FTDC_FTC_BrokerLaunchBankToBroker;
   %ignore THOST_FTDC_FTC_BankLaunchBrokerToBank;
   %ignore THOST_FTDC_FTC_BrokerLaunchBrokerToBank;


   %typemap(out) char[ANY], char[] {
       const std::string &gb2312($1);
       std::vector<wchar_t> wstr(gb2312.size());
       wchar_t* wstrEnd = nullptr;
       const char* gbEnd = nullptr;
       mbstate_t state = {};
       int res = use_facet<codecvt<wchar_t, char, mbstate_t> >
           (g_loc).in(state,
               gb2312.data(), gb2312.data() + gb2312.size(), gbEnd,
               wstr.data(), wstr.data() + wstr.size(), wstrEnd);

       if (codecvt_base::ok == res)
       {
           wstring_convert<codecvt_utf8<wchar_t>> cutf8;
           std::string result = cutf8.to_bytes(wstring(wstr.data(), wstrEnd));   
           resultobj = SWIG_FromCharPtrAndSize(result.c_str(), result.size()); 
       }
       else
       {
           std::string result;
           resultobj = SWIG_FromCharPtrAndSize(result.c_str(), result.size()); 
       }
   }
   %typemap(in) char *[] {
     /* Check if is a list */
     if (PyList_Check($input)) {
       int size = PyList_Size($input);
       int i = 0;
       $1 = (char **) malloc((size+1)*sizeof(char *));
       for (i = 0; i < size; i++) {
         PyObject *o = PyList_GetItem($input, i);
         if (PyString_Check(o)) {
           $1[i] = PyString_AsString(PyList_GetItem($input, i));
         } else {
           free($1);
           PyErr_SetString(PyExc_TypeError, "list must contain strings");
           SWIG_fail;
         }
       }
       $1[i] = 0;
     } else {
       PyErr_SetString(PyExc_TypeError, "not a list");
       SWIG_fail;
     }
   }

   // This cleans up the char ** array we malloc'd before the function call
   %typemap(freearg) char ** {
     free((char *) $1);
   }
   %include "ThostFtdcUserApiDataType.h"
   %include "ThostFtdcUserApiStruct.h"
   %include "ThostFtdcMdApi.h"
   ```
   **thosttraderapi.i**

   ```cpp
   %module(directors="1") thosttraderapi 
   %{ 
   #include "ThostFtdcTraderApi.h"
   #include <codecvt>
   #include <locale>
   #include <vector>
   #include <string>
   using namespace std;
   #ifdef _MSC_VER
   const static locale g_loc("zh-CN");
   #else  
   const static locale g_loc("zh_CN.GB18030");
   #endif
   %}

   %typemap(out) char[ANY], char[] {
       const std::string &gb2312($1);
       std::vector<wchar_t> wstr(gb2312.size());
       wchar_t* wstrEnd = nullptr;
       const char* gbEnd = nullptr;
       mbstate_t state = {};
       int res = use_facet<codecvt<wchar_t, char, mbstate_t> >
           (g_loc).in(state,
               gb2312.data(), gb2312.data() + gb2312.size(), gbEnd,
               wstr.data(), wstr.data() + wstr.size(), wstrEnd);

       if (codecvt_base::ok == res)
       {
           wstring_convert<codecvt_utf8<wchar_t>> cutf8;
           std::string result = cutf8.to_bytes(wstring(wstr.data(), wstrEnd));   
           resultobj = SWIG_FromCharPtrAndSize(result.c_str(), result.size()); 
       }
       else
       {
           std::string result;
           resultobj = SWIG_FromCharPtrAndSize(result.c_str(), result.size()); 
       }
   }

   %typemap(in) char *[] {
     /* Check if is a list */
     if (PyList_Check($input)) {
       int size = PyList_Size($input);
       int i = 0;
       $1 = (char **) malloc((size+1)*sizeof(char *));
       for (i = 0; i < size; i++) {
         PyObject *o = PyList_GetItem($input, i);
         if (PyString_Check(o)) {
           $1[i] = PyString_AsString(PyList_GetItem($input, i));
         } else {
           free($1);
           PyErr_SetString(PyExc_TypeError, "list must contain strings");
           SWIG_fail;
         }
       }
       $1[i] = 0;
     } else {
       PyErr_SetString(PyExc_TypeError, "not a list");
       SWIG_fail;
     }
   }

   // This cleans up the char ** array we malloc'd before the function call
   %typemap(freearg) char ** {
     free((char *) $1);
   }
   %feature("director") CThostFtdcTraderSpi; 
   %ignore THOST_FTDC_VTC_BankBankToFuture;
   %ignore THOST_FTDC_VTC_BankFutureToBank;
   %ignore THOST_FTDC_VTC_FutureBankToFuture;
   %ignore THOST_FTDC_VTC_FutureFutureToBank;
   %ignore THOST_FTDC_FTC_BankLaunchBankToBroker;
   %ignore THOST_FTDC_FTC_BrokerLaunchBankToBroker;
   %ignore THOST_FTDC_FTC_BankLaunchBrokerToBank;
   %ignore THOST_FTDC_FTC_BrokerLaunchBrokerToBank;  
   %feature("director") CThostFtdcTraderSpi; 
   %include "ThostFtdcUserApiDataType.h"
   %include "ThostFtdcUserApiStruct.h" 
   %include "ThostFtdcTraderApi.h"
   ```
2. 分别运行swig命令生成中间文件thostmduserapi_wrap.cxx和thosttraderapi_wrap.cxx

   ```bash
   swig -threads -c++ -python thostmduserapi.i
   swig -threads -c++ -python thosttraderapi.i
   ```
3. 创建两个make文件

   ```bash
   touch make_mduserapi
   touch make_traderapi
   ```
   **make_mduserapi**
   其中第二行的python位置与版本以系统实际安装的位置为准，找到对应的include文件夹与python版本

   ```bash
   OBJS=thostmduserapi_wrap.o
   INCLUDE=-I./ -I/root/miniconda3/include/python3.11

   TARGET=_thostmduserapi.so
   CPPFLAG=-shared -fPIC
   CC=g++
   LDLIB=-L. -lthostmduserapi_se
   $(TARGET) : $(OBJS)
   	$(CC) $(CPPFLAG) $(INCLUDE) -o $(TARGET) $(OBJS) $(LDLIB)
   $(OBJS) : %.o : %.cxx
   	$(CC) -c -fPIC $(INCLUDE) $< -o $@
   clean:
   	-rm -f $(OBJS)
   	-rm -f $(TARGET)
   ```
   **make_traderapi**

   ```bash
   OBJS=thosttraderapi_wrap.o
   INCLUDE=-I./ -I/root/miniconda3/include/python3.11

   TARGET=_thosttraderapi.so
   CPPFLAG=-shared -fPIC
   CC=g++
   LDLIB=-L. -lthosttraderapi_se
   $(TARGET) : $(OBJS)
   	$(CC) $(CPPFLAG) $(INCLUDE) -o $(TARGET) $(OBJS) $(LDLIB)
   $(OBJS) : %.o : %.cxx
   	$(CC) -c -fPIC $(INCLUDE) $< -o $@
   clean:
   	-rm -f $(OBJS)
   	-rm -f $(TARGET)
   ```
4. 分别运行两次make命令，最终生成两个.so文件_thostmduserapi.so和_thosttraderapi.so

   ```bash
   make -f make_mduserapi
   make -f make_traderapi
   ```
5. 最终需要的投入使用的文件列表如下

   ```
   _thostmduserapi.so
   _thosttraderapi.so
   libthostmduserapi_se.so
   libthosttraderapi_se.so
   thostmduserapi.py
   thosttraderapi.py
   ```
