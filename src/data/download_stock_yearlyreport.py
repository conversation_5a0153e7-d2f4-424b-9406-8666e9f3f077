# -*- coding: utf-8 -*-
# 下载股票年度财报数据

import pandas as pd

def download_stock_yearlyreport(startyear, endyear, conn_ts):
    """
    下载股票年度财报数据

    Parameters
    ----------
    startyear : str - 'yyyy' 时间窗口起点
    endyear : str - 'yyyy' 时间窗口终点
    conn_ts : tushare数据接口

    Returns
    -------
    DataFrame
    """
    ls_year = [str(i) + '1231' for i in range(int(startyear), int(endyear) + 1)]

    if len(ls_year) == 0:
        return None
    
    num_offset = 5000
    max_offset = 6000

    ls_balance = []
    ls_cashflow = []
    ls_income = []
    ls_anndate = []

    for year in ls_year:
        # 资产负债表
        i = 0
        while True:
            df_download = conn_ts.balancesheet_vip(period = year, offset = str(i * num_offset), fields = 'ts_code, end_date, total_share, total_assets, total_liab, total_ncl, total_hldr_eqy_inc_min_int, money_cap, cash_reser_cb, oth_eqt_tools_p_shr', report_type = '1')
            ls_balance.append(df_download.dropna(how = 'all', axis = 1).dropna(how = 'all', axis = 0))
            if (df_download.shape[0] >= max_offset):
                i += 1
            else:
                break
        # 现金流量表
        i = 0
        while True:
            df_download = conn_ts.cashflow_vip(period = year, offset = str(i * num_offset), fields = 'ts_code, end_date, net_profit, n_cashflow_act, n_cashflow_inv_act, n_incr_cash_cash_equ, c_cash_equ_end_period', report_type = '1')
            ls_cashflow.append(df_download.dropna(how = 'all', axis = 1).dropna(how = 'all', axis = 0))
            if (df_download.shape[0] >= max_offset):
                i += 1
            else:
                break
        # 利润表
        i = 0
        while True:
            df_download = conn_ts.income_vip(period = year, offset = str(i * num_offset), fields = 'ts_code, end_date, total_revenue, total_cogs, total_profit, ebit, n_income', report_type = '1')
            ls_income.append(df_download.dropna(how = 'all', axis = 1).dropna(how = 'all', axis = 0))
            if (df_download.shape[0] >= max_offset):
                i += 1
            else:
                break
        # 财报发布时间
        i = 0
        while True:
            df_download = conn_ts.disclosure_date(end_date = year, offset = str(i * num_offset), fields = 'ts_code, end_date, actual_date')
            ls_anndate.append(df_download)
            if (df_download.shape[0] >= max_offset):
                i += 1
            else:
                break

    df_balance = pd.concat(ls_balance, ignore_index = True).drop_duplicates()
    df_cashflow = pd.concat(ls_cashflow, ignore_index = True).drop_duplicates()
    df_income = pd.concat(ls_income, ignore_index = True).drop_duplicates()
    df_anndate =  pd.concat(ls_anndate, ignore_index = True).drop_duplicates()

    df_yreport = pd.DataFrame(columns = ['ts_code', 'end_date'])
    for df in [i for i in [df_balance, df_cashflow, df_income] if i.shape[0] != 0]:
        df_yreport = df_yreport.merge(df, on = ['ts_code', 'end_date'], how = 'outer')
    df_yreport = df_yreport.merge(df_anndate, on = ['ts_code', 'end_date'], how = 'left')

    df_yreport.columns = [i.upper() for i in df_yreport.columns]
    df_yreport.rename({
        'TS_CODE': 'STOCK_CODE',
        'END_DATE': 'REPORT_DATE',
        'ACTUAL_DATE': 'DISCLOSE_DATE',
        'TOTAL_SHARE': 'TS' ,
        'MONEY_CAP': 'MC',
        'CASH_RESER_CB': 'CR',
        'TOTAL_ASSETS': 'TA',
        'TOTAL_NCL': 'TLL',
        'TOTAL_LIAB': 'TL',
        'TOTAL_HLDR_EQY_INC_MIN_INT': 'TE',
        'OTH_EQT_TOOLS_P_SHR': 'PS',
        'NET_PROFIT': 'NP',
        'N_CASHFLOW_ACT': 'NCO',
        'N_CASHFLOW_INV_ACT': 'NCI',
        'N_INCR_CASH_CASH_EQU': 'NCE',
        'C_CASH_EQU_END_PERIOD': 'ECE',
        'TOTAL_REVENUE': 'TOR',
        'TOTAL_COGS': 'TOC',
        'TOTAL_PROFIT': 'TP',
        'N_INCOME': 'NI',
        }, axis = 1, inplace = True)

    return df_yreport

if __name__ == '__main__':
    pass