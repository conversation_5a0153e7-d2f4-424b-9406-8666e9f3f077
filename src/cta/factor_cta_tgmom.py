# -*- coding: utf-8 -*-
# 计算时序加速动量因子持仓权重

import numpy as np

def factor_cta_tgmom(df_mktdata, value, df_qualified, n_momentum, n_benchmark, n_shift, trg_value = 0):
    '''
    计算时序加速动量因子持仓权重

    Parameters
    ----------
    df_mktdata : DataFrame - 日频行情数据
    value : str - 收益率测算目标
    df_qualified : DataFrame - 可以交易品种数据
    n_momentum : int - 当前动量测算时间[日]
    n_benchmark : int - 基准动量测算时间[日]
    n_shift : int - 基准相对动量的距离[日]
    trg_value : float - 相对基准触发动量的判定标准, 默认为0

    Returns
    -------
    DataFrame
    '''
    if n_shift < 0:
        raise ValueError('n_shift must be positive.')

    df_momret = (df_mktdata.query('MTYPE == 1').pivot(index = 'TRADEDATE', columns = 'STDCODE', values = value) + 1).apply(np.log).rolling(n_momentum).sum().apply(np.exp) - 1
    df_benret = (df_mktdata.query('MTYPE == 1').pivot(index = 'TRADEDATE', columns = 'STDCODE', values = value) + 1).apply(np.log).rolling(n_benchmark).sum().apply(np.exp) - 1
    df_benret = df_benret.shift(n_shift)

    df_signal = (((df_momret - df_benret) * (df_benret > 0) * df_qualified) > trg_value) * 1
    df_signal -= (((df_benret - df_momret) * (df_benret < 0) * df_qualified) > trg_value) * 1
    df_signal.fillna(0, inplace = True)

    se_holdnum = df_signal.apply(np.abs).sum(axis = 1)
    df_holdpct = df_signal.divide(se_holdnum, axis = 0)
    
    return df_holdpct

if __name__ == '__main__':
    pass