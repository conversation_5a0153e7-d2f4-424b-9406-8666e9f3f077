# -*- coding: utf-8 -*-
# 计算后复权股票收益率

def cal_stock_dailyreturn(df_stkdata):
    '''
    计算后复权股票收益率

    Parameters
    ----------
    df_stkdata : DataFrame - 股票收盘价, 复权因子数据
    Returns
    -------
    DataFrame
    '''
    # 收盘价向后复权
    df_stkdata['CLOSE'] = df_stkdata['CLOSE'] * df_stkdata['ADJ_FACTOR']
    df_close = df_stkdata.pivot(index = 'TRADEDATE', columns = 'STOCK_CODE', values = 'CLOSE').ffill()
    # 计算PRE_CLOSE
    df_preclose = df_close.shift(1).stack().to_frame(name = 'PRE_CLOSE').reset_index()
    # 计算RETURN
    df_return = df_close.pct_change(fill_method = None).stack().to_frame(name = 'RETURN').reset_index()

    df_stkreturn = df_stkdata.merge(df_preclose, on = ['TRADEDATE', 'STOCK_CODE'], how = 'left')
    df_stkreturn = df_stkreturn.merge(df_return, on = ['TRADEDATE', 'STOCK_CODE'], how = 'left')

    return df_stkreturn

if __name__ == '__main__':
    pass