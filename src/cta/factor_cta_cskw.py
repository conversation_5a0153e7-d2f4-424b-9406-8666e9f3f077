# -*- coding: utf-8 -*-
# 计算截面偏度因子持仓权重

import numpy as np  

def factor_cta_cskw(df_mktdata, value, df_qualified, n_skewness, n_hold):
    '''
    计算截面偏度因子持仓权重

    Parameters
    ----------
    df_mktdata : DataFrame - 日频行情数据
    value : str - 收益率测算目标
    df_qualified : DataFrame - 可以交易品种数据
    n_skewness : int - 偏度测算时间[日]
    n_hold : int - 多/空单边持仓合约数

    Returns
    -------
    DataFrame
    '''

    df_setret = df_mktdata.query('MTYPE == 1').pivot(index = 'TRADEDATE', columns = 'STDCODE', values = value)
    df_skew = -(df_setret.rolling(n_skewness).skew())

    df_signal = ((df_skew * df_qualified).rank(method = 'min', axis = 1, ascending = False) <= n_hold) * 1
    df_signal -= ((df_skew * df_qualified).rank(method = 'min', axis = 1, ascending = True) <= n_hold) * 1
    df_signal.fillna(0, inplace = True)

    se_holdnum = df_signal.apply(np.abs).sum(axis = 1)
    df_holdpct = df_signal.divide(se_holdnum, axis = 0)

    return df_holdpct

if __name__ == '__main__':
    pass